﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias ascmap Add-SCMap
Function Add-SCMap {
    <#
    .Synopsis
        This method will add a new map in the system attached to a specific Area

    .DESCRIPTION
        This method will create a map in Security Center attached to a specific Area and
        return all the basic properties of the map entity.  

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $area = New-SCArea -Name "Map area"
        $mapId = Add-SCMap -AreaId $area -Provider d7287da0-a7ff-405f-8166-b6baf26d066c
    
        #Exit the session
        Exit-SCSession

        This code snippet will add a map with the specific provider to the area given.

    .NOTES
        Written by Genetec Inc.

    .LINK
        Get-SCMaps
        Remove-SCMap
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)]  [alias("aid")]   $AreaId,
        [parameter(Mandatory=$false)] [alias("prov")]  $Provider,
        [parameter(Mandatory=$false)] [alias("file")]  $FileData,
	[parameter(Mandatory=$false)] [alias("ext")]   $FileExtension,
        [parameter(Mandatory=$false)] [alias("zoom")]  $ZoomLevel,
        [parameter(Mandatory=$false)] [alias("res")]   $Resolution,
        [parameter(Mandatory=$false)] [alias("scale")] $ScaleFactor
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            
            $aid = GetIdFromObject $AreaId
            $uri = "Entities/$aid/CreateMap/"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Provider", $Provider)
            $jsonObject.Add("FileData", $FileData)
			$jsonObject.Add("FileExtension", $FileExtension)
            $jsonObject.Add("ZoomLevel", $ZoomLevel)
            $jsonObject.Add("Resolution", $Resolution)
            $jsonObject.Add("ScaleFactor", $ScaleFactor)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ascmo Add-SCMapObject
Function Add-SCMapObject {
    <#
    .SYNOPSIS
        Add a new map object to the given map entity

    .DESCRIPTION
        Add a new map object to the given map entity.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The MapId parameter represents the Id of the map to retrieve (The guid representing the map in the Security Center System)
        You can also pass any map object that contains an ID as a parameter.

        The Type parameter is the Guid that represents the type of object to create :
        Macro : 57537b1d-81aa-42f9-8e27-1b9e689b37e4
        Alarm : 29fc0073-8f91-4d0a-a30f-eb3cbdac69f6
        Rectangle : dc2d9dd8-4e9f-4f84-9e3c-3a187c6fe119
        Image : 0907310e-ca60-40ad-98c1-c4b6e5d1359c
        Camera :dc2d9dd8-4e9f-4f84-9e3c-3a187c6fe1e6
        Door : dc2d9dd8-4e9f-4f84-9e3c-3a187c6fe1e9
        Device : dc2d9dd8-4e9f-4f84-9e3c-3a187c6fe113
        Ellipse : 79112da3-3d3f-443e-8109-f2ea29d85035
        Line : dc2d9dd8-4c27-4e9b-b62f-63d04cfc1019
        Text : dc2d9dd8-4e9f-4f84-9e3c-3a187c6fe118

        The Link parameter represents the Id of the entity to add (The guid representing the camera in the Security Center System
        for instance).

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $maps = @(Get-SCEntities -Type Maps | Where { $_.Name -eq "Map" })
        $cameras = @(Get-SCentities -Type cameras | Where { $_.Name -like "*512Kbps*" })
        if ($maps.count -gt 0 -and $cameras -gt 0) {
            $map = $maps[0]

            Add-SCMapObject -MapId $map -Type dc2d9dd8-4e9f-4f84-9e3c-3a187c6fe1e6 -Latitude 73 -Longitude -45 -Rotation 0 -Link $camera.Id
        }

        Exit-SCSession 

        This code snippet will add the camera whose name match '*512Kpbs*' to the map named 'Map'
        at longitude/latitude -45/73 without any rotation.

    .NOTES
        Written by Genetec Inc.

    .LINK
         Get-SCMapObjects
         Set-SCMapObject
         Remove-SCMapObject
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)]  [alias("mid")]  $MapId,
        [parameter(Mandatory=$true)]  [alias("t")]    $Type,
        [parameter(Mandatory=$true)]  [alias("lat")]  $Latitude,
        [parameter(Mandatory=$true)]  [alias("long")] $Longitude,
        [parameter(Mandatory=$true)] [alias("rot")]  $Rotation,
        [parameter(Mandatory=$true)]  [alias("lnk")]  $Link
    )
    begin {
    }
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $mid = GetIdFromObject $MapId
            
            $uri = "Entities/$mid/mapobjects/"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", $mid)
            $jsonObject.Add("Type", $Type)
            $jsonObject.Add("Latitude", $Latitude)
            $jsonObject.Add("Longitude", $Longitude)
            $jsonObject.Add("Rotation", $Rotation)
            $jsonObject.Add("Link", $Link)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias gscmo Get-SCMapObjects
Function Get-SCMapObjects {
    <#
    .Synopsis
        This method will return the map objects associated with the given map

    .DESCRIPTION
        This method is used to retrieve all map objects associated with a given map.
        
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter ca

        The MapId parameter represents the Id of the map to retrieve (The guid representing the map in the Security Center System)
        You can also pass any map object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $maps = @(Get-SCEntities -Type Maps | Where { $_.Name -eq "Map" })
        if ($maps.count -gt 0) {
            $map = $maps[0]

            $mapObjects = Get-SCMapObjects -MapId $map

            Write-Output "There is $($mapObjects.Count) map object(s) on top of '$($map.Name)' map"
        }

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.

    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MapId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $MapId -RelationName "mapobjects"
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias gscmap Get-SCMaps
Function Get-SCMaps {
    <#
    .Synopsis
        This method will return all the properties of the map represented by the ID

    .DESCRIPTION
        This method will return all the basic properties of the camera represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CameraId parameter represents the Id of the map to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any map object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        Get-SCEntities -Type Maps | Get-SCMaps

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        Add-SCMap
        Remove-SCMap
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MapId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $MapId   
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias rscmap Remove-SCMap
Function Remove-SCMap {
    <#
    .Synopsis
        This method will remove the existing map from the specific Area

    .DESCRIPTION
        This method will remove the existing map from the specific Area.  

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $maps = @(Get-SCEntities -Type Maps | Where { $_.Name -eq "Map" })
        if ($maps.count -gt 0) {
            $map = $maps[0]

            Remove-SCMap -MapId $map
        }
    
        #Exit the session
        Exit-SCSession

        This code snippet will add a map with the specific provider to the area given.

    .NOTES
        Written by Genetec Inc.

    .LINK
        Get-SCMaps
        Remove-SCMap
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MapId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            
            $mid = GetIdFromObject $MapId
            $uri = "Entities/$mid"

            $jsonObject = [ordered]@{} 
            $jsonBody = @{} | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "DELETE" -Body $jsonBody

            $areas = Get-SCEntities -Type Areas | Get-SCArea | where { $_.Link -eq $mid }

            foreach ($area in $areas) {
                $area.Link = [Guid]::empty
                Set-SCArea $area
            }
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias rscmo Remove-SCMapObject
Function Remove-SCMapObject {
    <#
    .SYNOPSIS
        Remove a specific map object from the given map entity.

    .DESCRIPTION
        Remove a map object from a map entity.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The MapId parameter represents the Id of the map to retrieve (The guid representing the map in the Security Center System)
        You can also pass any map object that contains an ID as a parameter.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $maps = @(Get-SCEntities -Type Maps | Where { $_.Name -eq "Map" })
        if ($maps.count -gt 0) {
            $map = $maps[0]
            $mapObjects = Get-SCMapObjects $map

            foreach ($mapObject in $mapObjects) {
                Remove-SCMapObject $map $mapObject
            }
        }

        Exit-SCSession 

        This snippet will remove all map objects from the map named 'Map' if any.

    .NOTES
        Written by Genetec Inc.
    .LINK
        Add-SCMapObject
        Get-SCMapObjects
        Set-SCMapObject
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MapId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipeline=$true)] [alias("mo")]  $MapObject
    )
    begin {
    }
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $moid = $MapObject.Guid
            $mid  = GetIdFromObject $MapId
            
            $uri = "Entities/$mid/mapobjects/$moid"

            $jsonBody = @{} | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "DELETE" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias sscmo Set-SCMapObject
Function Set-SCMapObject {
    <#
    .SYNOPSIS
        Modify the property of a map object from the given map entity.

    .DESCRIPTION
        Modify the property of a specific map object from the given map entity.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The MapId parameter represents the Id of the map to retrieve (The guid representing the map in the Security Center System)
        You can also pass any map object that contains an ID as a parameter

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $maps = @(Get-SCEntities -Type Maps | Where { $_.Name -eq "Map" })
        if ($maps.count -gt 0) {
            $map = $maps[0]
            $mapObjects = Get-SCMapObjects $map
            foreach ($mapObject in $mapObjects) {
                $mapObject.Latitude = $mapObject.Latitude + 0.1
                $mapObject.Longitude = $mapObject.Longitude + 0.1
                $mapObject.ShowFov = !$mapObject.ShowFov
                $mapObject.relativeHeight = $mapObject.RelativeHeight + $mapObject.RelativeHeight * 0.10
                $mapObject.relativeWidth = $mapObject.RelativeWidth + $mapObject.RelativeWidth * 0.10
                Set-SCMapObject $map $mapObject
            }
        }

        Exit-SCSession 

        This code snippet will modify all map objects from the map named 'Map' if any, such that
        it will offset its location (both latitude and longitude) by +0.1 degree, enlarge its
        relative size by 10% and toggle the Field Of View display if applicable.

    .NOTES
        Written by Genetec Inc.

    .LINK
        Get-SCMapObjects
        Add-SCMapObject
        Remove-SCMapObject
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MapId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipeline=$true)] [alias("mo")]  $MapObject
    )
    begin {
    }
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $moid = $MapObject.Guid
            $mid  = GetIdFromObject $MapId
            
            $uri = "Entities/$mid/mapobjects/$moid"

            $jsonBody = $MapObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "PUT" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCMapProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a map
    .DESCRIPTION
        This method will list the supported properties and relation of a map (the data model, not the actual data).  This method is used
        when you want to know what is available for a given cardholder
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCMapProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiMap" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'