﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCUserGroupMembers {
    <#
    .Synopsis
        Method used to add a member to the given usergroup
    .DESCRIPTION
        This Method will allow the user to add a security center user a specified security center usergroup
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserGroupId will be used to specify the usergroup we want to add to the user to

        The parameter MemberId represents the Id of the user we want to add to the usergroup
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUser = New-SCUser -Name "MyNewRestUser" | Get-SCUser
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Add-SCUserGroupMembers -UserGroupId $myNewRestUserGroup -MemberId $myNewUser

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("mid")] $MemberId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $mid = GetIdFromObject $MemberId
            Set-SCEntityRelation -EntityId $UserGroupId -RelationName "members" -RelationId $mid   
        }
    }  
}

# -----------------------------------------------------------------------------
Function Add-SCUserGroupUserGroups {
    <#
    .Synopsis
        Method used to add a parent usergroup to the given usergroup
    .DESCRIPTION
        This Method will allow the user to add a security center parent usergroup to a specified security center usergroup
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserGroupId will be used to specify the usergroup we want to add to the parent usergroup to

        The parameter UserGroupIdToSet represents the Id of the parent usergroup we want to add to the usergroup
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewRestUserParent = New-SCUserGroup -Name "MyNewRestUserParent" | Get-SCUserGroup
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Add-SCUserGroupUserGroups -UserGroupId $myNewRestUserGroup -UserGroupIdToSet $MyNewRestUserParent

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("ugid")] $UserGroupIdToSet
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $ugid = GetIdFromObject $UserGroupIdToSet
            Set-SCEntityRelation -EntityId $UserGroupId -RelationName "usergroups" -RelationId $ugid   
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsug Get-SCUserGroup
Function Get-SCUserGroup {
    <#
    .Synopsis
        This method will return all the properties of the usergroup represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the usergroup represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The UserGroupId parameter represents the Id of the usergroup to retreive (The guid representing the usergroup in the Security Center System)
        You can also pass any usergroup object that contains an ID as a parameter
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUserGroup = New-SCUserGroup -Name "MyNewUsergroup"
        Get-SCUserGroup $myNewUserGroup

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $UserGroupId   
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCUserGroupMembers {
    <#
    .Synopsis
        Method used to get all the members of the given usergroup
    .DESCRIPTION
        This Method will allow the user to get all members of the given usergroup
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserGroupId will be used to specify the usergroup we want to get the members from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUser = New-SCUser -Name "MyNewRestUser" | Get-SCUser
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Add-SCUserUserGroups -UserId $myNewUser -UserGroupId $myNewRestUserGroup
        Get-SCUserGroupMembers -UserGroupId $myNewRestUserGroup

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $UserGroupId -RelationName "members"
        }  
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCUserGroupUserGroups {
    <#
    .Synopsis
        Method used to get all the parent usergroups of the given usergroup
    .DESCRIPTION
        This Method will allow the user to get all parent usergroups of the given usergroup
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserGroupId will be used to specify the usergrou we want to get the parent usergroups from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewRestUserParent = New-SCUserGroup -Name "MyNewRestUserParent" | Get-SCUserGroup
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Add-SCUserGroupUserGroups -UserGroupId $myNewRestUserGroup -UserGroupIdToSet $MyNewRestUserParent
        Get-SCUserGroupUserGroups -UserGroupId $myNewRestUserGroup

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId
    )
    
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {    
            Get-SCEntity -EntityId $UserGroupId -RelationName "Usergroups"
        }

    } 
}

# -----------------------------------------------------------------------------
Function Get-SCUserGroupPrivileges {
    <#
    .Synopsis
        Method used to get all the user privileges of the given usergroup
    .DESCRIPTION
        This Method will allow the user to get all the privileges of the given usergroup
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserGroupId will be used to specify the usergroup we want to get the privilege from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Get-SCUserGroupPrivileges -UserGroupId $myNewRestUserGroup

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $UserGroupId -RelationName privileges
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias nsug New-SCUserGroup
Function New-SCUserGroup {
    <#
    .Synopsis
        Method used to create a new usergroup with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new security center usergroup with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new usergroup upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        New-SCUserGroup -Name "MyNewUserGroup"
        
        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Usergroups
        } 
    }
}

# -----------------------------------------------------------------------------
Set-Alias rsug Remove-SCUserGroup
Function Remove-SCUserGroup {
    <#
    .Synopsis
        Will remove the usergroup represented by the provided UserGroupId from Security Center
    .DESCRIPTION
        This method will permantly remove the specified usergroup from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The UserGroupId parameter represents the usergroup to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $user = New-SCUserGroup -Name "MyNewUserGroup"
        Remove-SCUserGroup $user

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $UserGroupId
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCUserGroupMembers {
    <#
    .Synopsis
        Method used to remove a member of the given usergroup
    .DESCRIPTION
        This Method will allow the user to remove a security center member from a specified security center usergroup
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserGroupId will be used to specify the usergroup we want to remove the member from

        The parameter MemberId represents the Id of the member we want to remove from the usergroup
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUser = New-SCUser -Name "MyNewRestUser" | Get-SCUser
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Add-SCUserGroupMembers -UserGroupId $myNewRestUserGroup -MemberId $myNewUser

        Remove-SCUserGroupMembers -UserGroupId $myNewRestUserGroup -MemberId $myNewUser

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("mid")] $MemberId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $mid = GetIdFromObject $MemberId
            Remove-SCEntityRelation -EntityId $UserGroupId -RelationName "members" -RelationId $mid   
        }
    }   
}

# -----------------------------------------------------------------------------
Function Remove-SCUserGroupUserGroups {
    <#
    .Synopsis
        Method used to remove a parent usergroup from the given usergroup
    .DESCRIPTION
        This Method will allow the user to remove a security center parent usergroup from a specified security center usergroup
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserGroupId will be used to specify the usergroup we want to remove the parent usergroup from

        The parameter UserGroupIdToRemove represents the Id of the parent usergroup we want to remove from the usergroup
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewRestUserParent = New-SCUserGroup -Name "MyNewRestUserParent" | Get-SCUserGroup
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Add-SCUserGroupUserGroups -UserGroupId $myNewRestUserGroup -UserGroupIdToSet $MyNewRestUserParent

        Remove-SCUserGroupUserGroups -UserGroupId $myNewRestUserGroup -UserGroupIdToRemove $MyNewRestUserParent

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("ugid")] $UserGroupIdToRemove
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $ugid = GetIdFromObject $UserGroupIdToRemove
            Remove-SCEntityRelation -EntityId $UserGroupId -RelationName "usergroups" -RelationId $ugid   
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias ssug Set-SCUserGroup
Function Set-SCUserGroup() {
    <#
    .Synopsis
        Used to update the properties of a usergroup in Security Center
    .DESCRIPTION
        This method is used to update the properties of a usergroup to Security Center.  All properties that are not read-only will be updated.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated seperatly by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserGroup represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new usergroup
        $newug = New-SCUserGroup -Name "MyUserGroup" | gsug
        $newug.Description = "test"
        
        Set-SCUserGroup -User $newug

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new usergroup
        $newug = New-SCUserGroup -Name "MyUserGroup" | gsug
        $newug.Description = "test"
        
        ssug $newug

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("ug")] $UserGroup
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $UserGroup
        }
    }
}

# -----------------------------------------------------------------------------
Function Set-SCUserGroupPrivileges {
    <#
    .Synopsis
        Method used to set the state of the usergroup privileges of the given usergroup
    .DESCRIPTION
        This Method will set the state of the usergroup privileges of the given usergroup
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserGroupId will be used to specify the usergroup we want to set to privilege to
        The parameter PrivilegeId will be used to specify the user privilege we want to update
        The parameter PrivilegeState will be used to specify the state of the given privilege
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUserGroup = New-SCUserGroup -Name "MyNewUserGroup" | Get-SCUserGroup
        $myUserPriviledge = Get-SCUserGroupPrivileges -UserGroupId $myNewUserGroup
        $myUserPriviledge | foreach{ if($_.State -eq "Undefined"){ Set-SCUserGroupPrivileges -UserGroupId $myNewUserGroup -PrivilegeId $_.Id -PrivilegeState 'Granted'}}

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PrivilegeId
    )
 
    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'PrivilegeState'
        $ParameterAlias = 'ps'
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $true
        $ParameterAttribute.Position = 3

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        # Generate and set the ValidateSet 
            
        $arrSet = 'Denied', 'Granted', 'Undefined'
        $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

        # Add the ValidateSet to the attributes collection
        $AttributeCollection.Add($ValidateSetAttribute)

         #add the alias to the attributes collection
        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)
        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $PrivilegeState = $PsBoundParameters[$ParameterName]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $ugid = GetIdFromObject $UserGroupId
            $pid = GetIdFromObject $PrivilegeId
            $uri = "Entities/$ugid/Privileges/$pid"
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("State", $PrivilegeState)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Put" -Body $jsonBody
        } 
    } 
} 

# -----------------------------------------------------------------------------
Function Show-SCUserGroupProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a usergroup
    .DESCRIPTION
        This method will list the supported properties and relation of a usergroup (the data model, not the actual data).  This method is used
        when you want to know what is available for a given usergroup
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCUserGroupProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiUserGroup" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'