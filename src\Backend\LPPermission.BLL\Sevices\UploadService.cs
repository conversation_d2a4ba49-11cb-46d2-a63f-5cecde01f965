﻿using AutoMapper;
using LPPermission.BLL.Interfaces;
using LPPermission.DAL.Interfaces;
using LPPermission.DAL.Models;
using LPPermission.Models;
using Microsoft.EntityFrameworkCore;

namespace LPPermission.BLL.Services
{
    /// <summary>
    /// Service for handling upload-related operations, including processing CSV records,
    /// managing permissions, and retrieving geo and container data.
    /// </summary>
    public class UploadService : IUpload
    {
        private readonly IGenericRepository<Menu> _menuRepository;
        private readonly IGenericRepository<Privilege> _privilegeRepository;
        private readonly IGenericRepository<Permission> _permissionRepository;
        private readonly IGenericRepository<UserGroup> _storeUserGroupRepository;
        private readonly IGenericRepository<State> _stateRepositoty;
        private readonly IGenericRepository<Geo> _geoRepository;
        private readonly IGenericRepository<Container> _containerRepository;

        /// <summary>
        /// Constructor to initialize dependencies for the UploadService.
        /// </summary>
        public UploadService(
            IGenericRepository<Menu> menuRepository,
            IGenericRepository<Privilege> privilegeRepository,
            IGenericRepository<Permission> permissionRepository,
            IGenericRepository<UserGroup> storeUserGroupRepository,
            IGenericRepository<State> stateRepositoty,
            IGenericRepository<Geo> geoRepository,
            IGenericRepository<Container> containerRepository
            )
        {
            _menuRepository = menuRepository;
            _privilegeRepository = privilegeRepository;
            _permissionRepository = permissionRepository;
            _storeUserGroupRepository = storeUserGroupRepository;
            _stateRepositoty = stateRepositoty;
            _geoRepository = geoRepository;
            _containerRepository = containerRepository;

        }

        /// <summary>
        /// Processes CSV records to create or update menus, privileges, and permissions.
        /// </summary>
        /// <param name="records">List of CSV records to process.</param>
        /// <param name="geoName">Name of the geo location.</param>
        /// <param name="containerName">Name of the container.</param>
        public async Task ProcessCsvRecordsAsync(List<CsvRecord> records, string geoName, string containerName)
        {
            try
            {
                // Fetch GeoId and ContainerId
                var geo = await _geoRepository.GetByConditionAsync(g => g.Name == geoName);
                var container = await _containerRepository.GetByConditionAsync(c => c.Name == containerName);
                if (geo == null || container == null)
                {
                    throw new Exception("Geo or Container not found");
                }
                int geoId = geo.GeoId;
                int containerId = container.ContainerId;
                if (containerId == 0)
                {
                    throw new Exception("ContainerId not found");
                }
                int menuIdCounter = 1;
                int privilegeIdCounter = 1;

                foreach (var record in records)
                {
                    int? parentId = null;
                    var parentSections = record.Parent.Split('/');
                    int menuLevel = 0;

                    // Process hierarchical menus
                    foreach (var section in parentSections)
                    {
                        menuLevel++;
                        var truncatedName = section.Length > 8 ? section.Substring(0, 8) : section;
                        truncatedName = section;

                        var existingMenuQuery = _menuRepository
                            .GetByConditionAsyncMenu(m => m.Name == truncatedName && m.MenuLevel == menuLevel && m.ParentId == parentId)
                            .AsNoTracking();
                        if (existingMenuQuery == null)
                        {
                            throw new Exception("Menu Query not found");
                        }
                        try
                        {
                            var existingMenu = await existingMenuQuery.FirstOrDefaultAsync();


                            if (existingMenu == null)
                            {
                                var newMenu = new Menu
                                {
                                    ParentId = parentId,
                                    Name = truncatedName,
                                    Description = truncatedName,
                                    MenuLevel = menuLevel,
                                    MenuId = menuIdCounter
                                };

                                await _menuRepository.AddAsync(newMenu);
                                await _menuRepository.SaveAsync();

                                parentId = newMenu.MenuId;
                                menuIdCounter++;
                            }
                            else
                            {
                                parentId = existingMenu.MenuId;
                            }
                        }
                        catch (Exception ex)
                        {
                            throw new Exception("An error occurred while querying the menu");
                        }
                    }

                    // Process Privilege
                    if (parentId.HasValue)
                    {
                        var privilege = new Privilege
                        {
                            MenuId = parentId.Value,
                            Name = record.Privilege.Length > 50 ? record.Privilege.Substring(0, 50) : record.Privilege,
                            Description = record.Description.Length > 50 ? record.Description.Substring(0, 50) : record.Description,
                            GenetecRef = "null",
                            PrivilegeId = privilegeIdCounter
                        };

                        var existingPrivilegeQuery = _privilegeRepository
                            .GetByConditionAsyncPrivelege(p => p.Name == privilege.Name && p.MenuId == privilege.MenuId)
                            .AsNoTracking();
                        var existingPrivilege = await existingPrivilegeQuery.FirstOrDefaultAsync();

                        if (existingPrivilege == null)
                        {
                            await _privilegeRepository.AddAsync(privilege);
                            await _privilegeRepository.SaveAsync();
                            privilegeIdCounter++;
                        }
                        else
                        {
                            privilege = existingPrivilege;
                        }

                        // Dynamically process group-related columns from the CSV record
                        foreach (var groupPermission in record.GroupPermissions)
                        {
                            var userGroupName = groupPermission.Key;
                            var permissionValue = groupPermission.Value;

                            // Get or create UserGroupId with geo and container
                            int userGroupId = await GetOrCreateUserGroupId(userGroupName, geoId, containerId, geoName, containerName);

                            // Get StateId
                            int stateId = GetStateIdFromPermissionValue(permissionValue);

                            // Check if the Permission already exists
                            var existingPermissionQuery = _permissionRepository
                                .GetByConditionAsyncPermission(p =>
                                    p.PrivilegeId == privilege.PrivilegeId &&
                                    p.UserGroupId == userGroupId &&
                                    p.GeoId == geoId &&
                                    p.ContainerId == containerId);
                            var existingPermission = await existingPermissionQuery.FirstOrDefaultAsync();

                            if (existingPermission != null)
                            {
                                // Update the StateId only if it has changed
                                if (existingPermission.StateId != stateId)
                                {
                                    existingPermission.StateId = stateId;
                                    await _permissionRepository.SaveAsync();
                                }
                            }
                            else
                            {
                                // Add a new permission if it does not exist
                                var permission = new Permission
                                {
                                    PrivilegeId = privilege.PrivilegeId,
                                    UserGroupId = userGroupId,
                                    StateId = stateId,
                                    GeoId = geoId,
                                    ContainerId = containerId
                                };

                                await _permissionRepository.AddAsync(permission);
                                await _permissionRepository.SaveAsync();
                            }
                        }
                    }

                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Retrieves or creates a UserGroupId based on the provided parameters.
        /// </summary>
        public async Task<int> GetOrCreateUserGroupId(string userGroupName, int geoId, int containerId, string geoName, string containerName)
        {
            userGroupName = userGroupName.Replace("_", "-");

            var existingUserGroup = await _storeUserGroupRepository
                .GetByConditionAsyncUserGroup(ug =>
                    ug.Name == userGroupName &&
                    ug.GeoId == geoId &&
                    ug.ContainerId == containerId)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (existingUserGroup != null)
            {
                return existingUserGroup.UserGroupId;
            }

            var allUserGroups = await _storeUserGroupRepository
                .GetByConditionAsyncUserGroup(ug => true)
                .ToListAsync();
            var maxUserGroupId = allUserGroups.Max(ug => (int?)ug.UserGroupId) ?? 0;
            var nextUserGroupId = maxUserGroupId + 1;

            var newUserGroup = new UserGroup
            {
                UserGroupId = nextUserGroupId,
                Name = userGroupName,
                Description = userGroupName,
                GeoId = geoId,
                ContainerId = containerId,
            };

            await _storeUserGroupRepository.AddAsync(newUserGroup);
            await _storeUserGroupRepository.SaveAsync();

            return newUserGroup.UserGroupId;
        }
        /// <summary>
        /// Maps permission values to corresponding StateIds.
        /// </summary>
        private int GetStateIdFromPermissionValue(string permissionValue)
        {
            return permissionValue switch
            {
                "Allowed" => 1,
                "Deny" => 2,
                "Undefined" => 3,
                _ => throw new Exception($"Invalid permission value: {permissionValue}")
            };
        }
        /// <summary>
        /// Retrieves all permissions and maps them to DTOs.
        /// </summary>
        public async Task<IEnumerable<PermissionDto>> GetAllPermissions()
        {
            var permissionsQuery = _permissionRepository.GetAllPermissions();
            var permissions = await permissionsQuery;

            var permissionDtos = permissions
                .Select(p => new PermissionDto
                {
                    ContainerName = p.ContainerName,
                    GeoName = p.GeoName,
                    MenuName = p.MenuName,
                    PrivilegeName = p.PrivilegeName,
                    StateName = p.StateName,
                    UserGroupName = p.UserGroupName,
                    StoreName = p.StoreName
                })
                .ToList();

            return permissionDtos ?? Enumerable.Empty<PermissionDto>();
        }
        /// <summary>
        /// Retrieves all geo locations and maps them to DTOs.
        /// </summary>
        public async Task<IEnumerable<GeoDto>> GetAllGeosAsync()
        {
            var geosQuery = _geoRepository.GetAllGeos();
            var geos = await geosQuery.ToListAsync();
            var geosData = geos.Select(g => new GeoDto
            {
                GeoId = g.GeoId,
                Name = g.Name ?? string.Empty,
                Description = g.Description ?? string.Empty
            }).AsQueryable();
            return geosData;
        }
        /// <summary>
        /// Retrieves containers by GeoId and maps them to DTOs.
        /// </summary>
        public async Task<IEnumerable<ContainerDto>> GetContainersByGeoIdAsync(int geoId)
        {
            var containersQuery = _containerRepository.GetContainersByGeoId(geoId);
            var containers = await containersQuery.ToListAsync();
            var containersData = containers.Select(g => new ContainerDto
            {
                GeoId = g.GeoId,
                ContainerId = g.ContainerId,
                ContainerName = g.Name ?? string.Empty,
                Description = g.Description ?? string.Empty
            }).AsQueryable();
            return containersData;
        }
    }
}