# ==========================================================================
# Copyright (C) 1989-2018 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Show-SCLprManagerModules {
    <#
    .Synopsis
        Used to retrieve Lpr Manager Modules
    .DESCRIPTION
        This method will return the different Configuration modules from the LPR Manager role.
        If the Module Name is empty or not provided, the methid will return all the modules
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        Show-SCLprManagerModules -Id "LprManagerGuid" - ModuleName "HitMatcherSettings"

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$false)] [alias("MName")][string]$ModuleName = ""
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "Modules" -RelationId $ModuleName
        }
    }
}
# -----------------------------------------------------------------------------
Function Show-SCLprManagerAssociations {
    <#
    .Synopsis
        Used to retrieve Lpr Manager Associations
    .DESCRIPTION
        This method will return the associations of the LPR Manager role and the hotlists / permits entities
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        Show-SCLprManagerAssociations -Id "LprManagerGuid"

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "Associations"
        }
    }

}
# -----------------------------------------------------------------------------
Function Remove-SCLprManagerAssociation {
    <#
    .Synopsis
        Used to remove an association from Lpr Manager Associations
    .DESCRIPTION
        This method will remove an association from the LPR Manager role and the hotlists / permits entity associations
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        Remove-SCLprManagerAssociation -Id "LprManagerGuid" -AssociationGuid "PermitGuid"

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] $AssociationGuid
    )

    Begin {
    }

    Process {

        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/Associations/$AssociationGuid"

            InvokeSCRestMethod -UriSuffix $uri -Method "Delete" -Body (@{} | ConvertTo-Json)
        }
    }
}
# -----------------------------------------------------------------------------
Function Add-SCLprManagerAssociation {
    <#
    .Synopsis
        Used to Add an association to an Lpr Manager Associations
    .DESCRIPTION
        This method will remove an association from the LPR Manager role and the hotlists / permits entity associations
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        Add-SCLprManagerAssociation -Id "LprManagerGuid" -AssociationGuid "PermitGuid"

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("aid")] $AssociationGuid
    )
    begin {
    }
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $Role = GetIdFromObject $RoleId
            $Association = GetIdFromObject $AssociationId

            $uri = "Entities/$Role/Associations/"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", $Association)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCLprManagerModule {
    <#
    .Synopsis
        Used to set Lpr configuration modules
    .DESCRIPTION
        This method will update the configuration of the LPR Manager activatting / deactivationg all the modules of of the role.
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        Set-SCLprManagerModule -Id "LprManagerGuid" - ModuleName "HitMatcherSettings" -IsActive $true

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("mname")] [string]$ModuleName,
        [parameter(Mandatory=$true)] [alias("a")] [bool]$IsActive
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 

            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/Modules/$ModuleName"
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("isActive", $IsActive.ToString())
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "PUT" -Body $jsonBody
        }
    }
}
