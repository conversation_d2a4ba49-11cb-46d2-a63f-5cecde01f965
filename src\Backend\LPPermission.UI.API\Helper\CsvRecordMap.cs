﻿using CsvHelper.Configuration;
using LPPermission.Models;

namespace LPPermission.UI.API.Helper
{
    /// <summary>
    /// This method is used map the CSV property 
    /// </summary> <summary>

    public class CsvRecordMap : ClassMap<CsvRecord>
    {
        public CsvRecordMap()
        {
            Map(m => m.Parent).Name("Parent");
            Map(m => m.Privilege).Name("Privilege");
            Map(m => m.State).Name("State");
            Map(m => m.InheritedFrom).Name("Inherited from"); // Maps "Inherited from" header to InheritedFrom property
            Map(m => m.Description).Name("Description");

            // Dynamically map group-related columns
            Map(m => m.GroupPermissions).Convert(args =>
            {
                var record = args.Row;
                var groupPermissions = new Dictionary<string, string>();

                foreach (var header in record.HeaderRecord)
                {
                    if (header.StartsWith("TJXG_")) // Identify group-related columns dynamically
                    {
                        groupPermissions[header] = record.GetField(header);
                    }
                }

                return groupPermissions;
            });
        }
    }
}
