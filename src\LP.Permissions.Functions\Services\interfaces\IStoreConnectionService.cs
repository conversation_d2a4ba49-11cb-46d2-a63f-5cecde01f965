using LPPermission.DAL.Models;
using LPPermissionStoreSetup.Models;
using UserGroup = LPPermissionStoreSetup.Models.Group;

namespace FunctionApp.LPPermissions.Services.interfaces
{
    public interface IStoreConnectionService
    {
        bool Connect(string ipAddress, string username, string credential);
        Task<List<PermissionDto>> SetLPPermissions(string ipAddress, string username, string credential);
    }
}
