﻿using System.Net.Http.Json;
using LPPermission.BLL.Model;

namespace LPPermission.BLL.Services
{
    public class LogService
    {
        private readonly HttpClient _httpClient;

        public LogService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<List<LogEntry>> GetLogEntriesAsync()
        {
            return await _httpClient.GetFromJsonAsync<List<LogEntry>>("http://localhost:7010/api/GetLogEntries");
        }
    }
}
