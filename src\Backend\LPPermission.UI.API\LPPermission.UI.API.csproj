﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <OutputType>Exe</OutputType>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	<RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
	<ProjectGuid>{E6B134F8-117A-459F-8696-6BA005303630}</ProjectGuid>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="local.settings.json" />
  </ItemGroup>
  <!--<ItemGroup>
    <Content Include="local.settings.json" />
  </ItemGroup>-->
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.22.0" />
    <!-- Application Insights isn't enabled by default. See https://aka.ms/AAt8mw4. -->
    <!-- <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" /> -->
    <!-- <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="2.0.0" /> -->
	  <!--<PackageReference Include="Microsoft.Azure.Functions.Worker" Version="1.10.0" />-->
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.3.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.ApplicationInsights" Version="2.22.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
  <ItemGroup>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
	  <None Update="local.settings.json">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		  <CopyToPublishDirectory>Never</CopyToPublishDirectory>
	  </None>
  </ItemGroup>
  <ItemGroup>
    <Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Models\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LPPermission.BLL\LPPermission.BLL.csproj" />
    <ProjectReference Include="..\LPPermission.DAL\LPPermission.DAL.csproj" />
    <ProjectReference Include="..\LPPermission.Models\LPPermission.Models.csproj" />
  </ItemGroup>
</Project>