# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Get-SCParkingZone {
    <#
    .Synopsis
        This method will return all the properties of the parking zone represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the parking zone represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ParkingZoneId parameter represents the Id of the parking zone to retrieve (The guid representing the entity in the Security Center System)
        You can also pass any parking zone object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myParkingZone = New-SCParkingZone -n "Duck Pond Parking"

        Get-SCParkingZone -ParkingZoneId $myParkingZone

        #Exit the session
        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ParkingZoneId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $ParkingZoneId
            Get-SCEntity -EntityId $id           
        }
    }
}
# -----------------------------------------------------------------------------
Function New-SCParkingZone {
    <#
    .Synopsis
        Method used to create a new parking zone with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new parking zone with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new parking zone upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        $parkingZoneId = New-SCParkingZone -n "Imperial Griffin Parking"

        Exit-SCSession     
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t ParkingZones
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCParkingZone {
    <#
    .Synopsis
        Will remove the parking zone represented by the provided ParkingZoneId parameter from Security Center
    .DESCRIPTION
        This method will permanently remove the specified parking zone from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ParkingZoneId parameter represents the credential to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        #Create and then remove a parking zone
        $parkingZone = New-SCParkingZone -n "Meadow Parking" | Get-SCParkingZone

        Remove-SCParkingZone -ParkingZoneId $parkingZone

        Exit-SCSession     
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ParkingZoneId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $ParkingZoneId
            Remove-SCEntity -EntityId $id 
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCParkingZone() {
    <#
    .Synopsis
        Used to update the properties of a parking zone in Security Center
    .DESCRIPTION
        This method is used to update the properties of a parking zone to Security Center.  All properties that are not read-only will be updated.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter ParkingZone represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        # Create a new parking zone.
        $parkingZone = New-SCParkingZone -Name "Big Parking" | Get-SCParkingZone
        
        # Set the parking capacity.
        $parkingZone.Capacity = 2000

        # Update the entity.
        Set-SCParkingZone -ParkingZone $parkingZone 

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("h")] $ParkingZone
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $ParkingZone
        }
    }
}
# -----------------------------------------------------------------------------
Function Show-SCParkingZoneProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a parking zone
    .DESCRIPTION
        This method will list the supported properties and relation of a parking zone (the data model, not the actual data).  This method is used
        when you want to know what is available for a parking zone
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        Show-SCParkingZoneProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiParkingZone" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCParkingZoneLprCameras {
    <#
    .Synopsis
        Method used to get all parking zone lpr cameras
    .DESCRIPTION
        This Method will retrieve configuration information for all lpr cameras currently assigned to the parking zone
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter ParkingZoneId will be used to specify the parking zone to query
        You can also pass any parking zone object that contains an ID as a parameter
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 

        $parkingZoneId = New-SCParkingZone -n "New parking zone"

        $parkingZoneCameras = Get-SCParkingZoneLprCameras -ParkingZoneId $parkingZoneId

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ParkingZoneId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $ParkingZoneId -RelationName ParkingZoneLprCameras
        }
    }   
}

# -----------------------------------------------------------------------------
Function Add-SCParkingZoneLprCamera() {
    <#
    .Synopsis
         Method used to create assign a new lpr camera to a parking zone
    .DESCRIPTION
        This method is used to assign an lpr camera device to a parking zone
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ParkingZoneId parameter represents the id of the parking zone to assign the lpr camera to
        You can also pass any parking zone object that contains an id as a parameter

        The LprCameraInfo parameter represents the configuration of the lpr camera to assign to the parking zone. See the example below for sample creation.
        
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        $parkingZone = New-SCParkingZone -n "Grumpy bear parking"

        #The following assumes that 2 lpr camera devices exist under the following Ids in security center
        $entranceCameraInfo = [PSCustomObject]@{
            DeviceId = "10A80BB3-A3DC-4B7E-8BCD-DA799645A024"
            Position = "Entrance"
            RelativeMotion = "Ignored"
            }

        $exitCameraInfo = [PSCustomObject]@{
            DeviceId = "092D2DF5-D63C-495F-9656-EAFF957CB839"
            Position = "Exit"
            RelativeMotion = "Ignored"
            }

        Add-SCParkingZoneLprCamera -ParkingZoneId $parkingZone -LprCameraInfo $entranceCameraInfo
        Add-SCParkingZoneLprCamera -ParkingZoneId $parkingZone -LprCameraInfo $exitCameraInfo

        Exit-SCSession  
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("id")] $ParkingZoneId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("lprCamInfo")] $LprCameraInfo
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $id = GetIdFromObject $ParkingZoneId
            $jsonBody = $LprCameraInfo | ConvertTo-Json 
    
            InvokeSCRestMethod -Method 'POST' -UriSuffix "Entities/$id/ParkingZoneLprCameras" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Set-SCParkingZoneLprCamera() {
    <#
    .Synopsis
        Method used to update the configuration of an existing lpr camera access point assigned to a parking zone
    .DESCRIPTION
        This method is used to update the configuration for an lpr camera assigned to a parking zone.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ParkingZoneId parameter represents the Id the parking zone to update the lpr camera configuration of
        You can also pass any parking zone object

        The AccessPointId represents the id of the access point lpr camera to update the configuration of
        These can be obtained from the Get-SCParkingZoneLprCamera function

        The LprCameraInfo parameter represents the update parking zone lpr camera configuration.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        $parkingZone = New-SCParkingZone -n "Solar Inc Parking"

        #The following assumes that an lpr camera devices exists under the following device id in security center
        $cameraInfo = [PSCustomObject]@{
            DeviceId = "10A80BB3-A3DC-4B7E-8BCD-DA799645A024"
            Position = "Entrance"
            RelativeMotion = "Ignored"
            }

        $accessPointId = Add-SCParkingZoneLprCamera -ParkingZoneId $parkingZone -LprCameraInfo $entranceCameraInfo

        # Change camera configuration
        $cameraInfo.Position = "Exit"
        $cameraInfo.RelativeMotion = "Approaching"

        Set-SCParkingZoneLprCamera -ParkingZoneId $parkingZone -AccessPointId $accessPointId -LprCameraInfo $cameraInfo

        Exit-SCSession  
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("id")] $ParkingZoneId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("acessId")] $AccessPointId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("lprCamInfo")] $LprCameraInfo
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $id = GetIdFromObject $ParkingZoneId
            $acessId = GetIdFromObject $AccessPointId
            $jsonBody = $LprCameraInfo | ConvertTo-Json 
    
            InvokeSCRestMethod -Method 'PUT' -UriSuffix "Entities/$id/ParkingZoneLprCameras/$acessId" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCParkingZoneLprCamera {
    <#
    .Synopsis
        Will remove a parking zone lpr camera access point from the specified parking zone, provided it exists
    .DESCRIPTION
        This method will permanently remove the specified lpr camera access point from a parking zone
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ParkingZoneId paramter specifies the parking zone to remove lpr camera access points from. 
        You can also pass any parking zone object

        The AccessPointId parameter specifies the lpr camera access point to remove. A list of lpr camera access points can be obtained from the Get-SCParkingZoneLprCameras function

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
      
        Remove-SCParkingZoneLprCamera -ParkingZoneId $parkingZone -AccessPointId $lprCameraAccessPoint

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $ParkingZoneId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("accessId")] $AccessPointId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $ParkingZoneId
            $accessId = GetIdFromObject $AccessPointId
    
            InvokeSCRestMethod -Method 'DELETE' -UriSuffix "Entities/$id/ParkingZoneLprCameras/$accessId"      
        }
    }
}


# -----------------------------------------------------------------------------
Function Get-SCParkingZoneParkingRules {
    <#
    .Synopsis
        Method used to get all parking rules currently assigned to a parking zone
    .DESCRIPTION
        This Method will allow the user to get all parking rules currently assigned to a parking zone
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter ParkingZoneId will be used to specify the parking zone to query
        You can also pass any parking zone object
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        $parkingZoneId = New-SCParkingZone -n "NewParkingZone"

        $newRuleId = New-SCParkingRule -n "NewParkingRule"

        Add-SCParkingZoneParkingRule -ParkingZoneId $parkingZoneId -ParkingRuleId $newRuleId

        Get-SCParkingZoneParkingRules -ParkingZoneId $parkingZoneId

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ParkingZoneId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $ParkingZoneId -RelationName "ParkingRules"
        }
    }   
}

# -----------------------------------------------------------------------------
Function Add-SCParkingZoneParkingRule {
    <#
    .Synopsis
        Method used to assign a parking rule to a parking zone
    .DESCRIPTION
        This Method will allow the user assign an existing parking rule to a parking zone
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter ParkingZoneId represents the parking zone to assign the parking rule to
        You can also pass any parking zone object

        The parameter ParkingRuleId represents the Id of the parking zone rule to add to the parking zone
        You can also pass any parking rule
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        $parkingZoneId = New-SCParkingZone -n "NewParkingZone"

        $newRuleId = New-SCParkingRule -n "NewParkingRule"

        Add-SCParkingZoneParkingRule -ParkingZoneId $parkingZoneId -ParkingRuleId $newRuleId

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $ParkingZoneId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("ruleId")] $ParkingRuleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $ParkingZoneId
            $ruleId = GetIdFromObject $ParkingRuleId
            Set-SCEntityRelation -EntityId $id -RelationName "ParkingRules" -RelationId $ruleId   
        }
    } 
}

# -----------------------------------------------------------------------------
Function Remove-SCParkingZoneParkingRule {
    <#
    .Synopsis
        Will remove the given parking zone rule from the parking zone (provided it is assigned)
    .DESCRIPTION
        This method will permanently remove the assignment of a parking rule to a parking zone.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ParkingZoneId parameter represents the id of the parking zone to remove the rule from
        You can also pass any parking zone object

        The ParkingRuleId parameter represents the id of the parking rule the remove
        You can also pass any parking rule object
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        $parkingZoneId = New-SCParkingZone -n "NewParkingZone"

        $newRuleId = New-SCParkingRule -n "NewParkingRule"

        Add-SCParkingZoneParkingRule -ParkingZoneId $parkingZoneId -ParkingRuleId $newRuleId

        #Remove the newly added parking rule
        Remove-SCParkingZoneParkingRule -ParkingZoneId $parkingZoneId -ParkingRuleId $newRuleId

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $ParkingZoneId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("ruleId")] $ParkingRuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $ParkingZoneId
            $ruleId = GetIdFromObject $ParkingRuleId
    
            InvokeSCRestMethod -Method 'DELETE' -UriSuffix "Entities/$id/ParkingRules/$ruleId"      
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCParkingZoneVideoCameras {
    <#
    .Synopsis
        Method used to get the list of video cameras under a parking zone.
    .DESCRIPTION
        This Method will allow the user to retrieve the video cameras of a security center parking zone
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter ParkingZoneId will be used to specify the parking zone we want to retrieve the members of
        You can also pass any parking zone object
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new parking zone
        $parkingZone = New-SCParkingZone -n "MyNewParkingZone"
       
        #Retrieve children of parking zone 
        Get-SCParkingZoneVideoCameras -ParkingZoneId $parkingZone
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $ParkingZoneId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $ParkingZoneId
            Get-SCEntity -EntityId $id -RelationName "ParkingZoneVideoCameras"
        }
    }    
}
# -----------------------------------------------------------------------------
Function Remove-SCParkingZoneVideoCamera {
    <#
    .Synopsis
        Will remove the given parking zone video camera specified by the id from the hierarchical children a parking zone
    .DESCRIPTION
        This method will remove the assignment of a video camera from a parking zone
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ParkingZoneId parameter represents the id of the parking zone to remove the rule from
        You can also pass any parking zone object provided it has an Id field

        The VideoCameraId parameter represents the id of the parking rule the remove
        You can also pass any video camera object provided it has an Id field
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        $parkingZoneId = New-SCParkingZone -n "NewParkingZone"

        Add-SCParkingZoneVideoCamera -ParkingZoneId $parkingZoneId -VideoCameraId $videoCameraId

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $ParkingZoneId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cameraId")] $VideoCameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $ParkingZoneId
            $cameraId = GetIdFromObject $VideoCameraId
    
            InvokeSCRestMethod -Method 'DELETE' -UriSuffix "Entities/$id/ParkingZoneVideoCameras/$cameraId"      
        }
    }
}

# -----------------------------------------------------------------------------
Function Add-SCParkingZoneVideoCamera {
    <#
    .Synopsis
        Method used to associate a video camera with a parking zone.
    .DESCRIPTION
        This Method will allow the user assign a video camera to a parking zone. Video cameras can be used to display a live video stream during monitoring.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter ParkingZoneId represents the parking zone to assign the parking rule to
        You can also pass any parking zone object

        The parameter ParkingRuleId represents the Id of the parking zone rule to add to the parking zone
        You can also pass any parking rule
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        $parkingZoneId = New-SCParkingZone -n "NewParkingZone"

        Add-SCParkingZoneVideoCamera -ParkingZoneId $parkingZoneId -VideoCameraId $videoCameraId

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $ParkingZoneId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cameraId")] $VideoCameraId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $ParkingZoneId
            $cameraId = GetIdFromObject $VideoCameraId
            Set-SCEntityRelation -EntityId $id -RelationName "ParkingZoneVideoCameras" -RelationId $cameraId   
        }
    } 
}

Export-ModuleMember -Function '*-*' -Alias '*'
