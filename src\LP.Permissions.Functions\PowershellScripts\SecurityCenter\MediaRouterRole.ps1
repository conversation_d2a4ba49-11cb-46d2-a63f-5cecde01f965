﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-MediaRouterRedirector {
    <#
    .Synopsis
        This method will add a redirector on the given Media router role
    .DESCRIPTION
        This method will add a redirector on the given Media router role
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The MediaRouterRoleId parameter represents the Id of the media router role to add the redirectors to
        You can also pass any role object that contains an ID as a parameter

        The ServerId parameter represents the id of Genetec server where the redirector will be created
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $mr = Get-SCMediaRouterRole

        #must have a schedule named myschedule in SC
        $servers = Get-SCEntities -Type Servers

        Add-MediaRouterRedirector -MediaRouterRoleId $mr.Id -ServerId $servers[0].Id

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MediaRouterRoleId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sId")] $ServerId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $mrid = GetIdFromObject $MediaRouterRoleId
            $uri = "Entities/$mrid/Redirectors"
         
            $jsonObject = [ordered]@{}
            $jsonObject.Add("ServerId", $ServerId)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-MediaRouterRedirectors {
    <#
    .Synopsis
        This method will return all the redirectors of the given media router role
    .DESCRIPTION
        This method will return all the redirectors of the given media router role
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The MediaRouterRoleId parameter represents the Id of the media router role to retrieve the redirectors from
        You can also pass any role object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myMR = Get-SCMediaRouterRole 
        Get-MediaRouterRedirectors $myMR

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MediaRouterRoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $MediaRouterRoleId -RelationName "Redirectors"  
        }
    } 
}

# -----------------------------------------------------------------------------
Function Get-SCMediaRouterRole {
    <#
    .Synopsis
        This method will get the media router role
    .DESCRIPTION
        This method is used when the user wants to get the media router role
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $mediaRouter = Get-SCMediaRouterRole
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    Begin {
    }
    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCRoles -t StreamManagement -Filter All
        }
    }   
}

# -----------------------------------------------------------------------------
Function Remove-MediaRouterRedirector {
    <#
    .Synopsis
        This method will remove a redirector on the given Media router role
    .DESCRIPTION
        This method will remove a redirector on the given Media router role
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The MediaRouterRoleId parameter represents the Id of the media router role to remove the redirectors from
        You can also pass any role object that contains an ID as a parameter

        The ServerId parameter represents the id of Genetec server where the redirector to be deleted is running on
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $mr = Get-SCMediaRouterRole

        #must have a schedule named myschedule in SC
        $servers = Get-SCEntities -Type Servers

        Remove-MediaRouterRedirector -MediaRouterRoleId $mr.Id -ServerId $servers[0].Id

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MediaRouterRoleId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sId")] $ServerId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Remove-SCEntityRelation -EntityId $MediaRouterRoleId -RelationName "Redirectors" -RelationId $ServerId
            }
    }
}

# -----------------------------------------------------------------------------
Function Set-MediaRouterRedirector() {
<#
    .Synopsis
        This method will modify existing redirector that is associated with an media router role
    .DESCRIPTION
        This method allows you to change the settings of the redirector associated with the media router role. 
        
        The MediaRouterRoleId parameter represents the Id of the media router role to retrieve the redirectors from
        You can also pass any role object that contains an ID as a parameter

        The Redirector parameter represents the Redirector object that contains the parameters to set.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""
                
        $myMR = Get-SCMediaRouterRole
        $red =Get-MediaRouterRedirectors $myMR

        $red[0].LiveBandwidthHardLimit = 50
        Set-MediaRouterRedirector $myMR -Redirector $red

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>   

       [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $MediaRouterRoleId,
        [parameter(Mandatory=$true)] [alias("redir")] $Redirector
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $MediaRouterRoleId
            $jsonBody = $Redirector | ConvertTo-Json
            $ServerId = $Redirector.ServerId
            $uri = "entities/$rid/redirectors/$ServerId"
            InvokeSCRestMethod -UriSuffix $uri -Method 'PUT' -Body $jsonBody
        }
    }
}  

Export-ModuleMember -Function '*-*' -Alias '*'
