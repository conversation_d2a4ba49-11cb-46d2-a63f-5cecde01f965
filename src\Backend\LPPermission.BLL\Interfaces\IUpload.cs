﻿using LPPermission.Models;

namespace LPPermission.BLL.Interfaces
{
    public interface IUpload
    {
        Task ProcessCsvRecordsAsync(List<CsvRecord> records, string geoName, string containerName);

        Task<IEnumerable<PermissionDto>> GetAllPermissions();

        Task<IEnumerable<ContainerDto>> GetContainersByGeoIdAsync(int geoId);
        Task<IEnumerable<GeoDto>> GetAllGeosAsync();


    }

}
