﻿@page "/UploadFile"
@using LPPermission.UI.Services
@using TelerikUI_App.Models
@using LPPermission.UI.Shared
@inject AuthService AuthService
@inject NavigationManager NavigationManager
@inject AuthService authser

<head>
    <link href="css/uploadfile.css" rel="stylesheet" />
</head>
<div class="top-row px-4">
    <NavMenu />
</div>
<div class="page-content">
    
    <!-- File Upload Section -->
    <div class="file-upload-container">
        <div class="dropdown-container">
        <div class="dropdown-row">
            <div class="dropdown-item">
            <label for="geoDropdown" class="k-form-label">Select Geo:</label>
            <TelerikDropDownList Data="@Geos"
                                 TextField="Name"
                                 ValueField="GeoId"
                                 @bind-Value="SelectedGeoId"
                                 OnChange="@OnGeoChanged"
                                 Class="form-control" />
                                 </div>
            <div class="dropdown-item">
            <label for="containerDropdown" class="k-form-label">Select Container:</label>
           
                <TelerikDropDownList Data="@Containers"
                                     TextField="ContainerName"
                                     ValueField="ContainerId"
                                     @bind-Value="SelectedContainerId"
                                     Class="form-control" />
               
        </div>
        </div>
        </div>
        <h3 class="k-form-label">File Upload</h3>
        <TelerikUpload Multiple=false
                       AllowedExtensions="@(new List<string>() { ".csv" })"
                       SaveUrl="@(ApiSettings.Value.UploadSaveUrl)"
                       RemoveUrl=""
                       MaxFileSize="@(ApiSettings.Value.MaxFileSize)"
                       MinFileSize="@(ApiSettings.Value.MinFileSize)"
                       AutoUpload="true"
                       OnCancel="@OnUploadCancel"
                       OnClear="@OnUploadClear"
                       OnError="@OnUploadError"
                       OnProgress="@OnUploadProgress"
                       OnRemove="@OnUploadRemove"
                       OnSelect="@OnUploadSelect"
                       OnSuccess="@OnUploadSuccess"
                       OnUpload="@OnUpload">
        </TelerikUpload>
        <div class="k-form-hint">Accepted files: <strong>CSV</strong></div>

        <!-- Status Message -->
        <div class="status-message">
            @statusMessage
        </div>
    </div>
</div>
@code
{
    // private bool _isInitialized;

    // protected override async Task OnInitializedAsync()
    // {
    //     if (!authser.IsAuthenticated)
    //     {
    //         // User is authenticated, proceed with initialization
    //         NavigationManager.NavigateTo("/");
    //     }
    // }

} 