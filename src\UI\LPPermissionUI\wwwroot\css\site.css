﻿@import url('open-iconic/font/css/open-iconic-bootstrap.min.css');

html, body, app, .main {
    height: 100%;
    min-height: 100%;
    margin: 0;
}

html, body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

a, .btn-link {
    color: #0366d6;
}

.btn-primary {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}

.top-row {
    min-height: 3.5rem;
    display: flex;
    align-items: center;
}

.main {
    display: flex;
    flex-direction: column;
}

    .main .top-row {
        background-color: #f8f9fa;
        border-bottom: 1px solid #d6d5d5;
        justify-content: flex-end;
    }

        .main .top-row > a, .main .top-row .btn-link {
            white-space: nowrap;
            margin-left: 1.5rem;
        }

        .main .top-row a:first-child {
            overflow: hidden;
            text-overflow: ellipsis;
        }

.content {
    padding-top: 1.1rem;
    flex: auto;
    overflow-y: auto;
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid red;
}

.validation-message {
    color: red;
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
