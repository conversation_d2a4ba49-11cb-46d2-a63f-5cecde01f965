# ==========================================================================
# Copyright (C) 1989-2020 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Show-SCBwcManagerArchivers {
    <#
    .Synopsis
        Used to retrieve Bwc Manager Archivers
    .DESCRIPTION
        This method will return theassociated archivers forthe BWC Manager role.
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        Show-SCBwcManagerArchivers -Id "BWCManagerGuid"
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "AssociatedArchivers" -RelationId $ModuleName
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCBwcManagerArchiver {
    <#
    .Synopsis
        Used to remove an archiver from Bwc Manager Associated Archivers
    .DESCRIPTION
        This method will remove an archiver from the BWC Manager role
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        Remove-SCBwcManagerArchiver -Id "BwcManagerGuid" -ArchiverGuid "ArchiverGuid"

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] $ArchiverGuid 
    )

    Begin {
    }

    Process {

        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/AssociatedArchivers/$ArchiverGuid"

            InvokeSCRestMethod -UriSuffix $uri -Method "Delete" -Body (@{} | ConvertTo-Json)
        }
    }
}
# -----------------------------------------------------------------------------
Function Add-SCBwcManagerArchiver {
    <#
    .Synopsis
        Used to Add an archiverto an BwcManager
    .DESCRIPTION
        This method will add an archiver to the BWC Manager role
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        Add-SCBwcManagerArchiver -Id "BwcManagerGuid" -ArchiverGuid "ArchiverGuid"

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("aid")] $ArchiverGuid 
    )
    begin {
    }
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $Role = GetIdFromObject $RoleId
            $Archiver = GetIdFromObject $ArchiverGuid 

            $uri = "Entities/$Role/AssociatedArchivers/"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", $Archiver)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}