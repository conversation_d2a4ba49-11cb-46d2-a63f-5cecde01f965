﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCAreaMember {
    <#
    .Synopsis
        Method used to add a member to the given area
    .DESCRIPTION
        This Method will allow the user to add a security center entity as a member of a security center area
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AreaId will be used to specify the area we want to add the entity to

        The parameter MemberId represents the Id of the entity we want to add to the member list of the area
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = New-SCArea -n "MyNewArea"
        
        #Will create a new door
        $door = New-SCDoor -n "MyNewDoor"

        Add-SCAreaMember -AreaId $area -MemberId $door
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = nsa -n "MyNewArea"
        
        #Will create a new door
        $door = nsd -n "MyNewDoor"

        Add-SCAreaMember -Id $area -mid $door
        
        Exit-SCSession 

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AreaId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("mid")] $MemberId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $mid = GetIdFromObject $MemberId
            Set-SCEntityRelation -EntityId $AreaId -RelationName "members" -RelationId $mid   
        }
    }  
}

# -----------------------------------------------------------------------------
Function Add-SCAreaAccessPoint {
    <#
    .Synopsis
        Method used to add an access point to the given area
    .DESCRIPTION
        This Method will allow the user to add a security center access point to a specified area
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AreaAccessPointId will be used to specify the area access point we want to add the access point to

        The parameter AccessPointId represents the Id of the access point we want to add to the area access point
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $area = New-SCArea -n "MyNewArea" | Get-SCArea

        $door = new-scdoor -n "Door A"
        $doorSides = (Get-SCDoorDoorSides $door) | Get-SCEntity

        $doorIn = $doorSides | Where-Object { $_.AccessPointSide -eq "Alpha" }
        $doorOut =  $doorSides | Where-Object { $_.AccessPointSide -eq "Omega" }

        Add-SCAreaAccessPoint -AreaAccessPointId $($area.EntryAccessPoint) -AccessPointId $doorIn
        Add-SCAreaAccessPoint -AreaAccessPointId $($area.ExitAccessPoint) -AccessPointId $doorOut
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("aapId")] $AreaAccessPointId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("apId")] $AccessPointId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $apId = GetIdFromObject $AccessPointId
            Set-SCEntityRelation -EntityId $AreaAccessPointId -RelationName "AccessPoints" -RelationId $apid   
        }
    } 
}

# -----------------------------------------------------------------------------
Function Add-SCAreaArea {
    <#
    .Synopsis
        Method used to add an area as a parent of the given area
    .DESCRIPTION
        This Method will allow the user to add a security center area as a parent of another security center area
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AreaId will be used to specify the area we want to add the parent area

        The parameter AreaIdToAdd represents the Id of the area we want to add as a parent of the given area
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = New-SCArea -n "Area"
        
        #Will create a new area
        $areaParent = New-SCArea -n "Area Parent"

        Add-SCAreaArea -AreaId $area -AreaIdToAdd $areaParent
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = New-SCArea -n "Area"
        
        #Will create a new area
        $areaParent = New-SCArea -n "Area Parent"

        Add-SCAreaArea -Id $area -aid $areaParent
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AreaId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("aid")] $AreaIdToAdd
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $aid = GetIdFromObject $AreaIdToAdd
            Set-SCEntityRelation -EntityId $AreaId -RelationName "areas" -RelationId $aid   
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias gsa Get-SCArea
Function Get-SCArea {
    <#
    .Synopsis
        This method will return all the properties of the area represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the area represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The AreaId parameter represents the Id of the area to retrieve (The guid representing the area in the Security Center System)
        You can also pass any area object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        #Will create a new area
        $area = New-SCArea -n "myArea"

        #this will get the area config
        Get-SCArea -AreaId $area.Id

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        New-SCArea -n -n "area" | Get-SCArea

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        #this will get the admin users properties
        nsa "myNewArea" | gsa

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AreaId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AreaId          
        }
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCAreaAccessPoints {
    <#
    .Synopsis
        Method used to get all access points of the given area
    .DESCRIPTION
        This Method will allow the user to get all access points of the given area
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AreaAccessPointId will be used to specify the area access point we want to get the access points from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $area = New-SCArea -n "MyNewArea" | Get-SCArea

        $door = new-scdoor -n "Door A"
        $doorSides = (Get-SCDoorDoorSides $door) | Get-SCEntity

        $doorIn = $doorSides | Where-Object { $_.AccessPointSide -eq "Alpha" }
        $doorOut =  $doorSides | Where-Object { $_.AccessPointSide -eq "Omega" }

        Add-SCAreaAccessPoint -AreaAccessPointId $($area.EntryAccessPoint) -AccessPointId $doorIn
        Add-SCAreaAccessPoint -AreaAccessPointId $($area.ExitAccessPoint) -AccessPointId $doorOut
        
        Get-SCAreaAccessPoints -AreaAccessPointId $($area.EntryAccessPoint)

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("aapId")] $AreaAccessPointId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $AreaAccessPointId -RelationName "AccessPoints"
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCAreaMembers {
    <#
    .Synopsis
        Method used to get the members of the given area
    .DESCRIPTION
        This Method will allow the user to retrieve the members of a security center area
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AreaId will be used to specify the area we want to retrieve the members of
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = New-SCArea -n "MyNewArea"
        
        #Will create a new door
        $door = New-SCDoor -n "MyNewDoor"

        Add-SCAreaMember -AreaId $area -MemberId $door

        Get-SCAreaMembers -AreaId $area
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = nsa -n "MyNewArea"
        
        #Will create a new door
        $door = nsd -n "MyNewDoor"

        Add-SCAreaMember -Id $area -mid $door

        Get-SCAreaMembers $area
        
        Exit-SCSession 

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AreaId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AreaId -RelationName "members"
        }
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCAreaAreas {
    <#
    .Synopsis
        Method used to get the parent area of the given area
    .DESCRIPTION
        This Method will allow the user get the security center parent area of another security center area
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AreaId will be used to specify the area we want to retrieve the parent areas from

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = New-SCArea -n "Area"
        
        #Will create a new area
        $areaParent = New-SCArea -n "Area Parent"

        Add-SCAreaArea -AreaId $area -AreaIdToAdd $areaParent

        Get-SCAreaAreas -AreaId $area
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = New-SCArea -n "Area"
        
        #Will create a new area
        $areaParent = New-SCArea -n "Area Parent"

        Add-SCAreaArea -AreaId $area -AreaIdToAdd $areaParent

        Get-SCAreaAreas $area
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AreaId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AreaId -RelationName "areas"
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias nsa New-SCArea
Function New-SCArea {
    <#
    .Synopsis
        Will create a new areas with the provided name
    .DESCRIPTION
        This method will create an new area in Security Center with the given name.  The return value will contain the Id of the new area
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new area upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type area with the name MyNewArea
        New-SCArea -n "MyNewArea"
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type area with the name MyNewArea
        nsa "MyNewArea"
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation "New-SCArea "{
            New-SCEntity -n $Name -t Areas
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias rsa Remove-SCArea
Function Remove-SCArea {
    <#
    .Synopsis
        Will remove the area represented by the provided AreaId from Security Center
    .DESCRIPTION
        This method will permanently remove the specified area from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The AreaId parameter represents the entity to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $newArea = New-SCArea -Name "MyArea"

        #remove the new user by providing the user object
        Remove-SCArea -id $newArea
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $newArea = nsa "MyArea"

        #remove the new user by providing the user object
        rsa $newArea
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AreaId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $AreaId
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCAreaAccessPoint {
    <#
    .Synopsis
        Method used to remove an access point from the given area
    .DESCRIPTION
        This Method will allow the user to remove a security center access point from a specified area
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AreaAccessPointId will be used to specify the area access point we want to remove the access point from

        The parameter AccessPointId represents the Id of the access point we want to remove from the area access points
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $area = New-SCArea -n "MyNewArea" | Get-SCArea

        $door = new-scdoor -n "Door A"
        $doorSides = (Get-SCDoorDoorSides $door) | Get-SCEntity

        $doorIn = $doorSides | Where-Object { $_.AccessPointSide -eq "Alpha" }
        $doorOut =  $doorSides | Where-Object { $_.AccessPointSide -eq "Omega" }

        Add-SCAreaAccessPoint -AreaAccessPointId $($area.EntryAccessPoint) -AccessPointId $doorIn
        Add-SCAreaAccessPoint -AreaAccessPointId $($area.ExitAccessPoint) -AccessPointId $doorOut

        Remove-SCAreaAccessPoint -AreaAccessPointId $($area.EntryAccessPoint) -AccessPointId $doorIn
        Remove-SCAreaAccessPoint -AreaAccessPointId $($area.ExitAccessPoint) -AccessPointId $doorOut

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("aapId")] $AreaAccessPointId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("apid")] $AccessPointId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $apid = GetIdFromObject $AccessPointId
            Remove-SCEntityRelation -EntityId $AreaAccessPointId -RelationName "AccessPoints" -RelationId $apid  
        }
    }   
}

# -----------------------------------------------------------------------------
Function Remove-SCAreaMember {
    <#
    .Synopsis
        Method used to remove a member of the given area
    .DESCRIPTION
        This Method will allow the user to remove a security center entity from the member list of a security center area
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AreaId will be used to specify the area we want to remove the entity from

        The parameter MemberId represents the Id of the entity we want to remove from the member list of the area
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = New-SCArea -n "MyNewArea"
        
        #Will create a new door
        $door = New-SCDoor -n "MyNewDoor"

        Add-SCAreaMember -AreaId $area -MemberId $door

        Remove-SCAreaMember -AreaId $area -MemberId $door
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = nsa -n "MyNewArea"
        
        #Will create a new door
        $door = nsd -n "MyNewDoor"

        Add-SCAreaMember -Id $area -mid $door

        Remove-SCAreaMember -Id $area -mid $door
        
        Exit-SCSession 

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AreaId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("mid")] $MemberId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $mid = GetIdFromObject $MemberId
            Remove-SCEntityRelation -EntityId $AreaId -RelationName "members" -RelationId $mid   
        }
    }  
}

# -----------------------------------------------------------------------------
Function Remove-SCAreaArea {
    <#
    .Synopsis
        Method used to remove an area as a parent of the given area
    .DESCRIPTION
        This Method will allow the user to remove a security center area as a parent of another security center area
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AreaId will be used to specify the area we want to remove the parent area from

        The parameter AreaIdToRemove represents the Id of the area we want to remove as a parent of the given area
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = New-SCArea -n "Area"
        
        #Will create a new area
        $areaParent = New-SCArea -n "Area Parent"

        Add-SCAreaArea -AreaId $area -AreaIdToAdd $areaParent

        Remove-SCAreaArea -AreaId $area -AreaIdToAdd $areaParent
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new area
        $area = New-SCArea -n "Area"
        
        #Will create a new area
        $areaParent = New-SCArea -n "Area Parent"

        Add-SCAreaArea -Id $area -aid $areaParent

        Remove-SCAreaArea -Id $area -aid $areaParent
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AreaId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("mid")] $AreaIdToRemove
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $aid = GetIdFromObject $AreaIdToRemove
            Remove-SCEntityRelation -EntityId $AreaId -RelationName "areas" -RelationId $aid   
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias ssa Set-SCArea
Function Set-SCArea() {
    <#
    .Synopsis
        Used to update the properties of an area in Security Center
    .DESCRIPTION
        This method is used to update the properties of an area to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Area represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newArea = New-SCArea -Name "MyArea"  | gsa
        $newArea.Description = "test"
        
        Set-SCArea -Area $newArea

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newArea = New-SCArea -Name "MyArea" | gsa
        $newArea.Description = "test"
        
        ssa $newArea

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("a")] $Area
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Area
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCAreaActivityReport {
    <#
    .Synopsis
        Used to retrieve an area activity report
    .DESCRIPTION
        This method will return an area activity report for all the activities of the specified areas.  The report will contain a Results property
        that contains 2 arrays.  The first array will be the column definition.  This will contain the information that will be in each rows.
        The second array is the actual data of the report matching the column definition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The area parameter represents the Id of the area to retrieve (The guid representing the area in the Security Center System)
        You can also pass any area object that contains an ID as a parameter
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Show-SCAreaActivityReport $area

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $Area
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $aid = GetIdFromObject $Area
            $uri = "Reports/AreaActivities?Areas="  + $aid
            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        }
    }  
}

# -----------------------------------------------------------------------------
Function Show-SCAreaProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an area
    .DESCRIPTION
        This method will list the supported properties and relation of an area (the data model, not the actual data).  This method is used
        when you want to know what is available for a given area
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCAreaProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiArea" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}


Export-ModuleMember -Function '*-*' -Alias '*'