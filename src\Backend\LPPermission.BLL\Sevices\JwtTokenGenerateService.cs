﻿using LPPermission.BLL.Interfaces;
using LPPermission.BLL.Model;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;


/// <summary>
/// Service for generating and managing JWT access and refresh tokens.
/// </summary>
public class JwtTokenGenerateService : IJwtTokenGenerateService
{
    private readonly IConfiguration _configuration;
    private readonly SymmetricSecurityKey _key;
    private readonly TimeSpan _accessTokenExpiration;
    private readonly TimeSpan _refreshTokenExpiration;

    /// <summary>
    /// Constructor to initialize the service with configuration settings.
    /// </summary>
    
    public JwtTokenGenerateService(IConfiguration configuration)
    {
        _configuration = configuration;
        var key = _configuration["JwtSecretKey"];
        _key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(key));
        _accessTokenExpiration = TimeSpan.Parse(_configuration["JwtAccessTokenExpiration"]);
        _refreshTokenExpiration = TimeSpan.Parse(_configuration["JwtRefreshTokenExpiration"]);
    }

    public (string AccessToken, string RefreshToken) GenerateTokens(LoginModel login)
    {
        var accessToken = GenerateAccessToken(login);
        var refreshToken = GenerateRefreshToken();

        // Store refresh token securely (e.g., in a database or cache) for later validation
        StoreRefreshToken(login.UserName, refreshToken);

        return (AccessToken: accessToken, RefreshToken: refreshToken);
    }

    /// <summary>
    /// Method to Generate the Access Token
    /// </summary>
    /// <param name="login"></param>
    /// <returns></returns> <summary>

    public string GenerateAccessToken(LoginModel login)
    {
        var claims = new[]
        {
            new Claim(ClaimTypes.Name, login.UserName),
            new Claim(ClaimTypes.NameIdentifier, login.Password),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
        };

        var credentials = new SigningCredentials(_key, SecurityAlgorithms.HmacSha256);
        var expires = DateTime.UtcNow.Add(_accessTokenExpiration);

        var token = new JwtSecurityToken(
            issuer: _configuration["JwtIssuer"],
            audience: _configuration["JwtAudience"],
            claims: claims,
            expires: expires,
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    /// <summary>
    /// Method to generate the Refresh Token 
    /// </summary>
    /// <returns></returns>
    public string GenerateRefreshToken()
    {
        return Guid.NewGuid().ToString();  // Generate a unique refresh token
    }
    
    /// <summary>
    ///  Method to store the refresh token (in a database or cache)
    /// </summary>
    /// <param name="username"></param>
    /// <param name="refreshToken"></param> <summary>
    private void StoreRefreshToken(string username, string refreshToken)
    {
        // This is a placeholder. You should store the refresh token securely.
        // Example: Save to a database or a cache system (e.g., Redis).
    }

    // Method to validate refresh token and issue a new access token
    public (string AccessToken, string RefreshToken) RefreshAccessToken(string refreshToken)
    {
        // Validate the refresh token against the stored one in the database or cache
        if (!IsValidRefreshToken(refreshToken))
        {
            throw new UnauthorizedAccessException("Invalid refresh token.");
        }

        // Generate new access and refresh tokens
        var newAccessToken = GenerateAccessToken(new LoginModel { UserName = "user_from_refresh_token" });
        var newRefreshToken = GenerateRefreshToken();

        // Store the new refresh token
        StoreRefreshToken("user_from_refresh_token", newRefreshToken);

        return (newAccessToken, newRefreshToken);
    }

    /// <summary>
    /// Method to check the token is valid
    /// </summary>
    /// <param name="refreshToken"></param>
    /// <returns></returns> <summary>
    private bool IsValidRefreshToken(string refreshToken)
    {
        // You would check if the provided refresh token matches the stored one for the user.
        // Example: Retrieve from database/cache.
        return true; // Placeholder for actual validation
    }
}
