﻿using System;
using System.Collections.Generic;

namespace LPPermission.DAL.Models;

public partial class UserGroup
{
    public int UserGroupId { get; set; }

    public string? Description { get; set; }

    public int? ParentUserGroupId { get; set; }

    public string? Name { get; set; }

    public int? ContainerId { get; set; }

    public int? GeoId { get; set; }

    public string? Createby { get; set; }

    public DateTime? Createdate { get; set; }

    public string? Updateby { get; set; }

    public DateTime? Updatedate { get; set; }

    public virtual Container? Container { get; set; }

    public virtual Geo? Geo { get; set; }

    public virtual ICollection<UserGroup> InverseParentUserGroup { get; set; } = new List<UserGroup>();

    public virtual UserGroup? ParentUserGroup { get; set; }

    public virtual ICollection<Permission> Permissions { get; set; } = new List<Permission>();

    public virtual ICollection<StoreUserGroup> StoreUserGroups { get; set; } = new List<StoreUserGroup>();
}
