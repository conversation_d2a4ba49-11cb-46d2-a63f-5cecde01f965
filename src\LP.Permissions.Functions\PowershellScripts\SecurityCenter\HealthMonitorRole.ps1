﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Get-SCHealthMonitorRole {
    <#
    .Synopsis
        This method will get the Health monitoring role
    .DESCRIPTION
        This method is used when the user wants to get Health monitoring role
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $HMRole = Get-SCHealthMonitorRole
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    Begin {
    }
    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCRoles -t HealthMonitoring -Filter All
        }
    }   
}

# -----------------------------------------------------------------------------
Function Show-SCHealthMonitorEventReport {
    <#
    .Synopsis
        Used to retrieve an health monitoring report
    .DESCRIPTION
        This method will return a health monitoring report for all the health event types.  The report will contain a Results property
        that contains 2 arrays.  The first array will be the column definition.  This will contain the information this will be in each rows.
        The second array is the actual data of the report matching the column definition

        The HeathEventType parameter contains the health event type we want to show in the report
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        Get-SCEnum ApiHealthEventType

        Show-SCHealthMonitorEventReport ServiceStarted

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param ()

    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'HeathEventType'
        $ParameterAlias = 't'
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $true
        $ParameterAttribute.Position = 0

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        # Generate and set the ValidateSet 
        $sess = GetSession -Quiet $true
        if ($sess -ne $null) {
            $arrSet = $sess.SCHealthEventTypeCache

            $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection.Add($ValidateSetAttribute)
        }

         #add the alias to the attributes collection
        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)
        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $HeathEventType = $PsBoundParameters[$ParameterName]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {  
            $uri = "Reports/HealthEvents/?HealthEvents=$HeathEventType"
            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'
