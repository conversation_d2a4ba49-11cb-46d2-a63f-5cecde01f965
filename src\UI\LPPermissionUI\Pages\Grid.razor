﻿@page "/grid"

@using LPPermission.UI.Models
@using LPPermission.UI.Shared
@using TelerikUI_App.Models


<head>
    <link href="css/grid.css" rel="stylesheet" />
</head>

<div class="top-row px-4">
    <NavMenu />
</div>

<div class="grid-page">
    <h1 class="grid-header">User Groups and Permissions</h1>
    <button ButtonType="@ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" class="k-button"
        @onclick="CallFunctionApp" title="Click to call the function app">Sync Genetec Data</button>
    <div class="grid-container">
        <TelerikGrid Class="grid" Data="@Privileges" OnUpdate="@UpdateHandler" OnDelete="@DeleteHandler"
            OnCreate="@CreateHandler" ConfirmDelete="true" Pageable="true" Sortable="true" Groupable="true"
            FilterMode="GridFilterMode.FilterMenu" Resizable="true" Reorderable="true" EditMode="GridEditMode.Popup"
            SelectionMode="GridSelectionMode.Multiple" PageSize="@PageSize" Navigable="true">
            <GridColumns>

                <GridColumn Field="@nameof(PrivilegeDto.GeoName)" Title="Geo Name" Width="100px" />
                <GridColumn Field="@nameof(PrivilegeDto.UserGroupName)" Title="User Group Name" Width="150px" />
                <GridColumn Field="@nameof(PrivilegeDto.MenuName)" Title="Menu Name" Width="200px" />
                <GridColumn Field="@nameof(PrivilegeDto.PrivilegeName)" Title="Privilege Name" Width="200px" />
                <GridColumn Field="@nameof(PrivilegeDto.StateName)" Title="State" Width="100px" />
            </GridColumns>
        </TelerikGrid>
    </div>
</div>