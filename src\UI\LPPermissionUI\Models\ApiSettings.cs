﻿namespace LPPermission.UI.Models
{
    public class ApiSettings
    {
        public string GetPermissionUrl { get; set; }
        public string UploadSaveUrl { get; set; }
        public string UploadRemoveUrl { get; set; }
        public string FunctionAppUrl { get; set; }
        public string LoginUrl { get; set; }
        public int MaxFileSize { get; set; } // File size in bytes
        public int MinFileSize { get; set; }
        public int PageSize { get; set; }
    }
}
