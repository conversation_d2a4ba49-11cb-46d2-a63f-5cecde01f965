﻿using LPPermission.DAL.Models;
using LPPermission.Models;
using System.Linq.Expressions;

namespace LPPermission.DAL.Interfaces
{
    public interface IGenericRepository<T> where T : class
    {
        Task<T> GetByConditionAsync(Expression<Func<T, bool>> predicate);
        IQueryable<Menu> GetByConditionAsyncMenu(Expression<Func<Menu, bool>> predicate);
        Task AddAsync(T entity);
        Task SaveAsync();
        IQueryable<Privilege> GetByConditionAsyncPrivelege(Expression<Func<Privilege, bool>> predicate);
        IQueryable<Permission> GetByConditionAsyncPermission(Expression<Func<Permission, bool>> predicate);
        IQueryable<UserGroup> GetByConditionAsyncUserGroup(Expression<Func<UserGroup, bool>> predicate);
        Task<bool> ValidateUserLoginAsync(string username, string password);
        Task<IEnumerable<State>> GetAllStatesDirectAsync();
        Task<List<PermissionDto>> GetAllPermissions();
        Task DeletePermissions();
        Task<List<GeoDto>> GetAllGeosAsync();
        IQueryable<Geo> GetAllGeos();
        IQueryable<Container> GetContainersByGeoId(int geoId);
        Task<List<ContainerDto>> GetContainersByGeoIdAsync(int geoId);

    }
}
