﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Management.Automation;
using Microsoft.ApplicationInsights;
using SetLPPermissions.Utilities;
using Microsoft.ApplicationInsights.Extensibility;
using System.Text;

namespace SetLPPermissions.Services
{
    public class PowerShellHandlerService
    {       
        private  PowerShell ps = PowerShell.Create();
        private  readonly TelemetryClient telemetryClient;
        private readonly ILogger<PowerShellHandlerService> _logger;
        private readonly CircuitBreaker _circuitBreaker;
        private static string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
        private static string logFilePath = Path.Combine(baseDirectory, "ps_logfile.log");

        public PowerShellHandlerService(ILogger<PowerShellHandlerService> logger, TelemetryConfiguration telemetry)
        {
            _logger = logger;
            telemetryClient = new TelemetryClient(telemetry);
            _circuitBreaker = new CircuitBreaker(3, TimeSpan.FromMinutes(5));
        }
        public string Command(string scriptPath)
        {
            string errorMessage = string.Empty;
            string outputMessage = string.Empty;

            // PowerShell script file
            string scriptFullPath = Path.Combine(baseDirectory, scriptPath);
            string script = System.IO.File.ReadAllText(scriptFullPath);

            ps.AddScript(script);
            ps.AddCommand("Out-String");

            PSDataCollection<PSObject> output = new();
            ps.Streams.Error.DataAdded += (object? sender, DataAddedEventArgs e) =>
            {
                var errorRecord = ((PSDataCollection<ErrorRecord>?)sender)?[e.Index];
                if (errorRecord != null)
                {
                    errorMessage = errorRecord.ToString();
                    telemetryClient.TrackException(new Exception(errorMessage));
                    LogToFile($"Error: {errorMessage}");
                }
            };

            IAsyncResult async = ps.BeginInvoke<PSObject, PSObject>(null, output);
            ps.EndInvoke(async);

            StringBuilder stringBuilder = new();

            foreach (var outputItem in output)
            {
                stringBuilder.AppendLine(outputItem.BaseObject.ToString());
            }

            // Clearing up PowerShell run space for next run
            ps.Commands.Clear();

            if (!string.IsNullOrEmpty(errorMessage))
            {
                telemetryClient.TrackEvent("PowerShellCommandFailed", new Dictionary<string, string>
                {
                    { "ScriptPath", scriptPath },
                    { "ErrorMessage", errorMessage }
                });
                LogToFile($"PowerShellCommandFailed: ScriptPath={scriptPath}, ErrorMessage={errorMessage}");
                //throw new Exception(errorMessage); // Throw an exception on failure
                return errorMessage;
            }

            string outputMessageTrimmed = stringBuilder.ToString().Trim();
            telemetryClient.TrackEvent("PowerShellCommandSucceeded", new Dictionary<string, string>
            {
                { "ScriptPath", scriptPath },
                { "Output", outputMessageTrimmed }
            });
            LogToFile($"PowerShellCommandSucceeded: ScriptPath={scriptPath}, Output={outputMessageTrimmed}");

            return outputMessageTrimmed;
        }
        public void LogToFile(string message)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(logFilePath, true))
                {
                    writer.WriteLine($"{DateTime.Now}: {message}");
                }
            }
            catch (Exception ex)
            {
                telemetryClient.TrackException(ex);
            }
        }
        public async Task<string> ExecutePowerShellScript(string scriptPath, Dictionary<string, object> parameters=null)
        {
            string errorMessage = string.Empty;
            string outputMessage = string.Empty;

            // PowerShell script file
            string scriptFullPath = Path.Combine(baseDirectory, scriptPath);
            string script = System.IO.File.ReadAllText(scriptFullPath);
            return await _circuitBreaker.ExecuteAsync(async () =>
            {
                if(!File.Exists(script))
                {
                    throw new FileNotFoundException("PowerShell script not found", script);
                }
                try
                {
                    ps.AddScript(script);
                    foreach (var parameter in parameters)
                    {
                        ps.AddParameter(parameter.Key, parameter.Value);
                    }
                    var results = await ps.InvokeAsync();
                    var output = new StringBuilder();
                    foreach (var result in results)
                    {
                        output.AppendLine(result.ToString());
                    }
                    return output.ToString();
                }
                catch (Exception ex)
                {
                    telemetryClient.TrackException(ex);
                    _logger.LogError(ex, "Error executing PowerShell script");
                    throw;
                }
                finally
                {
                    ps.Commands.Clear();
                }
            });
        }
    }
}
