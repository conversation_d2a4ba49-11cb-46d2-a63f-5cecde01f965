param (
    [string]$IPAddress="**************",
    [string]$Username="Samurai",
    [string]$Credential="Password1!",
    [string]$ServerPassword="",
    [string]$LogFilePath="C:\Users\<USER>\source\repos\DurableFunctionNew\retry_logfile.log",
    [string]$InputFilePath="C:\Users\<USER>\source\repos\DurableFunctionNew\input.txt"
    
)

function Write-Message {
    param (
        [string]$message
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Add-Content -Path $logFilePath -Value "$timestamp - $message"
}


# Initialize the output file
#if (Test-Path $InputFilePath) {
 #   Remove-Item $InputFilePath
#}

 try {
        if (-not $IPAddress) {
            throw "ComputerName parameter is missing."
        }

        # Read the credentials from the JSON file
        #$credentialsPath = "C:\Workspace\maliha_code\src\FunctionApp\credentials.json"
        #$credentials = Get-Content -Path $credentialsPath | ConvertFrom-Json

        $directoryPassword = $Credential
        $genetecServerPassword = $ServerPassword

        # Attempt to enter the SC session
        Write-Message "Attempting to enter SC session for $IPAddress..."
        Enter-SCSession -ComputerName $IPAddress -User $Username -DirectoryPassword $directoryPassword -GenetecServerPassword $genetecServerPassword

        # Check if the session was established
        if ($?) {
            Write-Message "SC session established successfully for $ComputerName."

            # Read the privileges data from the JSON file
        $privilegesData = Get-Content -Path $InputFilePath | ConvertFrom-Json

        foreach ($group in $privilegesData) {
                $GroupID = $group.GroupID
                $GroupName = $group.GroupName
                $Privileges = $group.Privileges

                    foreach ($privilege in $Privileges) {
                        $PrivilegeID = $privilege.PrivilegeID
                        $PrivilegeName = $privilege.PrivilegeName
                        $PreviousState = $privilege.previousState
                        $State = $privilege.State

                    # Check and set the privilege state if different
                    if ($PreviousState -ne $State) {
                        Write-Message "Updating privilege state for UserGroup: $GroupName (ID: $GroupID), Privilege ID: $PrivilegeID from $PreviousState to $State"
                        Set-SCUserGroupPrivileges -UserGroupId $GroupID -PrivilegeId $PrivilegeID -PrivilegeState $State
                        Write-Message "Privilege state updated to '$State' for UserGroup: $GroupName (ID: $GroupID), Privilege ID: $PrivilegeID"
                    } else {
                        Write-Message "Privilege state for UserGroup: $GroupName (ID: $GroupID), Privilege ID: $PrivilegeID is already '$State'"
                    }
            }

        }
         Write-Message "Privileges for all user groups have been updated."

        Exit-SCSession
        Write-Message "SC session exited successfully for $IPAddress."
        } else {
            $errorMessage = "Failed to establish SC session for $IPAddress."
            Write-Error $errorMessage
            Write-Message $errorMessage
            Exit-SCSession
        }
        
    } catch {
        $errorMessage = "An error occurred for $IPAddress $($_.Exception.Message)"
        Write-Error $errorMessage
        Write-Message $errorMessage
        Exit-SCSession
    }