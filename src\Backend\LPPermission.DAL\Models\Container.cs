﻿using System;
using System.Collections.Generic;

namespace LPPermission.DAL.Models;

public partial class Container
{
    public int ContainerId { get; set; }

    public string? Name { get; set; }

    public int GeoId { get; set; }

    public string? Description { get; set; }

    public string Identifier { get; set; } = null!;

    public virtual Geo Geo { get; set; } = null!;

    public virtual ICollection<Permission> Permissions { get; set; } = new List<Permission>();

    public virtual ICollection<Store> Stores { get; set; } = new List<Store>();

    public virtual ICollection<UserGroup> UserGroups { get; set; } = new List<UserGroup>();
}
