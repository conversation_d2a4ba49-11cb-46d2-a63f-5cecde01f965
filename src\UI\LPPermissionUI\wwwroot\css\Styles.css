﻿/* General Body Styling */
body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f7fb;
    color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    overflow: hidden; /* Avoid scrollbars */
}

/* Styling the form container */
.form-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 30px; /* Reduced padding */
    max-width: 350px; /* Reduced max width */
    width: 100%;
    box-sizing: border-box;
    margin-top: 0; /* Ensure no top margin */
    margin-bottom: 0; /* Ensure no bottom margin */
    overflow: hidden; /* Prevent scrollbars */
}

/* Apply a clean and modern border style */
.form-container .red-border-left {
    border-left: 5px solid #85151e;
    padding-left: 10px;
}

/* Form field label styling */
.form-group label {
    font-weight: bold;
    color: #555;
    font-size: 12px; /* Reduced font size */
}

/* Input controls (textbox, dropdown, etc.) */
.k-textbox, .k-dropdown, .k-datepicker, .k-textarea {
    width: 100%;
    padding: 6px 10px; /* Reduced padding */
    margin-top: 6px; /* Reduced margin */
    margin-bottom: 16px; /* Reduced margin */
    border-radius: 5px;
    border: 1px solid #ddd;
    font-size: 12px; /* Reduced font size */
    box-sizing: border-box; /* Ensures padding and border are included in width */
    height: 32px; /* Reduced height of the textboxes */
}

.error-message {
    color: red;
    font-weight: bold;
    margin-top: 10px;
}

/* Focus and Hover States Removed */
.k-textbox:focus, .k-dropdown:focus, .k-datepicker:focus, .k-textarea:focus {
    border-color: #ddd; /* Removed blue color on focus */
    outline: none;
}

/* Styling the Submit Button */
.k-button {
    background-color: #85151e;
    color: white;
    border: none;
    padding: 10px 18px; /* Reduced padding */
    border-radius: 5px;
    font-size: 14px; /* Reduced font size */
    cursor: pointer;
    width: 100%;
}

.k-button:hover {
    background-color: #6a0c12;
}

/* Styling the Validation Error Messages */
.k-invalid-msg {
    color: #d32f2f; /* Error color */
    font-size: 12px; /* Matching font size */
    margin-top: 6px; /* Reduced margin */
    padding: 0; /* Adjust padding to prevent overflow */
    font-family: 'Arial', sans-serif; /* Same font as input fields */
}

/* Optional: Additional styling for required field indicator */
.k-required {
    color: #d32f2f;
}

/* Logo styling */
.logo {
    text-align: center;
    margin-bottom: 20px;
}

.logo img {
    width: 120px; /* Reduced logo size */
    height: auto;
}
