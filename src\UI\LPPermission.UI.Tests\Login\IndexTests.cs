using Bunit;
using LPPermission.UI.Services;
using LPPermission.UI.Services.Interface;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using TelerikUI_App.Models;

public class IndexTests : TestContext
{
    [Fact]
    public void OnInitializedAsync_ShouldInitializePerson()
    {
        // Arrange
        var loggerMock = new Mock<ILogger<Index>>();
        var httpClient = new HttpClient();
        Services.AddSingleton(loggerMock.Object);
        Services.AddSingleton(httpClient);

        // Act
        Services.AddSingleton<AuthService>();
        Services.AddSingleton<IJwtTokenStorageService, JwtTokenStorageService>();

        //var protectedSessionStorageMock = new Mock<IProtectedSessionStorage>();
        //Services.AddSingleton(protectedSessionStorageMock.Object);
        Services.AddSingleton<IJwtTokenStorageService, JwtTokenStorageService>();
        RegisterCommonServices();
        var component = RenderComponent<LPPermission.UI.Pages.Index>();
        
          var person = component.Instance.person;

        person.UserName.ShouldBe(string.Empty);
        person.Password.ShouldBe(string.Empty);
    }


        [Fact]
        public async Task HandleValidSubmit_ShouldLogInformationAndNavigateOnSuccess()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<Index>>();
            var httpClient = new HttpClient(new FakeHttpMessageHandler());
            Services.AddSingleton(loggerMock.Object);
            Services.AddSingleton(httpClient);
            RegisterCommonServices();
            var component = RenderComponent<LPPermission.UI.Pages.Index>();
            var person = new Login { UserName = "testuser", Password = "testpassword" };
            component.Instance.person = person;

            // Act
            await component.InvokeAsync(() => component.Instance.HandleValidSubmit());

            // Assert
            //component.Instance.ShowSuccessMessage.ShouldBeTrue();
        }

    [Fact]
    public void GetDefaultPerson_ShouldInitializePersonWithDefaultValues()
    {
        // Arrange
        var loggerMock = new Mock<ILogger<Index>>();
        var httpClient = new HttpClient();
        var keyVaultSecretsMock = new Dictionary<string, string>(); // Mock or default implementation

        Services.AddSingleton(loggerMock.Object);
        Services.AddSingleton(httpClient);
        Services.AddSingleton(keyVaultSecretsMock); // Register the mock KeyVaultSecrets

        RegisterCommonServices();

        var component = RenderComponent<LPPermission.UI.Pages.Index>();

        // Act
        component.Instance.GetDefaultPerson();

        // Assert
        var person = component.Instance.person;
        person.ShouldNotBeNull();
        person.UserName.ShouldBe(string.Empty);
        person.Password.ShouldBe(string.Empty);
    }

    private void RegisterCommonServices()
    {
        var telemetryClientMock = new Mock<ITelemetryClient>();
        Services.AddSingleton(telemetryClientMock.Object);

        var keyVaultSecretsMock = new Dictionary<string, string>(); // Mock or default implementation
        Services.AddSingleton(keyVaultSecretsMock);

        var authServiceMock = new Mock<AuthService>();
        Services.AddSingleton(authServiceMock.Object);

        var JwtServiceMock = new Mock<JwtTokenStorageService>();
        Services.AddSingleton(JwtServiceMock.Object);

        var loginMock = new Mock<ILoginService>();
        Services.AddSingleton(loginMock.Object);

        var apiMock = new Mock<IApiService>();
        Services.AddSingleton(apiMock.Object);
    }

}

//Fake HttpMessageHandler to simulate HttpClient responses
public class FakeHttpMessageHandler : HttpMessageHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, System.Threading.CancellationToken cancellationToken)
        {
            var response = new HttpResponseMessage(System.Net.HttpStatusCode.OK)
            {
                Content = new StringContent("{\"token\":\"fake-jwt-token\"}")
            };
            return Task.FromResult(response);
        }
    }
