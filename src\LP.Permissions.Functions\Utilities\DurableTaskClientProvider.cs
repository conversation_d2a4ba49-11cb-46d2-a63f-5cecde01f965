using Microsoft.DurableTask.Client;

public class DurableTaskClientProvider : IDurableTaskClientProvider
{
    private readonly DurableTaskClient _client;

    public DurableTaskClientProvider(DurableTaskClient client)
    {
        _client = client;
    }

    public DurableTaskClient GetClient()
    {
        return _client;
    }

    public DurableTaskClient GetClient(string? name = null)
    {
        // Assuming the name parameter does not affect the client retrieval in this implementation
        return _client;
    }
}
