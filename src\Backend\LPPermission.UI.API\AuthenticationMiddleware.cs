﻿using Azure.Security.KeyVault.Secrets;
using LPPermission.UI.API.Models;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.Collections.Concurrent;
using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Security.Claims;
using System.Text;

namespace LPPermission.UI.API
{
    public class AuthenticationMiddleware : IFunctionsWorkerMiddleware
    {
        private readonly IConfiguration _configuration;
        private readonly SymmetricSecurityKey _key;
        private readonly ILogger<AuthenticationMiddleware> _logger;
        private static readonly ConcurrentDictionary<string, TokenExpiry> _tokenStore = new ConcurrentDictionary<string, TokenExpiry>();
        private readonly SecretClient _secretClient;
        public AuthenticationMiddleware(IConfiguration configuration)
        {
            _configuration = configuration;
            // var Key = _configuration["JwtSecretKey"]
            _key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JwtSecretKey"]));

            // Initialize SecretClient to interact with Azure Key Vault
            //string keyVaultUri = _configuration["AzureKeyVault:VaultUri"];
            //_secretClient = new SecretClient(new Uri(keyVaultUri), new DefaultAzureCredential());

        }


        public async Task Invoke(FunctionContext context, FunctionExecutionDelegate next)
        {
            // Access the HTTP request data
            var request = await context.GetHttpRequestDataAsync();

            if (request == null)
            {
                await context.GetHttpResponseData().WriteStringAsync("Bad Request: No HTTP request data found.");
                return; // Stop processing here
            }
            if (context.FunctionDefinition.Name == "GetContainers" || context.FunctionDefinition.Name == "GetGeos" || context.FunctionDefinition.Name == "Save" || context.FunctionDefinition.Name == "GetPermission" || context.FunctionDefinition.Name == "Login" || context.FunctionDefinition.Name == "UserLogin" || context.FunctionDefinition.Name == "Login1")
            {
                await next(context);
            }

            else
            {
                // Check if there's an Authorization header
                var authHeader = request.Headers.FirstOrDefault(h => h.Key.Equals("Authorization", StringComparison.OrdinalIgnoreCase)).Value.FirstOrDefault();
                if (authHeader != null)
                {
                    var token = authHeader.Substring("Bearer ".Length).Trim();
                    //if (_tokenStore.ContainsKey(token))
                    //{
                    //    var storedToken = _tokenStore[token];

                    //    // Check if the token is expired
                    //    if (storedToken.ExpiryDate > DateTime.UtcNow)
                    //    {
                    //        // Token is valid, use it
                    //        var principal = new ClaimsPrincipal(new ClaimsIdentity(new[] { new Claim(ClaimTypes.Name, "User") }));
                    //        context.Items["User"] = principal;
                    //        await next(context);
                    //    }
                    //    else
                    //    {
                    //        // If expired, remove the token from the store
                    //        _tokenStore.TryRemove(token, out _);
                    //        await context.GetHttpResponseData().WriteStringAsync("The token has expired. Please log in again.");
                    //    }
                    //}
                    //else
                    //{
                    // Validate the token (example validation method)
                    var principal = ValidateToken(token, context);
                    if (principal != null)
                    {
                        // Store the validated token in the in-memory store with an expiry time
                        // _tokenStore[token] = new TokenExpiry { Token = token, ExpiryDate = DateTime.UtcNow.AddMinutes(30) }; // Example: 30 minutes expiry
                        // If the token is valid, you can set the user in the context if needed
                        context.Items["User"] = principal;

                        // Continue processing to the next middleware or the function itself
                        await next(context);
                    }
                    else
                    {
                        // If token validation fails, set 401 Unauthorized response and return early
                        //context.SetStatusCode(HttpStatusCode.Unauthorized);
                        await context.GetHttpResponseData().WriteStringAsync("Invalid Token");
                        return; // Stop further processing
                    }
                    //}
                }
                else
                {
                    // If the Authorization header is missing, return a 400 Bad Request response
                    //context.SetStatusCode(HttpStatusCode.BadRequest);
                    await context.GetHttpResponseData().WriteStringAsync("No Authorization header provided");
                    return; // Stop further processing
                }
            }
        }
        private ClaimsPrincipal ValidateToken(string token, FunctionContext context)
        {
            try
            {

                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_configuration["JwtSecretKey"]); // Use config for the key
                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ValidateLifetime = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ClockSkew = TimeSpan.Zero
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
                return principal;
            }
            //catch (SecurityTokenExpiredException ex)
            //{
            //    // Handle expired token
            //    _logger.LogWarning($"Token expired: {ex.Message}");

            //    var response = context.GetHttpResponseData();
            //    response.StatusCode = HttpStatusCode.Unauthorized;
            //     response.WriteStringAsync("The token has expired. Please log in again.");
            //    return null;  // This stops further processing of the request.
            //}
            //catch (SecurityTokenInvalidSignatureException ex)
            //{
            //    // Handle invalid signature
            //    _logger.LogError($"Invalid token signature: {ex.Message}");

            //    var response =  context.GetHttpResponseData();
            //    response.StatusCode = HttpStatusCode.Unauthorized;
            //     response.WriteStringAsync("The token signature is invalid. Please provide a valid token.");
            //    return null;  // This stops further processing of the request.
            //}
            catch (Exception ex)
            {
                // General exception catch
                _logger.LogError($"Token validation failed: {ex.Message}");

                var response = context.GetHttpResponseData();
                response.StatusCode = HttpStatusCode.Unauthorized;
                response.WriteStringAsync("Invalid token.");
                return null;  // This stops further processing of the request.
            }
        }
        private async Task StoreTokenInKeyVaultAsync(string token)
        {
            try
            {
                string secretName = "MyAuthToken"; // Secret name to store in Key Vault

                // Create a KeyVaultSecret object with the token
                KeyVaultSecret secret = new KeyVaultSecret(secretName, token)
                {
                    Properties = { ExpiresOn = DateTimeOffset.UtcNow.AddHours(1) } // Set expiration date
                };

                // Store the token as a secret in Azure Key Vault
                await _secretClient.SetSecretAsync(secret);

                _logger.LogInformation("Token successfully stored in Azure Key Vault.");
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while storing the token in Key Vault: {ex.Message}");
            }
        }

    }
}
