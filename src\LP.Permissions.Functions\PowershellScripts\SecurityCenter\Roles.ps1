﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCRoleServers {
    <#
    .Synopsis
        Method used to add a server to a role fail-over list
    .DESCRIPTION
        This method is used when you want to add a new server to a role's fail-over list.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId is used to specify the role you want to add the server to
        
        The parameter ServerId is used to specify the server you want to add to the role's fail-over list
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myHM = Get-SCRoles -Type HealthMonitoring | Get-SCRole

        Add-SCRoleServers -RoleId $myHM.Id -ServerId $serverToAdd.Id
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sid")] $ServerId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $ServerId
            Set-SCEntityRelation -EntityId $RoleId -RelationName "roleservers" -RelationId $sid   
        }
    }   
}

# -----------------------------------------------------------------------------
Function Backup-SCRoleDatabase {
    <#
    .Synopsis
        Method used to start the backup of a role database
    .DESCRIPTION
        This method will trigger a backup of the database of the specified role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId is used to specify the role you want to backup

        The parameter BackupFolder is the location where the backup will be created.  This folder must be accessable from the
        server where the role is running

        The parameter EnableCompression is optional (false by default) and is used to compress or not the backup upon completion
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $myHM = Get-SCRoles -Type HealthMonitoring | Get-SCRole

        Backup-SCRoleDatabase -RoleId $myHM -BackupFolder "C:\SecurityCenterBackup" -EnableCompression $false
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("bf")][string]$BackupFolder,
        [parameter(Mandatory=$false)] [alias("ec")] [bool]$EnableCompression=$false
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Folder", $BackupFolder)
            $jsonObject.Add("EnableCompression", $EnableCompression)
            $jsonBody = $jsonObject | ConvertTo-Json
            $uri = "Entities/$rid/CreateDatabaseBackup"
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias dbsr Debug-SCRole
Function Debug-SCRole {
    <#
    .Synopsis
        This will launch the diagnose command for the given role
    .DESCRIPTION
        This method will launch the diagnose command for the given role  This method is used
        when you want to troubleshoot a given role.

        The RoleId parameter represents the Id of the role to diagnose (The guid representing the role in the Security Center System)
        You can also pass any role object that contains an ID as a parameter
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type user with the name MyNewUser
        $entity = Get-SCRoles -t HealthMonitoring
        
        Debug-SCRole -RoleId $entity

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "diagnostics" 
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias gsr Get-SCRole
Function Get-SCRole {
    <#
    .Synopsis
        This method will return all the properties of the role represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the role represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RoleId parameter represents the Id of the role to retrieve (The guid representing the role in the Security Center System)
        You can also pass any role object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myHM = Get-SCRoles -Type HealthMonitoring 
        Get-SCRole $myHM.Id

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myHM = Get-SCRoles -Type HealthMonitoring | Get-SCRole

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId   
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias gsrs Get-SCRoles
Function Get-SCRoles {
    <#
    .Synopsis
        Method used to get all the roles of the given role type from the Security Center Server
    .DESCRIPTION
        Method is used when you need all roles of a given type (i.e., all archivers).  
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete  

        The Type parameter is used to specify which role type you want.  You can download all available role type
        by calling Get-SCEnum -enum RoleTypeIds

        The Filter parameter is used to specify the data returned by the method.  Base value will only return the minimum 
        data and the All Value will return all available data.  Base is the default value
    .EXAMPLE
        #
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myHM = Get-SCRoles -Type HealthMonitoring 

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        #
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myHM = gsrs -Type HealthMonitoring -f 'All'

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param()
 
    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName1 = 'Type'
        $ParameterAlias1 = ("t", "RoleTypeId")
        $ParameterName2 = 'Filter'
        $ParameterAlias2 = "f"
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection1 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
        $AttributeCollection2 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute1 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute1.Mandatory = $true
        $ParameterAttribute1.Position = 1
        $ParameterAttribute1.ValueFromPipeline=$true
        $ParameterAttribute1.ValueFromPipelineByPropertyName=$true

        $ParameterAttribute2 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute2.Mandatory = $false
        $ParameterAttribute2.Position = 2

        # Add the attributes to the attributes collection
        $AttributeCollection1.Add($ParameterAttribute1)
        $AttributeCollection2.Add($ParameterAttribute2)

        # Generate and set the ValidateSet 
        $sess = GetSession -Quiet $true
        if ( $sess -ne $null )
        { 
            $arrSet1 = $sess.SCRoleTypeCache
            $ValidateSetAttribute1 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet1)

            $arrSet2 = $sess.SCFilterCache
            $ValidateSetAttribute2 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet2)
            
            # Add the ValidateSet to the attributes collection
            $AttributeCollection1.Add($ValidateSetAttribute1)
            $AttributeCollection2.Add($ValidateSetAttribute2)
        }

        #add the alias to the attributes collection
        $ParamAlias1 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias1
        $AttributeCollection1.Add($ParamAlias1)
        $ParamAlias2 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias2
        $AttributeCollection2.Add($ParamAlias2)

        # Create and return the dynamic parameter
        $RuntimeParameter1 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName1, [string], $AttributeCollection1)
        $RuntimeParameterDictionary.Add($ParameterName1, $RuntimeParameter1)

        $RuntimeParameter2 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName2, [string], $AttributeCollection2)
        $RuntimeParameterDictionary.Add($ParameterName2, $RuntimeParameter2)

        return $RuntimeParameterDictionary
    }

    begin {
    }

    process {
        
        # Bind the parameter to a friendly variable
        $Type = $PsBoundParameters[$ParameterName1]
        $Filter = $PsBoundParameters[$ParameterName2]

        if($Filter -eq $Null) {
            $Filter = 'Base'
        }

        SCCmdletImplementation $MyInvocation.InvocationName {
            $sess = GetSession
            $sess.SCRoleTypeMap | foreach { if($Type -eq $_.RoleTypeId) {$roleGuid = $_.Id}}
            $uri = "Entities/Roles?RoleTypeIds=$roleGuid" + "&ValidFlags=$Filter" 

            InvokeSCRestMethod -UriSuffix $uri -Method 'Get'
        }
    } 
}

# -----------------------------------------------------------------------------
Function Get-SCRoleAgents {
    <#
    .Synopsis
        This method will return all the agents that a role has configured
    .DESCRIPTION
        This method is used to return all the agents that a roles has configured
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RoleId parameter represents the Id of the role to retrieve the agents from(The guid representing the role in the Security Center System)
        You can also pass any role object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        Get-SCRoleAgents $myArc.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "agents"  
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCRolesDatabaseInformation {
    <#
    .Synopsis
        This method will return all the database information of the given role
    .DESCRIPTION
        This method will return all the database information of the given role if the role supports a database
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RoleId parameter represents the Id of the role to retrieve the database information from (The guid representing the role in the Security Center System)
        You can also pass any role object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myHM = Get-SCRoles -Type HealthMonitoring 
        Get-SCRolesDatabaseInformation $myHM

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "databasestatus"  
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCRoleServers {
    <#
    .Synopsis
        Method used to add get the server fail-over list from a role 
    .DESCRIPTION
        This method is used when you want to retrieve the fail-over list from a roles
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId is used to specify the role you want to get the servers from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myHM = Get-SCRoles -Type HealthMonitoring | Get-SCRole

        Get-SCRoleServers -RoleId $myHM.Id
        
        Exit-SCSession
    .EXAMPLE
        Example 2
    .EXAMPLE
        Example 3
    .EXAMPLE
        Example 4
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "roleservers"  
        }
    }   
}

# -----------------------------------------------------------------------------
Function Invoke-SCRoleDebugCommand {
    <#
    .Synopsis
		Invoke a debug command provided by the role's logging framework. 
        
    .DESCRIPTION
		This method will invoke the debug command with the specified parameters, if provided.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

		The Logger parameter represents the name of the logger that registered the command
		The Command parameter represents the name of the command to invoke
		The Parameters parameter, if any, represents the command's invoking parameters. Parameters is an array of objects containing name, type and value.
		You have to know the parameters and their types that are required by the debug command.

    .EXAMPLE
		#
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"
         
        $parameters = @()
		$param1 = @{name="name1"; type="System.String"; value="hello"}
		$param2 = @{name="name2"; type="System.Int32"; value="10"}
		$parameters = $param1, $param2

		Invoke-DebugCommand -Logger "Logger Full Name" -Command "CommandName" -Parameters $parameters
        
        Exit-SCSession
        
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
 
    [CmdletBinding()]
    param (
		[parameter(Mandatory=$true)] [string]$Logger,
		[parameter(Mandatory=$true)] [string]$Command,
		[parameter(Mandatory=$false)] [object[]]$Parameters
    )
 
    Begin {
    }

    Process {
		$jsonObject = [ordered]@{} 
		$params = @();
		$jsonObject.Add("logger", $Logger)
		$jsonObject.Add("command", $Command)
		$jsonObject.Add("parameters", $Parameters)
		$jsonBody = $jsonObject | ConvertTo-Json

        SCCmdletImplementation $MyInvocation.InvocationName {
            InvokeSCServerAdminRestMethod -UriSuffix "DebugCommand" -Method 'POST' -Body $jsonBody
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias nsr New-SCRole
Function New-SCRole {
    <#
    .Synopsis
        Will create a new role of the given type with the provided name running on the given server
    .DESCRIPTION
        This method will create an new role in Security Center of the given type with the given name on the given server.  
        The return value will contain the new role ID
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The Type parameter will determine the type of the role created
        
        The name parameter will be given to the new role upon creation 

        The ServerId parameter will determine on which server the role will run
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"
         
        $server = Get-SCEntities -t Servers

        New-SCRole -Name "MyNewRole" -Type Federation -ServerId $server[0]
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"
         
        $server = Get-SCEntities -t Servers

        nsr -n "MyNewRole" -t Federation -id $server[0]
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        $server = Get-SCEntities -t Servers

        $role = New-SCRole -Name "MyNewRole" -Type ActiveDirectory -ServerId $server[0] -Extra @{ExternalAddress="genetec";Username="toto";Password="tata";UseApplicationCredentials=$false} | Get-SCRole
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("n")][string]$Name,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ServerId,
        [parameter(Mandatory=$false)] [hashtable]$Extra
    )
    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName1 = 'Type'
        $ParameterAlias1 = 't'
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection1 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute1 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute1.Mandatory = $true
        $ParameterAttribute1.Position = 1

        # Add the attributes to the attributes collection
        $AttributeCollection1.Add($ParameterAttribute1)

        # Generate and set the ValidateSet 
        $sess = GetSession -Quiet $true
        if ($sess -ne $null) { 
            $arrSet1 = $sess.SCCreateRoleTypeCache
            $ValidateSetAttribute1 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet1)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection1.Add($ValidateSetAttribute1)
        }

         #add the alias to the attributes collection
        $ParamAlias1 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias1
        $AttributeCollection1.Add($ParamAlias1)

        # Create and return the dynamic parameter
        $RuntimeParameter1 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName1, [string], $AttributeCollection1)
        $RuntimeParameterDictionary.Add($ParameterName1, $RuntimeParameter1)
        return $RuntimeParameterDictionary
    }

    begin {
    }

    process {
        # Bind the parameter to a friendly variable
        $Type = $PsBoundParameters[$ParameterName1]
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $ServerId
            $uri = "Entities/" 

            $sess = GetSession
            $sess.SCRoleTypeMap | foreach { if($Type -eq $_.RoleTypeId) {$roleGuid = $_.Id}}
 
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("EntityType", "Roles")
            $jsonObject.Add("Name", $Name)
            $jsonObject.Add("RoleTypeId", $roleGuid)
            $jsonObject.Add("Server", $sid)

            if (($Extra -ne $null) -and ($Extra.Count -gt 0)) {
                foreach ($key in $extra.Keys.GetEnumerator()) {
                    $jsonObject.Add($key, $Extra[$key])
                }
            }

            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function New-SCRoleDatabase {
    <#
    .Synopsis
        This method will create a new database for the given role
    .DESCRIPTION
        This method will create a new database for the given role if the role supports a database
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RoleId parameter represents the Id of the role to create the database on (The guid representing the role in the Security Center System)
        You can also pass any role object that contains an ID as a parameter

        The DatabaseName parameter will be the name given to the new database

        The DatabaseFolder parameter (optional, empty string by default, will resolve to SQl folder) is the folder where the database is created

        The OverwriteDatabase parameter (optional, false by default) will force an overwrite of the database if one with the same name exists

        The DatabaseServer parameter (optional, will be local machine if not specified) is the server that hosts the database

        The Username parameter (optional, will use default windows credentials if not specified) is the server username, intended if the role is configured with a Microsoft Azure database

        The Password parameter (optional, will use default windows credentials if not specified) is the server password, intended if the role is configured with a Microsoft Azure database
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myHM = Get-SCRoles -Type HealthMonitoring 
        New-SCRoleDatabase $myHM -DatabaseName "testDB"

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("dbn")][string]$DatabaseName = "",
        [parameter(Mandatory=$false)] [alias("dbf")][string]$DatabaseFolder = "",
        [parameter(Mandatory=$false)] [alias("odb")] [bool]$OverwriteDatabase=$false,
        [parameter(Mandatory=$false)] [alias("dbs")][string]$DatabaseServer = "",
		[parameter(Mandatory=$false)] [alias("dbun")][string]$Username = "",
		[parameter(Mandatory=$false)] [alias("dbp")][string]$Password = ""
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $rid = GetIdFromObject $RoleId
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Database", $DatabaseName)
        $jsonObject.Add("Folder", $DatabaseFolder)
        $jsonObject.Add("Overwrite", $OverwriteDatabase)
        $jsonObject.Add("Server", $DatabaseServer)
        $jsonObject.Add("Username", $Username)
        $jsonObject.Add("Password", $Password)
        $jsonBody = $jsonObject | ConvertTo-Json

        $uri = "Entities/$rid/CreateDatabase"
        InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Function Set-SCRoleDatabase {
    <#
    .Synopsis
        This method will set a new database configuration for the given role. If the database does not already exist it will be created by the role, provided the role is online to do so.
    .DESCRIPTION
        This method will set a new database configuration for the given role. If the database does not already exist it will be created by the role, provided the role is online to do so.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RoleId parameter represents the Id of the role to create the database on (The guid representing the role in the Security Center System)
        You can also pass any role object that contains an ID as a parameter

        The DatabaseName parameter will be the name given to the new database

        The DatabaseServer parameter (optional, will be local machine if not specified) is the server that hosts the database

        The Username parameter (optional, will use default windows credentials if not specified) is the server username, intended if the role is configured with a Microsoft Azure database

        The Password parameter (optional, will use default windows credentials if not specified) is the server password, intended if the role is configured with a Microsoft Azure database
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        # Local db example
        $lprManager = Get-SCRoles -Type LicensePlateManager | Select-Object -First 1
        Set-SCRoleDatabase $lprManager -DatabaseName "LprManagerDatabase"

        # Azure example
        $lprManager = Get-SCRoles -Type LicensePlateManager | Select-Object -First 1
        Set-SCRoleDatabase $lprManager -DatabaseName "LprManagerDatabase" -DatabaseServer 'yourazuresqlserver.database.windows.net' -Username 'admin' -Password 'password'

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("dbn")][string]$DatabaseName = "",
		[parameter(Mandatory=$false)] [alias("dbs")][string]$DatabaseServer = "",
		[parameter(Mandatory=$false)] [alias("dbun")][string]$Username = "",
		[parameter(Mandatory=$false)] [alias("dbp")][string]$Password = ""
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $rid = GetIdFromObject $RoleId
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Database", $DatabaseName)
        $jsonObject.Add("Server", $DatabaseServer)
        $jsonObject.Add("Username", $Username)
        $jsonObject.Add("Password", $Password)
        $jsonBody = $jsonObject | ConvertTo-Json

        $uri = "Entities/$rid/SetDatabase"
        InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias rsr Remove-SCRole
Function Remove-SCRole {
    <#
    .Synopsis
        Will remove the role represented by the provided RoleId from Security Center
    .DESCRIPTION
        This method will permantly remove the specified role from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RoleId parameter represents the role to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $server = Get-SCEntities -t Servers

        $newFed = New-SCRole -Name "MyNewRole" -Type Federation -ServerId $server[0]

        #remove the new user by providing the ID
        Remove-SCRole -RoleId $newFed.Id
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $server = Get-SCEntities -t Servers

        $newFed = New-SCRole -Name "MyNewRole" -Type Federation -ServerId $server[0]

        #remove the new user by providing the ID
        rsr $newFed
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $RoleId   
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCRoleDatabase {
    <#
    .Synopsis
        This method will delete the database of the given role
    .DESCRIPTION
        This method will delete the database of the given role if the role supports a database
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RoleId parameter represents the Id of the role to remove the database from (The guid representing the role in the Security Center System)
        You can also pass any role object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myHM = Get-SCRoles -Type HealthMonitoring 
        Remove-SCRoleDatabase $myHM

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $rid = GetIdFromObject $RoleId
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Id", "")
        $jsonBody = $jsonObject | ConvertTo-Json
        $uri = "Entities/$rid/DeleteDatabase"
        InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCRoleServers {
    <#
    .Synopsis
        Method used to remove a server from a role fail-over list
    .DESCRIPTION
        This method is used when you want to remove a server from a roles fail-over list.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId is used to specify the role you want to remove the server from
        
        The parameter ServerId is used to specify the server you want to remove from role fail-over list
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myHM = Get-SCRoles -Type HealthMonitoring | Get-SCRole

        Remove-SCRoleServers -RoleId $myHM.Id -ServerId $serverToAdd.Id
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sid")] $ServerId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $ServerId
            Remove-SCEntityRelation -EntityId $RoleId -RelationName "roleservers" -RelationId $sid   
        }
    }    
}

# -----------------------------------------------------------------------------
Function Restore-SCRoleDatabase {
    <#
    .Synopsis
        This method will restore a database for the given role
    .DESCRIPTION
        This method will restore the database for the given role if the role supports a database
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RoleId parameter represents the Id of the role to restore the db to (The guid representing the role in the Security Center System)
        You can also pass any role object that contains an ID as a parameter

        The parameter BackupFileName represents the full path and backup file name used for the restore operation
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myHM = Get-SCRoles -Type HealthMonitoring 
        Restore-SCRoleDatabase $myHM -BackupFileName 'C:\SecurityCenterBackup\HealthMonitor_ManualBackup_2015-03-05_15h03min13s.bak'

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("bfn")][string]$BackupFileName
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("File", $BackupFileName)
            $jsonBody = $jsonObject | ConvertTo-Json

            $uri = "Entities/$rid/RestoreDatabaseBackup"
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssr Set-SCRole
Function Set-SCRole() {
    <#
    .Synopsis
        Used to update the properties of a role to Security Center
    .DESCRIPTION
        This method is used to update the properties of a role to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated seperatly by using the Set-SCEntityRelation method

        The parameter Role represents and contains the properties that will be updated to security Center

		The switch forcepassword is required if you want to set the password '****'
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myFed = Get-SCRoles -Type Federation | gsr
        $myFed.Description = "my Fed"

        Set-SCRole $myFed

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("al")] $Role,
		[alias("Force")] [switch]$ForcePassword = $false
    )

    Begin {
    }

    Process {
	    if (!$ForcePassword -and $Role.Password -eq '****')
        {
            $Role.PSObject.Properties.Remove('Password')
            write-warning ("Password was set to '****' for role `'{0}`' and so will not be set" -f $Role.Name)
        }

        if($ForcePassword)
        {
            SCCmdletImplementation $MyInvocation.InvocationName {
                Set-SCEntity -EntityToSet $Role -Force 
            }
        }
        else
        {
            SCCmdletImplementation $MyInvocation.InvocationName {
                Set-SCEntity -EntityToSet $Role 
            }
        }
    }
} 

# -----------------------------------------------------------------------------
Function Show-SCRoleProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a role
    .DESCRIPTION
        This method will list the supported properties and relation of a role (the data model, not the actual data).  This method is used
        when you want to know what is available for a given role
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCRoleProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
    #>  

    [CmdletBinding()]
    Param()
 
    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName1 = 'Type'
        $ParameterAlias1 = "t"
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection1 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
   
        # Create and set the parameters' attributes
        $ParameterAttribute1 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute1.Mandatory = $true
        $ParameterAttribute1.Position = 1


        # Add the attributes to the attributes collection
        $AttributeCollection1.Add($ParameterAttribute1)

        # Generate and set the ValidateSet 
        $sess = GetSession -Quiet $true

        if ($sess -ne $null) { 
            $arrSet1 = $sess.SCRoleTypeCache
            $ValidateSetAttribute1 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet1)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection1.Add($ValidateSetAttribute1)
        }

        #add the alias to the attributes collection
        $ParamAlias1 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias1
        $AttributeCollection1.Add($ParamAlias1)

        # Create and return the dynamic parameter
        $RuntimeParameter1 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName1, [string], $AttributeCollection1)
        $RuntimeParameterDictionary.Add($ParameterName1, $RuntimeParameter1)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $Type = $PsBoundParameters[$ParameterName1]
    }

    process {
        Switch($Type) {
            Federation { $ApiRole = "ApiSecurityCenterFederationRole"}
            Omnicast { $ApiRole = "ApiOmnicastFederationRole"}
            LicensePlateManager { $ApiRole = "ApiLicensePlateManagerRole"}
            Archiver { $ApiRole = "ApiArchiverRole"}
            StreamManagement { $ApiRole = "ApiMediaRouterRole"}
            AccessManager { $ApiRole = "ApiAccessManagerRole"}
            PointOfSale { $ApiRole = "ApiPointOfSaleRole"}
            ZoneManagement { $ApiRole = "ApiZoneManagementRole"}
            HealthMonitoring { $ApiRole = "ApiHealthMonitoringRole"}
            AuxiliaryArchiver { $ApiRole = "ApiAuxiliaryArchiverRole"}
            DirectoryFailover { $ApiRole = "ApiDirectoryFailoverRole"}
            ActiveDirectory { $ApiRole = "ApiActiveDirectoryRole"}
            MapManager { $ApiRole = "ApiMapManagerRole"}
            WebApplication { $ApiRole = "ApiWebClientRole"}
            MediaGateway { $ApiRole = "ApiMediaGatewayRole"}
            Plugin { $ApiRole = "ApiPluginRole"}
            default { $ApiRole = "ApiRole"}
        }

        $uri = "Help/Entities/$ApiRole" 

        SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
        }
    } 
}

# -----------------------------------------------------------------------------
Function Update-SCRoleDatabase{ 
    <#
    .Synopsis
        This method will update a database for the given role if needed
    .DESCRIPTION
        This method will update the database for the given role if the role supports a database
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RoleId parameter represents the Id of the role to update the DB (The guid representing the role in the Security Center System)
        You can also pass any role object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myHM = Get-SCRoles -Type HealthMonitoring 
        Update-SCRoleDatabase $myHM

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId
    )
    SCCmdletImplementation $MyInvocation.InvocationName {
        $rid = GetIdFromObject $RoleId
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Id", "")
        $jsonBody = $jsonObject | ConvertTo-Json

        $uri = "Entities/$rid/UpgradeDatabase"
        InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------

Function New-SCSimulationRole([Int]$videoUnits = 0, [Int]$cashRegisters = 0, [Int]$cardHolders = 0, 
   [Int]$doors = 0, [Int]$areas = 0, [Int]$zones = 0, [Int]$elevators = 0, [Int]$alarms = 0, [Int]$camerasPerVideoUnit = 0, 
   [Int]$camerasPerCashRegister = 0, [Int]$cardholdersPerGroup = 30, [Int]$inputPinsPerZone = 0, 
[Int]$floorsPerElevator = 3,[ValidateSet('true','false')][string] $AssociateCardholdersInfo = 'true',[Parameter(Mandatory = $true)][string]$name,
[Parameter(Mandatory = $true)][string] $serverID){
    <#
    .Synopsis
        This method will create a new simulation role
    .DESCRIPTION
        Prior to calling this method you must ensure that you have called Enable-SimulationRole on your server. You then must 
        enter your SC Session. You then must specify the name you wish to give the role and Server GUID. 

        You can optionally specify any properties in the method call or you can configure it later in the config tool. 
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        New-SCSimulationRole -name "SimulationRoleExample" -serverID "9E4399AA-2A61-41AC-8E2C-E8AB549CE26B"

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

   $customEntities = 0
    New-SCRole -Type SimulationRole -Name $name -ServerId $serverID -Extra @{"SpecificXml" = "
    <XmlSimulationRole>
      <DefaultParentArea>00000000-0000-0000-0000-000000000007</DefaultParentArea>
      <SimulationType>Genetec.Platform.SimulationRole.Plugins.GenericSimulatorSetup</SimulationType>
      <XmlInfo>&lt;XmlGenericSimulator&gt;
      &lt;DefaultParentArea&gt;00000000-0000-0000-0000-000000000007&lt;/DefaultParentArea&gt;
      &lt;Entities&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;videounits&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;$videoUnits&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;cashregisters&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;$cashRegisters&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;cardholders&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;$cardHolders&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;doors&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;$doors&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;areas&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;$areas&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;zones&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;$zones&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;elevators&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;$elevators&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;alarms&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;$alarms&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;customentities&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;$customEntities&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
      &lt;/Entities&gt;
      &lt;CamerasPerCashRegister&gt;$camerasPerCashRegister&lt;/CamerasPerCashRegister&gt;
      &lt;CamerasPerVideoUnit&gt;$camerasPerVideoUnit&lt;/CamerasPerVideoUnit&gt;
      &lt;InputPinsPerZone&gt;$inputPinsPerZone&lt;/InputPinsPerZone&gt;
      &lt;FloorsPerElevator&gt;$floorsPerElevator&lt;/FloorsPerElevator&gt;
      &lt;AssociateCardholdersInfo&gt;$AssociateCardholdersInfo&lt;/AssociateCardholdersInfo&gt;
      &lt;CardholdersPerGroup&gt;$cardholdersPerGroup&lt;/CardholdersPerGroup&gt;
      &lt;Events&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;cameraeventtimer&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;0&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;cardholdereventtimer&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;0&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;credentialeventtimer&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;0&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;dooreventtimer&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;0&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;areaeventtimer&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;0&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;zoneeventtimer&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;0&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;elevatoreventtimer&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;0&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
        &lt;item&gt;
          &lt;key&gt;
            &lt;string&gt;alarmeventtimer&lt;/string&gt;
          &lt;/key&gt;
          &lt;value&gt;
            &lt;int&gt;0&lt;/int&gt;
          &lt;/value&gt;
        &lt;/item&gt;
      &lt;/Events&gt;
      &lt;ActivateEvents&gt;false&lt;/ActivateEvents&gt;
      &lt;UseNonBoringNames&gt;false&lt;/UseNonBoringNames&gt;
      &lt;CreateCardholderPictures&gt;false&lt;/CreateCardholderPictures&gt;
      &lt;CardholderPicturesRandomSeed&gt;0&lt;/CardholderPicturesRandomSeed&gt;
      &lt;CardholderSpecificRandomSeed&gt;0&lt;/CardholderSpecificRandomSeed&gt;
      &lt;RandomSeed&gt;0&lt;/RandomSeed&gt;
    &lt;/XmlGenericSimulator&gt;</XmlInfo>
    </XmlSimulationRole>
    "}
    }


Export-ModuleMember -Function '*-*' -Alias '*'
