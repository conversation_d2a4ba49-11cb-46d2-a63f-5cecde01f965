﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
	<RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
	<ProjectGuid>{16B9EADC-2BAB-487B-A063-F9EF78745090}</ProjectGuid>
</PropertyGroup> 
 	
  <ItemGroup>
    <PackageReference Include="Azure.Core" Version="1.45.0" />
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.23.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
	<PackageReference Include="Azure.Identity" Version="1.13.2" />
	<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	<PackageReference Include="Telerik.UI.for.Blazor" Version="8.1.1" />
  </ItemGroup>
  
  <ItemGroup>
    <Content Update="wwwroot\css\uploadfile.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="wwwroot\css\Styles.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\tjxlogo2.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\TJX_Logo.svg.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  
  <ItemGroup>
    <Folder Include="wwwroot\NewFolder1\" />
    <Folder Include="wwwroot\NewFolder\" />
  </ItemGroup>

</Project>
