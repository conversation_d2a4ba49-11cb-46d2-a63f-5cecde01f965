# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================


Set-Variable -name PermitZoneAllPermits -option ReadOnly -value ([GUID]('72D06E91-596F-4259-8012-BD2A0A657017')) -Scope Global -Force
Set-Variable -name PermitZoneEveryone -option ReadOnly -value ([GUID]('0362449C-E193-4747-9D66-E8B483069F1C')) -Scope Global -Force
Set-Variable -name PermitZoneNoPermits -option ReadOnly -value ([GUID]('E216707F-1FC0-46f4-B454-2CE8FD0CA5B4')) -Scope Global -Force

# -----------------------------------------------------------------------------
Function ConvertTo-SCPermitRuleRestriction() {
    <#
    .Synopsis
        Used to create a Restriction object in powershell session.
    .DESCRIPTION
        

        The parameter Id represent a read-only identifier of the restriction. It is generated by the system based on a hash of the object.

        The parameter Days is a bit-field representing to which days to apply this restriction. The values are:
        Never = 0,
        Monday = 0x01,
        Tuesday = 0x02,
        Wednesday = 0x04,
        Thursday = 0x08,
        Friday = 0x10,
        Saturday = 0x20,
        Sunday = 0x40,

        The parameter Hours is a string representing to which hours of the days to apply this restriction. Example: from 11am to 2pm -> 11:00-14:00

        The parameter ValidityRange is a string representing days and months to apply this restriction. Example: the whole years would be "01.01-12.31"

        The parameters Permits is an array of GUID representing the permits to which is applied this restriction. There is 3 special permit GUID that can also be used:
        PermitZoneAllPermits
        PermitZoneEveryone
        PermitZoneNoPermits

    .EXAMPLE
        #
        ConvertTo-SCPermitRuleRestriction -Days 124 -Hours '11:00-14:00' -ValidityRange '01.01-12.31' -Permits @([GUID]('00000000-0000-1234-0000-000000000000'),[GUID]('00000000-0000-5678-0000-000000000000'))
        ConvertTo-SCPermitRuleRestriction -Days 124 -Hours '11:00-14:00' -ValidityRange '01.01-12.31' -Permits @($PermitZoneAllPermits)
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [int]$Id,
        [byte] $Days,
        [string]$Hours,
        [string]$ValidityRange,
        [GUID[]]$Permits
    )
    Process {
        return [PSCustomObject]@{
            Id = $Id
            Days = $Days
            Hours = $Hours
            ValidityRange = $ValidityRange
            Permits = $Permits
            }
    }
}
# -----------------------------------------------------------------------------
Function Get-SCPermitRule {
    <#
    .Synopsis
        This method will return all the properties of the permit rule represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the permit rule represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the permit rule to retrieve (The guid representing the permit rule in the Security Center System)
        You can also pass any permit rule object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        # Create a new permit rule and get it.
        $myPermitRule = New-SCPermitRule -Name "MyPermitRule" | Get-SCPermitRule

        #Exit the session
        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RuleId           
        }
    }
}
# -----------------------------------------------------------------------------
Function New-SCPermitRule {
    <#
    .Synopsis
        Method used to create a new permit rule with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new permit rule with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new permit rule upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new permit rule and get it.
        $myPermitRule = New-SCPermitRule -Name "MyPermitRule" | Get-SCPermitRule

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t PermitRules
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCPermitRule {
    <#
    .Synopsis
        Will remove the permit rule represented by the provided RuleId parameter from Security Center
    .DESCRIPTION
        This method will permanently remove the specified permit rule from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the permit rule to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
      
        Remove-SCPermitRule -RuleId $permitruleid

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RuleId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $RuleId
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCPermitRule() {
    <#
    .Synopsis
        Used to update the properties of a permit rule in Security Center
    .DESCRIPTION
        This method is used to update the properties of a permit rule to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter PermitRule represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new permit rule.
        $myPermitRule = New-SCPermitRule -Name "MyPermitRule" | Get-SCPermitRule

        # Update the entity.
        $myPermitRule.Color = "ffffffff"
        Set-SCPermitRule -PermitRule $myPermitRule 

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("p")] $PermitRule
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $PermitRule
        }
    }
}
# -----------------------------------------------------------------------------
Function Show-SCPermitRuleProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a permit rule
    .DESCRIPTION
        This method will list the supported properties and relation of an permit rule (the data model, not the actual data).  This method is used
        when you want to know what is available for a permit rule
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCCredentialProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiPermitRule" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}
# -----------------------------------------------------------------------------
Function Get-SCPermitRuleRestrictions {
    <#
    .Synopsis
        This method will return all the properties of all the permit rule restriction represented by the RuleId
    .DESCRIPTION
        This method will return all the basic properties of the permit rule restriction contained in a PermitRule represented by the RuleId.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the permit rule that contain the restriction to retrieve (The guid representing the permit rule in the Security Center System)
        You can also pass any permit rule object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $restrictions = Get-SCPermitRuleRestrictions -RuleId $mypermitRule.id       

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $RuleId
    
            InvokeSCRestMethod -Method 'GET' -UriSuffix "Entities/$id/Restrictions"      
        }
    }
}
# -----------------------------------------------------------------------------
Function Get-SCPermitRuleRestriction {
    <#
    .Synopsis
        This method will return all the properties of the permit rule restriction represented by the ID in a specified permit rule.
    .DESCRIPTION
        This method will return all the basic properties of the permit rule restriction represented by the ID contained inside a specified RuleId.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the permit rule that contain the restriction. (The guid representing the permit rule in the Security Center System)
        You can also pass any permit rule object that contains an ID as a parameter

        The RestrictionId parameter represents the Id of the permit rule restriction to retrieve (The int representing the permit rule restriction in the Security Center System)
        You can also pass any permit rule restriction object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $restrictions = Get-SCPermitRuleRestriction -RuleId $mypermitRule.id -RestrictionId $restrictionId      

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RuleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("prid")] $RestrictionId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $RuleId
            $prid = GetIdFromObject $RestrictionId
    
            InvokeSCRestMethod -Method 'GET' -UriSuffix "Entities/$id/Restrictions/$prid"      
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCPermitRuleRestriction() {
    <#
    .Synopsis
        Used to update the properties of a permit rule restriction in Security Center
    .DESCRIPTION
        This method is used to update the properties of a permit rule restriction to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the permit rule that contain the restriction. (The guid representing the permit rule in the Security Center System)
        You can also pass any permit rule object that contains an ID as a parameter

        The Restriction parameter represents the permit rule restriction to update.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $restriction = [PSCustomObject]@{
            Days = 124
            Hours = "00:00-00:00"
            ValidityRange = "01.01-12.31"
            Permits = @([GUID]($myPermit.Id))
            }
        $restrictionId = Set-SCPermitRuleRestriction -PermitRuleId $myPermitRule.Id -Restriction $restriction

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("p")] $RuleId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("pr")] $Restriction
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $id = GetIdFromObject $RuleId
            $pid = GetIdFromObject $Restriction
            $jsonBody = $Restriction | ConvertTo-Json 
    
            InvokeSCRestMethod -Method 'PUT' -UriSuffix "Entities/$id/Restrictions/$pid" -Body $jsonBody
        }
    }
}
# -----------------------------------------------------------------------------
Function New-SCPermitRuleRestriction() {
    <#
    .Synopsis
         Method used to create a new permit rule restriction with the provided object
    .DESCRIPTION
        This method is used to create the object in security center and to fill the properties.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the permit rule that contain the restriction. (The guid representing the permit rule in the Security Center System)
        You can also pass any permit rule object that contains an ID as a parameter

        The Restriction parameter represents the permit rule restriction to create.
        
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new permit rule restriction
        $restriction = [PSCustomObject]@{
            Days = 124
            Hours = "00:00-00:00"
            ValidityRange = "01.01-12.31"
            Permits = @([GUID]($myPermit.Id))
            }
        $restrictionId = New-SCPermitRuleRestriction -PermitRuleId $myPermitRule.Id -Restriction $restriction

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("p")] $RuleId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("pr")] $Restriction
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $id = GetIdFromObject $RuleId
            $jsonBody = $Restriction | ConvertTo-Json 
    
            InvokeSCRestMethod -Method 'POST' -UriSuffix "Entities/$id/Restrictions" -Body $jsonBody
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCPermitRuleRestriction {
    <#
    .Synopsis
        Will remove the permit rule restriction represented by the RestrictionId in a specific Permit Rule.
    .DESCRIPTION
        This method will permanently remove the specified permit rule restriction from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the permit rule that contain the restriction. (The guid representing the permit rule in the Security Center System)
        You can also pass any permit rule object that contains an ID as a parameter

        The RestrictionId parameter represents the permit rule restriction to remove.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
      
        Remove-SCPermitRuleRestriction -RuleId $permitruleid -RestrictionId $myRestriction

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RuleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("prid")] $RestrictionId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $RuleId
            $prid = GetIdFromObject $RestrictionId
    
            InvokeSCRestMethod -Method 'DELETE' -UriSuffix "Entities/$id/Restrictions/$prid"      
        }
    }
}
# -----------------------------------------------------------------------------
Export-ModuleMember -Function '*-*' -Alias '*'