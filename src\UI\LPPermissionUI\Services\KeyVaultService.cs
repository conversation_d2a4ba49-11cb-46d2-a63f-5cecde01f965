﻿namespace LPPermission.UI.Services
{
    /// <summary>
    /// Service for accessing secrets stored in the application's configuration, such as Azure Key Vault.
    /// </summary>
    public class KeyVaultService
    {
        // Configuration object to access application settings
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Default constructor for KeyVaultService.
        /// </summary>
        public KeyVaultService()
        { }

        /// <summary>
        /// Constructor to initialize KeyVaultService with a configuration object.
        /// </summary>
        /// <param name="configuration">The configuration object containing secrets.</param>
        public KeyVaultService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        /// <summary>
        /// Retrieves a specific secret by its key from the configuration.
        /// </summary>
        /// <param name="key">The key of the secret to retrieve.</param>
        /// <returns>The value of the secret if it exists, otherwise null.</returns>
        public string GetSecret(string key)
        {
            return _configuration[key];
        }

        /// <summary>
        /// Retrieves all secrets from the configuration as a dictionary.
        /// Filters out keys with null or empty values.
        /// </summary>
        /// <returns>A dictionary containing all non-empty secrets.</returns>
        public Dictionary<string, string> GetAllSecrets()
        {
            return _configuration.AsEnumerable()
                .Where(kvp => !string.IsNullOrEmpty(kvp.Value)) // Filter out null or empty values
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }
    }
}