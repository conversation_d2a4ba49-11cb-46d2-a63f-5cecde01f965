﻿using LPPermission.UI.Models;

namespace LPPermission.UI.Services
{
    /// <summary>
    /// Service for retrieving log entries from the API.
    /// </summary>
    public class LogService
    {
        // HttpClient for making HTTP requests
        private readonly HttpClient _httpClient;

        /// <summary>
        /// Constructor to initialize the LogService with an HttpClient.
        /// </summary>
        /// <param name="httpClient">HttpClient instance for making HTTP requests.</param>
        public LogService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        /// <summary>
        /// Retrieves a list of log entries from the API.
        /// </summary>
        /// <returns>A list of log entries.</returns>
        public async Task<List<LogEntry>> GetLogEntriesAsync()
        {
            // Send a POST request to the API endpoint to retrieve log entries
            var response = await _httpClient.PostAsync("http://localhost:7010/api/GetLogEntries", null);

            // Ensure the response indicates success; throw an exception if not
            response.EnsureSuccessStatusCode();

            // Deserialize the response content into a list of LogEntry objects
            return await response.Content.ReadFromJsonAsync<List<LogEntry>>();
        }
    }
}