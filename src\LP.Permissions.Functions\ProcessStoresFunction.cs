using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Azure.Security.KeyVault.Secrets;
using Azure.Identity;
using FunctionApp.LPPermissions.Services.interfaces;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using System.Net;
using System.Security.Policy;

namespace FunctionApp.LPPermissions
{
    public class ProcessStoresFunction
    {
        private readonly ILogger _logger;
        private readonly SecretClient _secretClient;
        private readonly IFedConnectionService _fedConnectionService;
        private readonly IStoreConnectionService _storeConnectionService;
        private string urlkv= "";
        public ProcessStoresFunction(
            ILoggerFactory loggerFactory,
            IConfiguration configuration,
            IFedConnectionService fedConnectionService,
            IStoreConnectionService storeConnectionService)
        {
            _logger = loggerFactory.CreateLogger<ProcessStoresFunction>();

            // Initialize Key Vault client
            string keyVaultUrl = configuration["KeyVaultUrl"];
            urlkv = keyVaultUrl;
            _secretClient = new SecretClient(new Uri(keyVaultUrl), new DefaultAzureCredential());

            _fedConnectionService = fedConnectionService;
            _storeConnectionService = storeConnectionService;
        }

        [Function("ProcessStores")]
        public async Task<HttpResponseData> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post")] HttpRequestData req)
        {
            _logger.LogInformation("ProcessStores function started at: {time}", DateTime.UtcNow);

            try
            {
                // Step 1: Connect to the federation service and retrieve store IPs
                var storeIPs = await GetStoreIPsAsync();
                if (storeIPs == null || storeIPs.Count == 0)
                {
                    _logger.LogError("No store IPs retrieved from the federation service.");
                    var errorResponse = req.CreateResponse(System.Net.HttpStatusCode.InternalServerError);
                    await errorResponse.WriteStringAsync("Failed to retrieve store IPs.");
                    return errorResponse;
                }

                _logger.LogInformation("Retrieved {count} store IPs from the federation service.", storeIPs.Count);

                // Step 2: Process each store
                foreach (var ipAddress in storeIPs)
                {
                    _logger.LogInformation("Processing store with IP: {ipAddress}", ipAddress);
                    bool isProcessed = await ProcessStoreAsync(ipAddress);

                    if (isProcessed)
                    {
                        _logger.LogInformation("Successfully processed store with IP: {ipAddress}", ipAddress);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to process store with IP: {ipAddress}", ipAddress);
                    }
                }

                var response = req.CreateResponse(System.Net.HttpStatusCode.OK);
                await response.WriteStringAsync("Store processing completed successfully.");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while processing stores.");
                var errorResponse = req.CreateResponse(System.Net.HttpStatusCode.InternalServerError);
                await errorResponse.WriteStringAsync("An error occurred while processing stores.");
                return errorResponse;
            }
        }

        private async Task<List<string>> GetStoreIPsAsync()
        {
            List<string> storeIPs = null;

            try
            {
                _logger.LogInformation("Entered into GetStoreIPsAsync function");
                _logger.LogInformation("urlkv: {urlkv}", urlkv);

                // Retrieve credentials from Key Vault
                var primaryCredential = await GetCredentialAsync("Samurai");
                var federationServer = await GetCredentialAsync("FederationServer");

                _logger.LogInformation("primaryCredential: {primaryCredential}", primaryCredential);
                _logger.LogInformation("federationServer: {federationServer}", federationServer);

                // Try connecting with the primary user
                string systemInfo;
                storeIPs = _fedConnectionService.Connect(federationServer, "Samurai", primaryCredential, string.Empty, out systemInfo);

                if (storeIPs != null && storeIPs.Count > 0)
                {
                    _logger.LogInformation("Successfully retrieved store IPs using primary credential.");
                    return storeIPs; // Stop further attempts if successful
                }

                // Try connecting with the secondary user
                //var secondaryCredential = await GetCredentialAsync("Secondary");
                //storeIPs = _fedConnectionService.Connect(federationServer, "Secondary", secondaryCredential, string.Empty, out systemInfo);

                //if (storeIPs != null && storeIPs.Count > 0)
                //{
                //    _logger.LogInformation("Successfully retrieved store IPs using secondary credential.");
                //    return storeIPs; // Stop further attempts if successful
                //}

                //// Try connecting with the admin user
                //var adminCredential = await GetCredentialAsync("Admin");
                //storeIPs = _fedConnectionService.Connect(federationServer, "Admin", adminCredential, string.Empty, out systemInfo);

                //if (storeIPs != null && storeIPs.Count > 0)
                //{
                //    _logger.LogInformation("Successfully retrieved store IPs using admin credential.");
                //    return storeIPs; // Stop further attempts if successful
                //}
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve store IPs from the federation service.");
            }

            return storeIPs;
        }

        private async Task<bool> ProcessStoreAsync(string ipAddress)
        {
            try
            {
                // Retrieve credentials from Key Vault
                var primaryCredential = await GetCredentialAsync("Samurai");

                // Try processing the store
                var userGroups = await _storeConnectionService.SetLPPermissions(ipAddress, "Samurai", primaryCredential);
                if (userGroups != null && userGroups.Count > 0)
                {
                    _logger.LogInformation("Successfully processed store with IP: {ipAddress}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process store with IP: {ipAddress}");
            }

            return false;
        }

        private async Task<string> GetCredentialAsync(string credentialName)
        {
            try
            {
                _logger.LogInformation("credentialName: {credentialName}", credentialName);
                KeyVaultSecret secret = await _secretClient.GetSecretAsync(credentialName);
                _logger.LogInformation("value  : {secret.Value}", secret.Value);
                return secret.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve credential: {credentialName}");
                throw;
            }
        }
    }
}
