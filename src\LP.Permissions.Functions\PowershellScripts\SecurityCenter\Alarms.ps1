﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias asalr Add-SCAlarmRecipient
Function Add-SCAlarmRecipient {
    <#
    .Synopsis
        Method used to add a recipient to the given alarm
    .DESCRIPTION
        This Method will allow the user to add a security center user as a recipient to a security center alarm
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AlarmId will be used to specify the alarm we want to add the user to

        The parameter UserId represents the Id of the user we want to add to the recipient list of the alarm

        The parameter PropagationLevel is the propagation delay that will be used in case of a sequential alarm.  This parameter is optional with a default of 1
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        
        #Will create a new entity of type user with the name MyNewUser
        $us = New-SCEntity -n "MyNewUser" -t users

        Add-SCAlarmRecipient -AlarmId $al -UserId $us -PropagationLevel 10
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        
        #Will create a new entity of type user with the name MyNewUser
        $us = New-SCEntity -n "MyNewUser" -t users

        Add-SCAlarmRecipient -Id $al -uid $us
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $AlarmId,
        [parameter(Mandatory=$true)] [alias("uid")] $UserId,
        [parameter(Mandatory=$false)] [alias("p")][int] $PropagationLevel=1
    )
    begin {
    }
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $aid = GetIdFromObject $AlarmId
            $uid = GetIdFromObject $UserId

            $uri = "Entities/$aid/recipients/"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", $uid)
            $jsonObject.Add("PropagationLevel", $PropagationLevel)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias asalae Add-SCAlarmAttachedEntity
Function Add-SCAlarmAttachedEntity {
    <#
    .Synopsis
        Method used to add an attach to the given alarm
    .DESCRIPTION
        This Method will allow the user to add a security center entity (camera, cardholder or cardholder group only) as a attach entity to a security center alarm
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AlarmId will be used to specify the alarm we want to add the user to

        The parameter EntityId represents the Id of the entity we want to add to the attach entity list of the alarm
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        
        $cs = Search-SCEntities -n "Mycam" -t cameras

        Add-SCAlarmRecipient -AlarmId $al -EntityId $cs
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $AlarmId,
        [parameter(Mandatory=$true)] [alias("eid")] $EntityId
    )
    begin {
    }
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $aid = GetIdFromObject $AlarmId
            $eid = GetIdFromObject $EntityId

            $uri = "Entities/$aid/AttachedEntities/"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", $eid)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias gsal Get-SCAlarm
Function Get-SCAlarm {
    <#
    .Synopsis
        This method will return all the properties of the alarm represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the alarm represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The AlarmId parameter represents the Id of the alarm to retrieve (The guid representing the entity in the Security Center System)
        You can also pass any alarm object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCAlarm -n "MyNewAlarm"

        Get-SCAlarm -AlarmId $al.Id

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        New-SCEntity -n "MyNewAlarm" -t alarms | Get-SCAlarm

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        nsal "MyNewAlarm" | gsal

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AlarmId
    )
    begin{
    }
    process{
        SCCmdletImplementation $MyInvocation.InvocationName {
            $aid = GetIdFromObject $AlarmId
            Get-SCEntity -EntityId $aid
        }
    }
        
}

# -----------------------------------------------------------------------------
Set-Alias gsalr Get-SCAlarmRecipients
Function Get-SCAlarmRecipients {
    <#
    .Synopsis
        Method used to retrieve the recipient list of the given alarm
    .DESCRIPTION
        This Method will allow the user to retrieve the recipient list of a security center alarm
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AlarmId will be used to specify the alarm we want to retrieve the recipient list from

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        
        #Will create a new entity of type user with the name MyNewUser
        $us = New-SCEntity -n "MyNewUser" -t users

        Add-SCAlarmRecipient -AlarmId $al -UserId $us -PropagationLevel 10

        Get-SCAlarmRecipients -AlarmId $al.Id
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        
        #Will create a new entity of type user with the name MyNewUser
        $us = New-SCEntity -n "MyNewUser" -t users

        Add-SCAlarmRecipient -AlarmId $al -UserId $us -PropagationLevel 10

        Get-SCAlarmRecipients $al
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AlarmId
    )
    begin{
    }
    process{
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AlarmId -RelationName "recipients"
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias gsalae Get-SCAlarmAttachedEntities
Function Get-SCAlarmAttachedEntities {
    <#
    .Synopsis
        Method used to get the attach entities of the given alarm
    .DESCRIPTION
        This Method will allow the user to get the security center entities (camera, cardholder or cardholder group only) that are attach to a security center alarm
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AlarmId will be used to specify the alarm we want to add the user to
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        
        #Will create a new entity of type user with the name MyNewUser
        $cs = New-SCEntity -n "Mycam" -t cameras

        Add-SCAlarmRecipient -AlarmId $al -EntityId $cs

        Get-SCAlarmAttachedEntities -AlarmId $al
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AlarmId
    )
    begin{
    }
    process{
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AlarmId -RelationName "AttachedEntities"
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias isala Invoke-SCAlarmAck
Function Invoke-SCAlarmAck {
    <#
    .Synopsis
        Method used to acknowledge an alarm
    .DESCRIPTION
        This Method will allow the user to acknowledge a specific instance of a security center alarm
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AlarmInstance is used to specify which alarm instance we want to acknowledge

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        $us = New-SCEntity -n "MyNewUser" -t users

        Add-SCAlarmRecipient -AlarmId $al -UserId $us -PropagationLevel 10

        $instance = Invoke-SCAlarmTrigger -AlarmId $al.Id
        
        Invoke-SCAlarmAck -AlarmInstance $instance

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("ai")] [int]$AlarmInstance
    )
 
    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'AckType'
        $ParameterAlias = 'at'
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $false
        $ParameterAttribute.Position = 1

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        # Generate and set the ValidateSet 
            
        $arrSet = 'Ack', 'Nack'
        $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

        # Add the ValidateSet to the attributes collection
        $AttributeCollection.Add($ValidateSetAttribute)

         #add the alias to the attributes collection
        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)
        return $RuntimeParameterDictionary
    }

    begin {
		# Bind the parameter to a friendly variable
		$AckType = $PsBoundParameters[$ParameterName]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            if(!$AckType){
                $AckType = 'Ack'
            }

        
            $uri = "Reports/Alarms/$AlarmInstance/$AckType" 

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", "")
            $jsonBody = $jsonObject | ConvertTo-Json

            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        } 
    } 
}

# -----------------------------------------------------------------------------
Set-Alias isalfa Invoke-SCAlarmForceAckAll
Function Invoke-SCAlarmForceAckAll {
    <#
    .Synopsis
        Method used to acknowledge All active alarms
    .DESCRIPTION
        This Method will allow an admin user (and only an administrator) to acknowledge all active alarm in the system
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        $us = New-SCEntity -n "MyNewUser" -t users

        Add-SCAlarmRecipient -AlarmId $al -UserId $us -PropagationLevel 10

        $instance = Invoke-SCAlarmTrigger -AlarmId $al.Id
        
        Invoke-SCAlarmForceAckAll

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    SCCmdletImplementation $MyInvocation.InvocationName {
        $uri = "Entities/Alarms/ForceAckAllAlarms" 

        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Id", "")
        $jsonBody = $jsonObject | ConvertTo-Json

        InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    } 
}

# -----------------------------------------------------------------------------
Set-Alias isalt Invoke-SCAlarmTrigger
Function Invoke-SCAlarmTrigger {
    <#
    .Synopsis
        Method used to trigger an alarm
    .DESCRIPTION
        This Method will allow the user to trigger a security center alarm. Calling this method will return the instance id of the triggered alarm
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AlarmId is used to specify which alarm to trigger

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        $us = New-SCEntity -n "MyNewUser" -t users

        Add-SCAlarmRecipient -AlarmId $al -UserId $us -PropagationLevel 10

        $instance = Invoke-SCAlarmTrigger -AlarmId $al.Id
        
        Invoke-SCAlarmAck -AlarmInstance $instance

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AlarmId
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $aid = GetIdFromObject $AlarmId
            $uri = "Entities/$aid/Trigger"
         
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", "")
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias nsal New-SCAlarm
Function New-SCAlarm {
    <#
    .Synopsis
        Will create a new alarm with the provided name
    .DESCRIPTION
        This method will create an new alarm in Security Center with the given name.  The return value will contain the Id of the new alarm
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new alarm upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        New-SCAlarm -n "MyNewAlarm"
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        nsal "MyNewAlarm"
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Alarms
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias rsal Remove-SCAlarm
Function Remove-SCAlarm {
    <#
    .Synopsis
        This method will remove the alarm represented by the provided AlarmId from Security Center
    .DESCRIPTION
        This method will permanently remove the specified alarm from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The AlarmId parameter represents the entity to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $newAlarm = New-SCAlarm -Name "MyAlarm"

        #remove the new user by providing the user object
        Remove-SCAlarm -id $newAlarm
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $newAlarm = nsal "MyAlarm"

        #remove the new alarm by providing the user object
        rsal $newAlarm
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AlarmId
        )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $AlarmId
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias rsalr Remove-SCAlarmRecipient
Function Remove-SCAlarmRecipient {
    <#
    .Synopsis
        Method used to remove a recipient from the given alarm
    .DESCRIPTION
        This Method will allow the user to remove a security center user from the recipient list of a security center alarm
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AlarmId will be used to specify the alarm we want to remove the user from

        The parameter UserId represents the Id of the user we want to remove from to the recipient list of the alarm
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        $us = New-SCEntity -n "MyNewUser" -t users

        Add-SCAlarmRecipient -AlarmId $al -UserId $us -PropagationLevel 10

        Remove-SCAlarmRecipient -AlarmId $al -UserId $us
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms

        $us = New-SCEntity -n "MyNewUser" -t users

        Add-SCAlarmRecipient -Id $al -uid $us

        Remove-SCAlarmRecipient -Id $al -uid $us
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $AlarmId,
        [parameter(Mandatory=$true)] [alias("uid")] $UserId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uid = GetIdFromObject $UserId
            Remove-SCEntityRelation -EntityId $AlarmId -RelationName "recipients" -RelationId $uid  
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias rsalae Remove-SCAlarmAttachedEntity
Function Remove-SCAlarmAttachedEntity {
    <#
    .Synopsis
        Method used to remove an attached entity from the given alarm
    .DESCRIPTION
        This Method will allow the user to remove a security center entity from the attached entity list of a security center alarm
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AlarmId will be used to specify the alarm we want to remove the user from

        The parameter EntityId represents the Id of the entity we want to remove from to the recipient list of the alarm
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCEntity -n "MyNewAlarm" -t alarms
        $cs = Search-SCEntities -n "Mycam" -t cameras

        Add-SCAlarmAttachedEntity -AlarmId $al -EntityId $cs

        Remove-SCAlarmAttachedEntity -AlarmId $al -EntityId $cs
        
        Exit-SCSession 

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $AlarmId,
        [parameter(Mandatory=$true)] [alias("eid")] $EntityId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $eid = GetIdFromObject $EntityId
            Remove-SCEntityRelation -EntityId $AlarmId -RelationName "AttachedEntities" -RelationId $eid  
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssal Set-SCAlarm
Function Set-SCAlarm() {
    <#
    .Synopsis
        Used to update the properties of an alarm in Security Center
    .DESCRIPTION
        This method is used to update the properties of an entity to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods

        The parameter Alarm represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = New-SCAlarm -n "MyNewAlarm"
                
        $al = Get-SCAlarm $al

        #update it's description
        $al.Description = "My new Alarm"

        #update config in SecurityCenter
        Set-SCAlarm -Alarm $al

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        #Will create a new entity of type alarm with the name MyNewAlarm
        $al = nsal -n "MyNewAlarm2" | gsal
                
        #update it's description
        $al.Description = "My new Alarm 2"

        #update config in SecurityCenter
        ssal $al

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("al")] $Alarm
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Alarm
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssals Show-SCAlarmReport
Function Show-SCAlarmReport {
    <#
    .Synopsis
        Used to retrieve an alarm report
    .DESCRIPTION
        This method will return an alarm report for all the specified alarms.  The report will contain a Results property
        that contains 2 arrays.  The first array will be the column definition.  This will contain the information this will be in each rows.
        The second array is the actual data of the report matching the column definition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The first parameter is AlarmIdList which contains the list of all alarms we want to show in the report

        The second parameter is AlarmState which contains the state of the alarms we want to query (optional) 
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $myAlarm1 = New-SCAlarm -Name "MyNewAlarm1" | Get-SCAlarm
        $myAlarm2 = New-SCAlarm -Name "MyNewAlarm2" | Get-SCAlarm

        $AdminUser = Search-SCEntities -Name "admin" -Type users

        Add-SCAlarmRecipient -AlarmId $myAlarm1 -UserId $AdminUser -PropagationLevel 10
        Add-SCAlarmRecipient -AlarmId $myAlarm2 -UserId $AdminUser -PropagationLevel 10

        $alarmInstanceId1 = Invoke-SCAlarmTrigger $myAlarm1
        $alarmInstanceId2 = Invoke-SCAlarmTrigger $myAlarm2

        Invoke-SCAlarmAck -AckType Ack -AlarmInstance $alarmInstanceId1.Id
        Invoke-SCAlarmAck -AckType Ack -AlarmInstance $alarmInstanceId2.Id

        Show-SCAlarmReport -AlarmState Acked -AlarmIdList $myAlarm1, $myAlarm2
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$false, ValueFromPipeline=$true)] [alias("ail")] $AlarmIdList
    )
 
    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'AlarmState'
        $ParameterAlias = 'as'
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $false
        $ParameterAttribute.Position = 1

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        # Generate and set the ValidateSet 
        $sess = GetSession -Quiet $true
        if ( $sess -ne $null ) { 
            $arrSet = $sess.SCAlarmStates

            $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection.Add($ValidateSetAttribute)
        }

         #add the alias to the attributes collection
        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)
        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $AlarmState = $PsBoundParameters[$ParameterName]
    }

    process {
        SCCmdletImplementation  MyInvocation.InvocationName {
            $alarmsParam = ""

            if($AlarmIdList) {
               $alarmsParam = "?Alarms="

               $AlarmIdList | foreach{ $alarmsParam = $alarmsParam + (GetIdFromObject $_) + ","}
               $alarmsParam = $alarmsParam.Remove($alarmsParam.Length - 1)
            }

            if($AlarmState){
                if($AlarmIdList){
                    $alarmsParam = $alarmsParam + '&'
                }
                else{
                    $alarmsParam = $alarmsParam + '?'
                }
                $alarmsParam = $alarmsParam + 'States=' + $AlarmState
            }
        

            $uri = "Reports/Alarms"  + $alarmsParam

            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        } 
    } 
}

# -----------------------------------------------------------------------------
Set-Alias ssalp Show-SCAlarmProperties
Function Show-SCAlarmProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an Alarm
    .DESCRIPTION
        This method will list the supported properties and relation of an Alarm (the data model, not the actual data).  This method is used
        when you want to know what is available for a given alarm
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCAlarmProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiAlarm" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'

