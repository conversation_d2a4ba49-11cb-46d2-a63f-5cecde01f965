using LPPermission.UI.Models;
using LPPermission.UI.Services.Interface;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Options;
using Telerik.Blazor.Components;

namespace LPPermission.UI.Pages
{
    public partial class UploadFile
    {
        // Dependency injection for navigation, HTTP client, API settings, and telemetry client
        [Inject] public NavigationManager Navigation { get; set; } = default!;
        [Inject] public HttpClient Http { get; set; } = default!;
        [Inject] public IOptions<ApiSettings> ApiSettings { get; set; } = default!;
        [Inject] private ITelemetryClient TelemetryClient { get; set; } = default!;

        // Flags to control request behavior
        private bool AllowRequests { get; set; } = true;
        private bool ReturnSuccess { get; set; } = true;

        // Status message for UI feedback
        public string statusMessage = "";

        /// <summary>
        ///  Event handler for removing uploaded files
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task OnUploadRemove(UploadEventArgs args)
        {
            try
            {
                var file = args.Files.First();

                if (!AllowRequests)
                {
                    args.IsCancelled = true; // Cancel the remove operation
                    Console.WriteLine($"OnRemove event cancelled for {file.Name}");
                }
                else
                {
                    Console.WriteLine($"OnRemove event for {file.Name}");

                    // Add custom data and headers to the request
                    args.RequestData.Add("successData", ReturnSuccess);
                    args.RequestHeaders.Add("successHeader", ReturnSuccess);
                }
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during OnUploadRemove.");
            }
        }

        /// <summary>
        /// Event handler for selecting files to upload
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public async Task OnUploadSelect(UploadSelectEventArgs args)
        {
            try
            {
                Console.WriteLine("OnSelect event for:");

                foreach (var file in args.Files)
                {
                    Console.WriteLine($"  File: {file.Name}, Size: {file.Size} bytes");
                }

                // Cancel the operation if more than 5 files are selected
                if (args.Files.Count > 5)
                {
                    args.IsCancelled = true;
                    Console.WriteLine("OnSelect event cancelled.");
                }
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during OnUploadSelect.");
            }
        }

        /// <summary>
        /// Event handler for uploading files
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        private async Task OnUpload(UploadEventArgs args)
        {
            try
            {
                if (!args.Files.Any())
                {
                    return; // No files to upload
                }

                if (!AllowRequests)
                {
                    args.IsCancelled = true; // Cancel the upload operation
                    Console.WriteLine($"OnUpload event cancelled for:");
                }
                else
                {
                    // Add custom data and headers to the request
                    args.RequestData.Add("successData", ReturnSuccess);
                    args.RequestHeaders.Add("successHeader", ReturnSuccess);
                    Console.WriteLine($"OnUpload event for:");
                }

                foreach (var file in args.Files)
                {
                    Console.WriteLine($"  File: {file.Name}");
                }
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during OnUpload.");
            }
        }

        /// <summary>
        /// Event handler for cancelling an upload
        /// </summary>
        /// <param name="args"></param>
        public void OnUploadCancel(UploadCancelEventArgs args)
        {
            try
            {
                var file = args.Files.First();
                args.IsCancelled = true; // Cancel the operation
                Console.WriteLine($"OnCancel event cancelled.");
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during OnUploadCancel.");
            }
        }


        /// <summary>
        ///  Event handler for clearing uploaded files    
        /// </summary>
        /// <param name="args"></param>
        private void OnUploadClear(UploadClearEventArgs args)
        {
            try
            {
                if (args.Files.Count > 3)
                {
                    args.IsCancelled = true; // Cancel the clear operation
                    Console.WriteLine("OnClear event cancelled.");
                }
                else
                {
                    Console.WriteLine("OnClear event fired for:");

                    foreach (var file in args.Files)
                    {
                        Console.WriteLine($"  Name: {file.Name}, Size: {file.Size} bytes");
                    }
                }
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during OnUploadClear.");
            }
        }

        /// <summary>
        /// Event handler for upload errors
        /// </summary>
        /// <param name="args"></param>
        
        public void OnUploadError(UploadErrorEventArgs args)
        {
            try
            {
                Console.WriteLine($"OnError event for:");
                Console.WriteLine($"  File: {args.Files.First().Name}");
                Console.WriteLine($"  Operation: {args.Operation}");
                Console.WriteLine($"  Response Status Code: {args.Request.Status}");
                Console.WriteLine($"  Response Status Message: {args.Request.StatusText}");
                Console.WriteLine($"  Response Type: {args.Request.ResponseType}");
                Console.WriteLine($"  Response Text: {args.Request.ResponseText}");
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during OnUploadError.");
            }
        }

        /// <summary>
        /// Event handler for tracking upload progress
        /// </summary>
        /// <param name="args"></param>
        private void OnUploadProgress(UploadProgressEventArgs args)
        {
            try
            {
                Console.WriteLine($"OnProgress event for:");
                Console.WriteLine($"  File: {args.Files.First().Name}");
                Console.WriteLine($"  Progress: {args.Progress}");
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during OnUploadProgress.");
            }
        }

        /// <summary>
        /// Event handler for successful uploads
        /// </summary>
        /// <param name="args"></param>
        public void OnUploadSuccess(UploadSuccessEventArgs args)
        {
            try
            {
                Console.WriteLine($"OnSuccess event for:");
                Console.WriteLine($"  File: {args.Files.First().Name}");
                Console.WriteLine($"  Operation: {args.Operation}");
                Console.WriteLine($"  Response Status Code: {args.Request.Status}");
                Console.WriteLine($"  Response Status Message: {args.Request.StatusText}");
                Console.WriteLine($"  Response Type: {args.Request.ResponseType}");
                Console.WriteLine($"  Response Text: {args.Request.ResponseText}");
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during OnUploadSuccess.");
            }
        }
        /// <summary>
        /// Helper method to log exceptions using Application Insights 
        /// </summary>
        /// <param name="ex"></param>
        /// <param name="message"></param>
        
        private void LogException(Exception ex, string message)
        {
            var exceptionTelemetry = new ExceptionTelemetry(ex)
            {
                SeverityLevel = SeverityLevel.Error
            };
            exceptionTelemetry.Properties.Add("CustomMessage", message);
            TelemetryClient.TrackException(exceptionTelemetry);

            Console.WriteLine($"{message}: {ex.Message}");
        }
    }
}