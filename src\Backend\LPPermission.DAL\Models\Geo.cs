﻿using System;
using System.Collections.Generic;

namespace LPPermission.DAL.Models;

public partial class Geo
{
    public int GeoId { get; set; }

    public string? Name { get; set; }

    public string? Description { get; set; }

    public virtual ICollection<Container> Containers { get; set; } = new List<Container>();

    public virtual ICollection<Permission> Permissions { get; set; } = new List<Permission>();

    public virtual ICollection<UserGroup> UserGroups { get; set; } = new List<UserGroup>();
}
