param (
    [string]$IPAddress="**************",
    [string]$Username="Samurai",
    [string]$Credential="Password1!",
    [string]$ServerPassword="",
    [string]$LogFilePath="C:\Users\<USER>\source\repos\DurableFunctionNew\retry_logfile.log",
    [string]$OutputFilePath="C:\Users\<USER>\source\repos\DurableFunctionNew\result.txt"
    
)

function Write-Message {
    param (
        [string]$message
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Add-Content -Path $logFilePath -Value "$timestamp - $message"
}


# Initialize the output file
if (Test-Path $outputFilePath) {
    Remove-Item $outputFilePath
}

 try {
        if (-not $IPAddress) {
            throw "ComputerName parameter is missing."
        }

        # Read the credentials from the JSON file
        #$credentialsPath = "C:\Workspace\maliha_code\src\FunctionApp\credentials.json"
        #$credentials = Get-Content -Path $credentialsPath | ConvertFrom-Json

        $directoryPassword = $Credential
        $genetecServerPassword = $ServerPassword

        # Attempt to enter the SC session
        Write-Message "Attempting to enter SC session for $IPAddress..."
        Enter-SCSession -ComputerName $IPAddress -User $Username -DirectoryPassword $directoryPassword -GenetecServerPassword $genetecServerPassword

        # Check if the session was established
        if ($?) {
            Write-Message "SC session established successfully for $ComputerName."

            # Get all user groups
            $UserGroups = Get-SCEntities -Type UserGroups

            # Write the store name to the output file
            #"Store: $IPAddress" | Out-File -FilePath $outputFilePath -Append
          
             # Prepare the result object
        $result = @()

        foreach ($Group in $UserGroups) {
            $GroupID = $Group.ID
            $GroupName = $Group.Name
            if ($GroupID -ne $null) {
            $Privileges = Get-SCUserGroupPrivileges -UserGroupId $GroupID

            # Limit to 3 privileges per group
               # $Privileges = $Privileges | Select-Object -First 3
              # Write the user group name and ID to the file
              #  "User Group: $GroupName (ID: $GroupID)" | Out-File -FilePath $outputFilePath -Append

                # Write the privileges to the file
                #$Privileges | Out-File -FilePath $outputFilePath -Append

                # Add a blank line for readability
                #"" | Out-File -FilePath $outputFilePath -Append

            # Create a group object
            $groupObject = [PSCustomObject]@{
                GroupID    = $GroupID
                GroupName  = $GroupName
                Privileges = @()
            }

            # Add privileges to the group object
            foreach ($Privilege in $Privileges) {
                $privilegeObject = [PSCustomObject]@{
                    PrivilegeID   = $Privilege.Id
                    PrivilegeName = $Privilege.Description
                    PreviousState = $Privilege.State
                    State         = $Privilege.State
                    #Parents       = $Privilege.Parents
                    #SetChildren   = $Privilege.SetChildren
                    #SetParentsIfNecessary       = $Privilege.SetParentsIfNecessary
                }
                $groupObject.Privileges += $privilegeObject
            }
            }

            # Add the group object to the result
              # Convert the group object to JSON and add to the result
                $result += $groupObject | ConvertTo-Json -Depth 3
        }

        # Convert the result to JSON and write to the output file
        #$result | ConvertTo-Json -Depth 3 | Out-File -FilePath $OutputFilePath -Append

            Write-Message "Privileges for all user groups have been written to $outputFilePath."

            Exit-SCSession
            Write-Message "SC session exited successfully for $IPAddress."
            return $result
        } else {
            $errorMessage = "Failed to establish SC session for $IPAddress."
            Write-Error $errorMessage
            Write-Message $errorMessage
            Exit-SCSession
        }
    } catch {
        $errorMessage = "An error occurred for $IPAddress $($_.Exception.Message)"
        Write-Error $errorMessage
        Write-Message $errorMessage
        Exit-SCSession
    }