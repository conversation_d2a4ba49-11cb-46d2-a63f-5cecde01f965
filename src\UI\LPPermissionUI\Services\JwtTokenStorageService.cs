﻿using LPPermission.UI.Services.Interface;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using System.Threading.Tasks;

namespace LPPermission.UI.Services
{
    /// <summary>
    /// Service for securely storing, retrieving, and removing JWT tokens using session storage.
    /// </summary>
    public class JwtTokenStorageService : IJwtTokenStorageService
    {
        // Protected session storage for securely storing data in the browser
        private readonly ProtectedSessionStorage _protectedSessionStorage;

        // Key used to store the JWT token in session storage
        private const string _tokenKey = "authToken";

        /// <summary>
        /// Default constructor for JwtTokenStorageService.
        /// </summary>
        public JwtTokenStorageService()
        {
        }

        /// <summary>
        /// Constructor to initialize JwtTokenStorageService with session storage.
        /// </summary>
        /// <param name="protectedSessionStorage">Instance of ProtectedSessionStorage for secure storage.</param>
        public JwtTokenStorageService(ProtectedSessionStorage protectedSessionStorage)
        {
            _protectedSessionStorage = protectedSessionStorage;
        }

        /// <summary>
        /// Stores the JWT token in session storage.
        /// </summary>
        /// <param name="token">The JWT token to store.</param>
        public async Task SetTokenAsync(string token)
        {
            await _protectedSessionStorage.SetAsync(_tokenKey, token);
        }

        /// <summary>
        /// Retrieves the JWT token from session storage.
        /// </summary>
        /// <returns>The JWT token if it exists, otherwise null.</returns>
        public async Task<string> GetTokenAsync()
        {
            var result = await _protectedSessionStorage.GetAsync<string>(_tokenKey);
            return result.Success ? result.Value : null;
        }

        /// <summary>
        /// Removes the JWT token from session storage.
        /// </summary>
        public async Task RemoveTokenAsync()
        {
            await _protectedSessionStorage.DeleteAsync(_tokenKey);
        }
    }
}