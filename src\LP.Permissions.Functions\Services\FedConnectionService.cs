using System;
using System.Diagnostics;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Extensibility;
using FunctionApp.LPPermissions.Services.interfaces;
using Microsoft.Extensions.Logging;

namespace FunctionApp.LPPermissions.Services
{
    public class FedConnectionService : IFedConnectionService
    {
        private readonly string fedConnectScriptPath;
        private readonly string logFilePath;
        private readonly string ipJsonFilePath;
        private readonly TelemetryClient telemetryClient;
        private readonly int maxRetries;
        private readonly ILogger<FedConnectionService> _logger;

        public FedConnectionService(string fedConnectScriptPath, string logFilePath, string ipJsonFilePath, TelemetryClient telemetryClient, ILogger<FedConnectionService> logger, int maxRetries = 3)
        {
            this.fedConnectScriptPath = fedConnectScriptPath;
            this.logFilePath = logFilePath;
            this.ipJsonFilePath = ipJsonFilePath;
            this.telemetryClient = telemetryClient;
            this.maxRetries = maxRetries;
            _logger = logger;
        }

       
        public List<string> Connect(string ipAddress, string username, string credential, string serverCredential, out string systemInfo)
        {
            int retryCount = 0;
            bool success = false;
            systemInfo = string.Empty;
            List<string> storeIPs = new List<string>();
            _logger.LogInformation("Entring into connect funciton (Pipeline)");
            _logger.LogInformation("IPAddress: {ipAddress}", ipAddress);
            _logger.LogInformation("Username: {username}", username);
            _logger.LogInformation("Credential: {credential}", credential);
            _logger.LogInformation("ServerCredential: {serverCredential}", serverCredential);

            _logger.LogInformation("LogFilePath: {logFilePath}", logFilePath);

            _logger.LogInformation("IPJsonFilePath: {ipJsonFilePath}", ipJsonFilePath);

            _logger.LogInformation("FedConnectScriptPath: {fedConnectScriptPath}", fedConnectScriptPath);
            _logger.LogInformation("Starting FedConnect script execution...");
            while (retryCount < maxRetries && !success)
            {
                try
                {
                    
                    _logger.LogInformation("Attempt {RetryCount}: Running FedConnect script...", retryCount + 1);
                    LogToFile(logFilePath, $"Attempt {retryCount + 1}: Running FedConnect script...");

                    // Run FedConnect.ps1 to get the list of IPs
                    string fedConnectCommand = $"powershell.exe -NoProfile -ExecutionPolicy Bypass -File \"{fedConnectScriptPath}\" -IPAddress \"{ipAddress}\" -Username \"{username}\" -Credential \"{credential}\" -ServerPassword \"{serverCredential}\" -LogFilePath \"{logFilePath}\"";
                    _logger.LogInformation("Executing command: {Command}", fedConnectCommand);
                    LogToFile(logFilePath, $"Executing command: {fedConnectCommand}");

                    ProcessStartInfo fedConnectProcessStartInfo = new ProcessStartInfo("cmd.exe", "/c " + fedConnectCommand)
                    {
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    };

                    using (Process fedConnectProcess = new Process { StartInfo = fedConnectProcessStartInfo })
                    {
                        fedConnectProcess.Start();
                        string fedConnectOutput = fedConnectProcess.StandardOutput.ReadToEnd();
                        string fedConnectError = fedConnectProcess.StandardError.ReadToEnd();
                        fedConnectProcess.WaitForExit();

                        _logger.LogInformation("FedConnect Output: {Output}", fedConnectOutput);
                        LogToFile(logFilePath, $"FedConnect Output: {fedConnectOutput}");

                        _logger.LogError("FedConnect Error: {Error}", fedConnectError);
                        LogToFile(logFilePath, $"FedConnect Error: {fedConnectError}");

                        if (fedConnectProcess.ExitCode != 0)
                        {
                            throw new Exception(fedConnectError);
                        }

                        // Process output to extract IPs
                        string[] outputLines = fedConnectOutput.Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);
                        foreach (string line in outputLines)
                        {
                            if (Regex.IsMatch(line, @"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b"))
                            {
                                Match match = Regex.Match(line, @"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b");
                                if (match.Success)
                                {
                                    storeIPs.Add(match.Value);
                                }
                            }
                        }

                        // Write IPs to JSON file
                        File.WriteAllText(ipJsonFilePath, JsonConvert.SerializeObject(storeIPs, Formatting.Indented));
                        _logger.LogInformation("IP addresses written to JSON file: {FilePath}", ipJsonFilePath);
                        LogToFile(logFilePath, $"IP addresses written to JSON file: {ipJsonFilePath}");
                    }

                    success = true;
                    _logger.LogInformation("Attempt {RetryCount} succeeded.", retryCount + 1);
                    LogToFile(logFilePath, $"Attempt {retryCount + 1} succeeded.");
                    telemetryClient.TrackEvent("AttemptSucceeded", new Dictionary<string, string>
                    {
                        { "Attempt", (retryCount + 1).ToString() },
                        { "ScriptPath", fedConnectScriptPath }
                    });
                    systemInfo = "FedConnect successful";
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Attempt {RetryCount} failed.", retryCount + 1);
                    LogToFile(logFilePath, $"Attempt {retryCount + 1} failed: {ex.Message}");
                    telemetryClient.TrackEvent("AttemptFailed", new Dictionary<string, string>
                    {
                        { "Attempt", (retryCount + 1).ToString() },
                        { "ScriptPath", fedConnectScriptPath },
                        { "ErrorMessage", ex.Message }
                    });
                    retryCount++;
                    if (retryCount < maxRetries)
                    {
                        _logger.LogWarning("Retrying...");
                        LogToFile(logFilePath, "Retrying...");
                        telemetryClient.TrackEvent("Retrying", new Dictionary<string, string>
                        {
                            { "Attempt", (retryCount + 1).ToString() },
                            { "ScriptPath", fedConnectScriptPath }
                        });
                    }
                }
            }

            return storeIPs;
        }
        private void LogToFile(string filePath, string message)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(filePath, true))
                {
                    writer.WriteLine($"{DateTime.Now}: {message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to write to log file: {ex.Message}");
                telemetryClient.TrackException(ex);
            }
        }
    }
}
