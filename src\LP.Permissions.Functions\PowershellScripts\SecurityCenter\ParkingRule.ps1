﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Get-SCParkingRule {
    <#
    .Synopsis
        This method will return all the properties of the parking zone rule represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the parking zone rule represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ParkingRuleId parameter represents the Id of the parking zone rule to retrieve (The guid representing the entity in the Security Center System)
        You can also pass any parking zone rule object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myParkingRule = New-SCParkingRule -n "MyParkingRulePS"

        Get-SCParkingRule -ParkingRuleId $myParkingRule.Id

        #Exit the session
        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ParkingRuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $ParkingRuleId
            Get-SCEntity -EntityId $id
        }
    }
}
# -----------------------------------------------------------------------------
Function New-SCParkingRule {
    <#
    .Synopsis
        Method used to create a new parking zone rule with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new parking zone rule with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new parking zone rule upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        $ParkingRuleId = New-SCParkingRule -n "MyParkingRulePS"

        Exit-SCSession     
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t ParkingRules
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCParkingRule {
    <#
    .Synopsis
        Will remove the parking zone rule represented by the provided ParkingRuleId parameter from Security Center
    .DESCRIPTION
        This method will permanently remove the specified parking zone rule from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ParkingRuleId parameter represents the credential to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        $parkingRuleId = New-SCParkingRule -n "MyParkingRulePS"

        Remove-SCParkingRule -ParkingRuleId $parkingRuleId

        Exit-SCSession     
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ParkingRuleId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $ParkingRuleId
            Remove-SCEntity -EntityId $id
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCParkingRule() {
    <#
    .Synopsis
        Used to update the properties of a parking zone rule in Security Center
    .DESCRIPTION
        This method is used to update the properties of a parking zone rule to Security Center.  All properties that are not read-only will be updated.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter ParkingRule represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        # Create a new parking zone rule.
        $myParkingRule = New-SCParkingRule -Name "MyParkingRule" | Get-SCParkingRule
        
        # Update parking rule properties. Set convenience and grace period to 1 minute
        $myParkingRule.ConvenienceTime = "0.00:01:00"
        $myParkingRule.GracePeriod = "0.00:01:00"
        
        # Update the entity.
        Set-SCParkingRule -ParkingRule $myParkingRule 

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("h")] $ParkingRule
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $ParkingRule
        }
    }
}
# -----------------------------------------------------------------------------
Function Show-SCParkingRuleProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a parking zone rule
    .DESCRIPTION
        This method will list the supported properties and relation of an parking zone rule (the data model, not the actual data).  This method is used
        when you want to know what is available for a parking zone rule
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
        
        Show-SCParkingZoneProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiParkingRule" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'
