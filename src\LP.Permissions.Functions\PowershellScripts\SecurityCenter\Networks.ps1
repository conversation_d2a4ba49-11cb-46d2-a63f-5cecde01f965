﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCNetworkServer {
    <#
    .Synopsis
        Method used to add a server to the given network
    .DESCRIPTION
        This Method will allow the user to add a security center server to the specified network
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter NetworkId will be used to specify the network we want to add the server to

        The parameter ServerId represents the Id of the server we want to add to the network
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = New-SCNetwork -Name "MyNewNetwork" | Get-SCNetwork

        $servers = Get-SCEntities -Type "Servers" -f All
        $servers | foreach{ Add-SCNetworkServer $myNewNetwork -ServerId $_.Id }
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $NetworkId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sid")] $ServerId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $ServerId
            Set-SCEntityRelation -EntityId $NetworkId -RelationName "servers" -RelationId $sid   
        }
    }   
}

# -----------------------------------------------------------------------------
Function Add-SCNetworkSubnetworks {
    <#
    .Synopsis
        Method used to add a subnetwork to the given network
    .DESCRIPTION
        This Method will allow the user to add a security center network to another specified network
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter NetworkId will be used to specify the network we want to add the subnetwork to

        The parameter SubnetworkId represents the Id of the network we want to add to the parent network
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = New-SCNetwork -Name "MyNewNetwork" | Get-SCNetwork

        $myNewSubNetwork = New-SCNetwork -Name "MyNewSubNetwork" | Get-SCNetwork

        Add-SCNetworkSubnetworks -NetworkId $myNewNetwork -SubnetworkId $myNewSubNetwork
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $NetworkId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("snid")] $SubnetworkId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $SubnetworkId
            Set-SCEntityRelation -EntityId $NetworkId -RelationName "subnetworks" -RelationId $sid   
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsnet Get-SCNetwork
Function Get-SCNetwork {
    <#
    .Synopsis
        This method will return all the properties of the network represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the network represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The NetworkId parameter represents the Id of the network to retrieve (The guid representing the network in the Security Center System)
        You can also pass any network object that contains an ID as a parameter
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = New-SCNetwork -Name "MyNewNetwork"
        Get-SCNetwork -NetworkId $myNewNetwork
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = nsnet -n "MyNewNetwork" | gsnet
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = nsnet "MyNewNetwork" | gsnet
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $NetworkId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $NetworkId  
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCNetworkRoutes {
    <#
    .Synopsis
        Method used to get all routes of the given network
    .DESCRIPTION
        This Method will allow the user to get all routes of the given network
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter NetworkId will be used to specify the network we want to get the routes from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = New-SCNetwork -Name "MyNewNetwork" | Get-SCNetwork
        
        Get-SCNetworkRoutes $myNewNetwork

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $NetworkId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $NetworkId -RelationName "routes"
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCNetworkServers {
    <#
    .Synopsis
        Method used to get all servers of the given network
    .DESCRIPTION
        This Method will allow the user to get all servers of the given network
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter NetworkId will be used to specify the network we want to get the servers from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = New-SCNetwork -Name "MyNewNetwork" | Get-SCNetwork

        $servers = Get-SCEntities -Type "Servers" -f All
        $servers | foreach{ Add-SCNetworkServer $myNewNetwork -ServerId $_.Id }

        Get-SCNetworkServers $myNewNetwork
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $NetworkId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $NetworkId -RelationName "servers"
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCNetworkSubnetworks {
    <#
    .Synopsis
        Method used to get all subnetworks of the given network
    .DESCRIPTION
        This Method will allow the user to get all subnetworks of the given network
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter NetworkId will be used to specify the network we want to get the subnetworks from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = New-SCNetwork -Name "MyNewNetwork" | Get-SCNetwork

        $myNewSubNetwork = New-SCNetwork -Name "MyNewSubNetwork" | Get-SCNetwork

        Add-SCNetworkSubnetworks -NetworkId $myNewNetwork -SubnetworkId $myNewSubNetwork

        Get-SCNetworkSubnetworks $myNewNetwork
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $NetworkId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $NetworkId -RelationName "subnetworks"
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias nsnet New-SCNetwork
Function New-SCNetwork {
    <#
    .Synopsis
        Method used to create a new network with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new network with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new network upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = New-SCNetwork -Name "MyNewNetwork"
        
        Exit-SCSession  
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = nsnet -n "MyNewNetwork"
        
        Exit-SCSession  
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Networks
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias rsnet Remove-SCNetwork
Function Remove-SCNetwork {
    <#
    .Synopsis
        Will remove the network represented by the provided NetworkId from Security Center
    .DESCRIPTION
        This method will permanently remove the specified network from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The NetworkId parameter represents the entity to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = New-SCNetwork -Name "MyNewNetwork"

        Remove-SCNetwork -NetworkId $myNewNetwork.Id

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = New-SCNetwork -Name "MyNewNetwork"

        rsnet $myNewNetwork

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $NetworkId
        )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $NetworkId
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssnet Set-SCNetwork
Function Set-SCNetwork() {
    <#
    .Synopsis
        Used to update the properties of a network in Security Center
    .DESCRIPTION
        This method is used to update the properties of a network to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Network represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = New-SCNetwork -Name "MyNewNetwork" | Get-SCNetwork
        $myNewNetwork.Description = "TEST"

        Set-SCNetwork -Network $myNewNetwork

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewNetwork = nsnet "MyNewNetwork" | gsnet
        $myNewNetwork.Description = "TEST"

        ssnet $myNewNetwork

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("net")] $Network
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Network
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCNetworkProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a network
    .DESCRIPTION
        This method will list the supported properties and relation of a network (the data model, not the actual data).  This method is used
        when you want to know what is available for a given network
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCNetworkProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiNetwork" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}

Export-ModuleMember -Function '*-*' -Alias '*'