﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35312.102
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LPPermission.Models", "LPPermission.Models\LPPermission.Models.csproj", "{4E7C8F6F-9CA0-44F0-A4D3-F9190319F291}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LPPermission.BackEnd.Tests", "Tests\LPPermission.BackEnd.Tests\LPPermission.BackEnd.Tests.csproj", "{9F424864-E527-482C-BED7-4C235DA8900C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LPPermission.BLL", "LPPermission.BLL\LPPermission.BLL.csproj", "{B4930BEB-6D9E-4A29-8B23-EFE598F921C8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LPPermission.BLL.Tests", "Tests\LPPermission.BLL.Tests\LPPermission.BLL.Tests.csproj", "{309E66D0-EE86-4061-A7F7-71AE53BA921A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LPPermission.DAL", "LPPermission.DAL\LPPermission.DAL.csproj", "{31534754-5E17-4E98-BD10-DAF4183B912A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LPPermission.DAL.Tests", "Tests\LPPermission.DAL.Tests\LPPermission.DAL.Tests.csproj", "{830C7FAD-B670-41F5-BE7E-0D093B14B85A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "LPPermission.UI.API", "LPPermission.UI.API\LPPermission.UI.API.csproj", "{7738FF70-0B99-46B9-914A-BE7D40A3FCE5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{6140B1DB-DD72-4F83-BF89-7D8397409651}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4E7C8F6F-9CA0-44F0-A4D3-F9190319F291}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4E7C8F6F-9CA0-44F0-A4D3-F9190319F291}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4E7C8F6F-9CA0-44F0-A4D3-F9190319F291}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4E7C8F6F-9CA0-44F0-A4D3-F9190319F291}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F424864-E527-482C-BED7-4C235DA8900C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F424864-E527-482C-BED7-4C235DA8900C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F424864-E527-482C-BED7-4C235DA8900C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F424864-E527-482C-BED7-4C235DA8900C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4930BEB-6D9E-4A29-8B23-EFE598F921C8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4930BEB-6D9E-4A29-8B23-EFE598F921C8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4930BEB-6D9E-4A29-8B23-EFE598F921C8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4930BEB-6D9E-4A29-8B23-EFE598F921C8}.Release|Any CPU.Build.0 = Release|Any CPU
		{309E66D0-EE86-4061-A7F7-71AE53BA921A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{309E66D0-EE86-4061-A7F7-71AE53BA921A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{309E66D0-EE86-4061-A7F7-71AE53BA921A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{309E66D0-EE86-4061-A7F7-71AE53BA921A}.Release|Any CPU.Build.0 = Release|Any CPU
		{31534754-5E17-4E98-BD10-DAF4183B912A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{31534754-5E17-4E98-BD10-DAF4183B912A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{31534754-5E17-4E98-BD10-DAF4183B912A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{31534754-5E17-4E98-BD10-DAF4183B912A}.Release|Any CPU.Build.0 = Release|Any CPU
		{830C7FAD-B670-41F5-BE7E-0D093B14B85A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{830C7FAD-B670-41F5-BE7E-0D093B14B85A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{830C7FAD-B670-41F5-BE7E-0D093B14B85A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{830C7FAD-B670-41F5-BE7E-0D093B14B85A}.Release|Any CPU.Build.0 = Release|Any CPU
		{7738FF70-0B99-46B9-914A-BE7D40A3FCE5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7738FF70-0B99-46B9-914A-BE7D40A3FCE5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7738FF70-0B99-46B9-914A-BE7D40A3FCE5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7738FF70-0B99-46B9-914A-BE7D40A3FCE5}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{9F424864-E527-482C-BED7-4C235DA8900C} = {6140B1DB-DD72-4F83-BF89-7D8397409651}
		{309E66D0-EE86-4061-A7F7-71AE53BA921A} = {6140B1DB-DD72-4F83-BF89-7D8397409651}
		{830C7FAD-B670-41F5-BE7E-0D093B14B85A} = {6140B1DB-DD72-4F83-BF89-7D8397409651}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D138788C-DF8A-44CB-AFFB-2E1772F178BE}
	EndGlobalSection
EndGlobal
