﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <OutputType>Exe</OutputType>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	  <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
	  <ProjectGuid>{49E0DF30-B9FF-4F3B-8E8A-953EC1F2DDF5}</ProjectGuid>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="local.settings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.2" />
    <PackageReference Include="Azure.Identity" Version="1.13.2" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.23.0" />
    <!-- Application Insights isn't enabled by default. See https://aka.ms/AAt8mw4. -->
    <!-- <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" /> -->
    <!-- <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="2.0.0" /> -->
    <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.DurableTask" Version="1.2.3" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.2.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Core" Version="3.0.41" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Http" Version="3.2.0" />
    <PackageReference Include="Microsoft.DurableTask.Abstractions" Version="1.10.0" />
    <PackageReference Include="Microsoft.DurableTask.Client" Version="1.10.0" />
    <PackageReference Include="Microsoft.DurableTask.Client.Grpc" Version="1.10.0" />
    <PackageReference Include="Microsoft.DurableTask.Grpc" Version="1.10.0" />
    <PackageReference Include="Microsoft.DurableTask.Worker" Version="1.10.0" />
    <PackageReference Include="Microsoft.DurableTask.Worker.Grpc" Version="1.10.0" />
    <PackageReference Include="Microsoft.PowerShell.SDK" Version="7.5.0" />
    <PackageReference Include="System.Management.Automation" Version="7.4.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LPPermission.BLL\LPPermission.BLL.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="host.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
    <None Update="PowershellScripts\FedConnect.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\FedConnectOld.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\GetPermissionData.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\UpdatePermission.Old.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\UpdatePermission.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="result.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="retry_logfile.log">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\AccessControlUnits.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\AccessManagerRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\AccessRules.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\ActiveDirectoryRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Alarms.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\ArchiverRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Areas.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\AuthenticationServiceRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\AuxiliaryArchiverRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\BwcManagerRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Cameras.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\CameraSequences.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\CardHolderGroups.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\CardHolders.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Credentials.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\CustomDataTypes.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\CustomFields.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\DirectoryManagerRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Doors.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\DoorTemplates.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\EntityBase.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\FederationRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\HealthMonitorRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Hotlists.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\InterfaceModuleDeviceDefinitions.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\InterfaceModules.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\IntrusionDetectionRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\IntrusionUnit.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\LicensePlateManagement.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\LprManagerRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\LprUnit.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Macros.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Maps.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\MediaRouterRole.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\MlpiRules.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Networks.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\OvertimeRules.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\ParkingRule.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\ParkingZone.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Partitions.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\PermitRules.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Permits.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\ReverseTunnel.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Roles.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Schedules.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\SecurityCenter.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\SecurityCenter.psd1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\SecurityCenter.psm1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\ServerAdmin.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Streams.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\UserGroups.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Users.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\VideoUnits.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="PowershellScripts\SecurityCenter\Visitor.ps1">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="store_ips.log">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
  </ItemGroup>
</Project>