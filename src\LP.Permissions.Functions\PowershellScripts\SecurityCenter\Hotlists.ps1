# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Get-SCHotlist {
    <#
    .Synopsis
        This method will return all the properties of the hotlist represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the hotlist represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The HotlistId parameter represents the Id of the hotlist to retrieve (The guid representing the entity in the Security Center System)
        You can also pass any hotlist object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myHotlist = New-SCHotlist -n "MyHotlistPS"

        Get-SCHotlist -HotlistId $myHotlist.Id

        #Exit the session
        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $HotlistId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $HotlistId           
        }
    }
}
# -----------------------------------------------------------------------------
Function New-SCHotlist {
    <#
    .Synopsis
        Method used to create a new hotlist with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new hotlist with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new hotlist upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $hotlistId = New-SCHotlist -n "MyHotlistPS"

        Exit-SCSession     
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Hotlists
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCHotlist {
    <#
    .Synopsis
        Will remove the hotlist represented by the provided HotlistId parameter from Security Center
    .DESCRIPTION
        This method will permanently remove the specified hotlist from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The HotlistId parameter represents the credential to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Remove-SCHotlist -HotlistId $myHotlist.Id
        Exit-SCSession     
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $HotlistId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $HotlistId
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCHotlist() {
    <#
    .Synopsis
        Used to update the properties of a hotlist in Security Center
    .DESCRIPTION
        This method is used to update the properties of a hotlist to Security Center.  All properties that are not read-only will be updated.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Hotlist represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new hotlist.
        $myHotlist = New-SCHotlist -Name "MyHotlist" | Get-SCHotlist
        # Set the hotlist file path.
        $myHotlist.Path = "D:\hotlist\a.txt"
        # Update the entity.
        Set-SCHotlist -Hotlist $myHotlist 

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("h")] $Hotlist
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Hotlist
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCHotlistProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a hotlist
    .DESCRIPTION
        This method will list the supported properties and relation of an hotlist (the data model, not the actual data).  This method is used
        when you want to know what is available for a hotlist
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCHotlistProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiHotlist" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'
