# ==========================================================================
# Copyright (C) 1989-2021 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias ascadfs Add-SCAdfsRole
Function Add-SCAdfsRole {
    <#
    .Synopsis
        This method will add a ADFS role.
    .DESCRIPTION
        This method is used when the user wants to add an ADFS role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter RoleName is used to specify the name given to the open id role you want to create.

        The parameter ServerId is used to specify the server where the role will run.

    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $serverToAdd = Get-SCEntities -Type Servers

        #make sure to select the server entity that you need to add to the archiver
        Add-SCAdfsRole -RoleName "ADFS role" -ServerId $serverToAdd[0]
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("name")] $RoleName,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sid")] $ServerId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $ServerId

            $hash = [ordered]@{RoleSubType = "C07A88BD-6E40-4849-B379-88E1BC54474C"}

            New-SCRole -Name $RoleName -Type AuthRole -ServerId $serverId -Extra $hash
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ascsaml2 Add-SCSaml2Role
Function Add-SCSaml2Role {
    <#
    .Synopsis
        This method will add a SAML2 role.
    .DESCRIPTION
        This method is used when the user wants to add a SAML2 role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter RoleName is used to specify the name given to the open id role you want to create.

        The parameter ServerId is used to specify the server where the role will run.

    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $serverToAdd = Get-SCEntities -Type Servers

        #make sure to select the server entity that you need to add to the archiver
        Add-SCSamlRole -RoleName "SAML role" -ServerId $serverToAdd[0]
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("name")] $RoleName,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sid")] $ServerId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $ServerId

            $hash = [ordered]@{RoleSubType = "2D68F9C4-1349-44E1-92B2-6E32A22FA25D"}

            New-SCRole -Name $RoleName -Type AuthRole -ServerId $serverId -Extra $hash
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ascoid Add-SCOpenIdRole
Function Add-SCOpenIdRole {
    <#
    .Synopsis
        This method will add an open id role.
    .DESCRIPTION
        This method is used when the user wants to add an open id role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter RoleName is used to specify the name given to the open id role you want to create.

        The parameter ServerId is used to specify the server where the role will run.

    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $serverToAdd = Get-SCEntities -Type Servers

        #make sure to select the server entity that you need to add to the archiver
        Add-SCOpenIdRole -RoleName "OpenIdRole" -ServerId $serverToAdd[0]
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("name")] $RoleName,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sid")] $ServerId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $ServerId

            $hash = [ordered]@{RoleSubType = "AACCF493-6B59-4AAC-8AE2-3682D34FDFF5"}

            New-SCRole -Name $RoleName -Type AuthRole -ServerId $serverId -Extra $hash
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssasp Show-SCAuthenticationServiceProperties
Function Show-SCAuthenticationServiceProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an Authentication Service role.
    .DESCRIPTION
        This method will list the supported properties and relation of the authentication role(the data model, not the actual data).  This method is used
        when you want to know what is available for a given authentication role.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCAuthenticationServiceProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiAuthenticationRole" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}

# -----------------------------------------------------------------------------
Set-Alias ascugtar Add-SCUserGroupToAuthenticationRole
Function Add-SCUserGroupToAuthenticationRole {
    <#
    .Synopsis
        This method will add a user group to an authetication role.
    .DESCRIPTION
        This method is used when the user wants to add a user group to an authentication role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter RoleId is used to specify the role to add the user group to.

        The parameter UserGroupId is used to specify the user group to add.

    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #make sure to select the server entity that you need to add to the archiver
        Add-SCUserGroupToAuthenticationRole -RoleId "roleId" -UserGroupId "userGroupId"
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("rid")] $RoleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("ugid")] $UserGroupId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $ugid = GetIdFromObject $UserGroupId
            $rid = GetIdFromObject $RoleId

            $uri = '/entities/' + $rid + '/usergroups'
            $body = @{Id = $ugid}| ConvertTo-Json

            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $body
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias aadfstc Add-SCAdfsTrustChain
Function Add-SCAdfsTrustChain {
    <#
    .Synopsis
        This method will add a trust chain to an ADFS authetication role.
    .DESCRIPTION
        This method is used when the user wants to add a trust chain an ADFS authentication role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter RoleId is used to specify the role to add the user group to.

        The parameter Domain is used to specify the domain name.

        The parameter URL is used to specify the URL.

        The parameter RelayingParty is used to specify the relaying party.

        The parameter PassiveAuthentication is used to enable/disable passive authentication.

    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #make sure to select the server entity that you need to add to the archiver
        Add-SCAdfsTrustChain -RoleId "roleId" -Domain "domain name" -URL "url" -RelayingParty "relaying party" -PassiveAuthentication $true
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("rid")] $RoleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("dmn")] $Domain,
        [parameter(Mandatory=$true,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("uri")] $URL,
        [parameter(Mandatory=$true,Position=3,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("rp")] $RelayingParty,
        [parameter(Mandatory=$true,Position=4,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pa")] $PassiveAuthentication
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId

            $uri = '/entities/' + $rid + '/trustchains'

            $jsonDomainObject = [ordered]@{}
            $jsonDomainObject.Add("FullyQualifiedForm", $null)
            $jsonDomainObject.Add("NetbiosForm", $Domain)

            $jsonDomains = @()
            $jsonDomains += $jsonDomainObject

            $jsonObject = [ordered]@{}
            $jsonObject.Add("Domains", $jsonDomains) 
            $jsonObject.Add("URL", $URL)
            $jsonObject.Add("RelyingPartyId", $RelayingParty)
            $jsonObject.Add("EnablePassiveAuth", $PassiveAuthentication)
            $jsonBody = $jsonObject | ConvertTo-Json

            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias gnepts Get-SCNetworkEndpoints
Function Get-SCNetworkEndpoints {
    <#
    .Synopsis
        This method will get the network endpoints for OpenId and Saml2 roles.
    .DESCRIPTION
        This method is used to get the network endpoints for OpenId and Saml2 roles.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter RoleId is used to specify the role to get the network endpoints for.

    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Get-SCNetworkEndpoints -RoleId "roleId"
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("rid")] $RoleId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId

            $uri = '/entities/' + $rid + '/NetworkEndPoints'

            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        }
    }
}

# -----------------------------------------------------------------------------

Set-Alias soidccs Set-SCOpenIdClientSecret
Function Set-SCOpenIdClientSecret {
    <#
    .Synopsis
        This method will set the client secret value of an OpenId role.

    .DESCRIPTION
        This method is used to set the client secret on an OpenId role if the option IsClientConfidential is set to $true.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter RoleId is used to specify the role to get the network endpoints for.
        The parameter ClientSecret is used to specify the secret value to use.

    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Set-SCOpenIdClientSecret -RoleId "roleId" -ClientSecret "2134dascsdd2$%34qrwvb45t"
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("cs")][string]$ClientSecret
       
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {

            $rid = GetIdFromObject $RoleId
            $jsonObject = [ordered]@{}
            $jsonObject.Add("Id", "Role.OpenId.ClientSecret")
            $jsonObject.Add("Payload", $ClientSecret)
            $jsonBody = $jsonObject | ConvertTo-Json
            $uri = "Entities/$rid/SecureData"
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody

        }
    }
}
# -----------------------------------------------------------------------------


Export-ModuleMember -Function '*-*' -Alias '*'
