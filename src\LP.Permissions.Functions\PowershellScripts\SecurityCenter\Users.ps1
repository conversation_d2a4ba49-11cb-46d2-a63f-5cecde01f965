﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCUserUserGroups {
    <#
    .Synopsis
        Method used to add a usergroup to the given user
    .DESCRIPTION
        This Method will allow the user to add a security center usergroup to a specified security center user
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserId will be used to specify the user we want to add to the usergroup to

        The parameter UserGroupId represents the Id of the usergroup we want to add to the user
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUser = New-SCUser -Name "MyNewRestUser" -Password "" | Get-SCUser
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Add-SCUserUserGroups -UserId $myNewUser -UserGroupId $myNewRestUserGroup

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("ugid")] $UserGroupId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $ugid = GetIdFromObject $UserGroupId
            Set-SCEntityRelation -EntityId $UserId -RelationName "usergroups" -RelationId $ugid   
        }
    }    
}

# -----------------------------------------------------------------------------
Function Add-SCUserAccessRight {
    <#
    .Synopsis
        Method used to add an access right to a user
    .DESCRIPTION
        This method will allow the user to grant access to a given partition to a Security Center user.
    .PARAMETER UserId
        Used to specify the guid of the user that the access right will be added to
    .PARAMETER PartitionId
        Used to specify the guid of the partition that the user will be granted access to
    .EXAMPLE
        Import-Module SecurityCenter
        Enter-SCSession
        $user = Search-SCEntities -Type User -Name "user1"
        $partition = Search-SCEntities -type Partitions -Name "TestPartition"
        Add-SCUserAccessRight -UserId $user.id -PartitionId $partition.Id
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PartitionId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $pid = GetIdFromObject $PartitionId
            Set-SCEntityRelation -EntityId $UserId -RelationName "PartitionUserAccess" -RelationId $pid   
        }
    }    
}

# -----------------------------------------------------------------------------
Function Add-SCUserPartitionAdmin {
    <#
    .Synopsis
        Method used to add an partition admin rights to a user
    .DESCRIPTION
        This method will allow the user admin rights to a given partition..
    .PARAMETER UserId
        Used to specify the guid of the user that the partition admin rights will be added to
    .PARAMETER PartitionId
        Used to specify the guid of the partition that the user will be granted partition admin rights to
    .EXAMPLE
        Import-Module SecurityCenter
        Enter-SCSession
        $user = Search-SCEntities -Type User -Name "user1"
        $partition = Search-SCEntities -type Partitions -Name "TestPartition"
        Add-SCUserPartitionAdmin -UserId $user.id -PartitionId $partition.Id
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PartitionId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uid = GetIdFromObject $UserId
            $pid = GetIdFromObject $PartitionId
            Set-SCEntityRelation -EntityId $uid -RelationName "PartitionAdmin" -RelationId $pid   
        }
    }    
}

# -----------------------------------------------------------------------------
Function Remove-SCUserAccessRight {
    <#
    .Synopsis
        Method used to remove an access right to a user
    .DESCRIPTION
        This method will allow the user to revoke access to a given partition for a Security Center user.
    .PARAMETER UserId
        Used to specify the guid of the user that the access right will be removed from
    .PARAMETER PartitionId
        Used to specify the guid of the partition that the user will be no longer granted access to
    .EXAMPLE
        Import-Module SecurityCenter
        Enter-SCSession
        $user = Search-SCEntities -Type User -Name "user1"
        $partition = Search-SCEntities -type Partitions -Name "TestPartition"
        Remove-SCUserAccessRight -UserId $user.id -PartitionId $partition.Id
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PartitionId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $pid = GetIdFromObject $PartitionId
            Remove-SCEntityRelation -EntityId $UserId -RelationName "PartitionUserAccess" -RelationId $pid   
        }
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCUserAccessRights {
    <#
    .Synopsis
        Method used to get all the partition ids where the given user has access to.

    .DESCRIPTION
        This Method will allow the user to get all the partition ids where the given user has access to.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
        The parameter UserId will be used to specify the user we want to get the partition ids
    .EXAMPLEEnter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
		$user = Search-SCEntities -Type Users -Name "user1"
		Get-SCUserAccessRights -UserId $user
		Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uid = GetIdFromObject $UserId
            Get-SCEntity -EntityId $uid -RelationName PartitionUserAccess
        }
    }   
}

# -----------------------------------------------------------------------------
Function Remove-SCUserPartitionAdmin {
    <#
    .Synopsis
        Method used to Remove the partition admin rights to a user
    .DESCRIPTION
        This method will remove the user admin rights to a given partition..
    .PARAMETER UserId
        Used to specify the guid of the user that the partition admin rights will be removed from
    .PARAMETER PartitionId
        Used to specify the guid of the partition that the user's partition admin rights will be removed from
    .EXAMPLE
        Import-Module SecurityCenter
        Enter-SCSession
        $user = Search-SCEntities -Type User -Name "user1"
        $partition = Search-SCEntities -type Partitions -Name "TestPartition"
        Remove-SCUserPartitionAdmin -UserId $user.id -PartitionId $partition.Id
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PartitionId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uid = GetIdFromObject $UserId
            $pid = GetIdFromObject $PartitionId
            Remove-SCEntityRelation -EntityId $uid -RelationName "PartitionAdmin" -RelationId $pid   
        }
    }    
}

# -----------------------------------------------------------------------------
Function Add-SCUserGroupAccessRight {
    <#
    .Synopsis
        Method used to add an access right to a usergroup
    .DESCRIPTION
        This method will allow the user to grant access to a given partition to a Security Center user.
    .PARAMETER UserGroupId
        Used to specify the guid of the usergroup that the access right will be added to
    .PARAMETER PartitionId
        Used to specify the guid of the partition that the usergroup will be granted access to
    .EXAMPLE
        Import-Module SecurityCenter
        Enter-SCSession
        $usergroup = Search-SCEntities -Type Usergroups -Name "usergroup1"
        $partition = Search-SCEntities -type Partitions -Name "TestPartition"
        Add-SCUserGroupAccessRight -UserId $usergroup.id -PartitionId $partition.Id
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PartitionId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $pid = GetIdFromObject $PartitionId
            Set-SCEntityRelation -EntityId $UserGroupId -RelationName "PartitionUserAccess" -RelationId $pid   
        }
    }    
}

# -----------------------------------------------------------------------------
Function Remove-SCUserGroupAccessRight {
    <#
    .Synopsis
        Method used to remove an access right to a usergroup
    .DESCRIPTION
        This method will allow the user to revoke access to a given partition for a Security Center user.
    .PARAMETER UserGroupId
        Used to specify the guid of the usergroup that the access right will be removed from
    .PARAMETER PartitionId
        Used to specify the guid of the partition that the usergroup will no longer be granted access to
    .EXAMPLE
        Import-Module SecurityCenter
        Enter-SCSession
        $usergroup = Search-SCEntities -Type Usergroups -Name "usergroup1"
        $partition = Search-SCEntities -type Partitions -Name "TestPartition"
        Remove-SCUserGroupAccessRight -UserId $usergroup.id -PartitionId $partition.Id
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PartitionId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $pid = GetIdFromObject $PartitionId
            Remove-SCEntityRelation -EntityId $UserGroupId -RelationName "PartitionUserAccess" -RelationId $pid   
        }
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCUserGroupAccessRights {
    <#
    .Synopsis
        Method used to get all the partition ids where the given userGroup has access rights.

    .DESCRIPTION
        This Method will allow the user to get all the partition ids where the given usergroup has access rights.
        
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserGroupId will be used to specify the usergroup we want to get the partition ids
    .EXAMPLE
        Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        $userGroup = Search-SCEntities -Type Usergroups -Name "userGroup1"
        Get-SCUserGroupAccessRights -UserGroupId $userGroup
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserGroupId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uid = GetIdFromObject $UserGroupId
            Get-SCEntity -EntityId $uid -RelationName PartitionUserAccess
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsu Get-SCUser
Function Get-SCUser {
    <#
    .Synopsis
        This method will return all the properties of the user represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the user represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The UserId parameter represents the Id of the user to retrieve (The guid representing the user in the Security Center System)
        You can also pass any user object that contains an ID as a parameter
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUser = New-SCUser -Name "MyNewRestUser" -Password ""
        Get-SCUser $myNewUser

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $UserId     
        }
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCUserUserGroups {
    <#
    .Synopsis
        Method used to get all the usergroups of the given user
    .DESCRIPTION
        This Method will allow the user to get all usergroups of the given user
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserId will be used to specify the user we want to get the usergroups from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUser = New-SCUser -Name "MyNewRestUser" -Password "" | Get-SCUser
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Add-SCUserUserGroups -UserId $myNewUser -UserGroupId $myNewRestUserGroup
        Get-SCUserUserGroups -UserId $myNewUser

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {  
            Get-SCEntity -EntityId $UserId -RelationName "Usergroups"
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCUserPartitionAdmin {
    <#
    .Synopsis
        Method used to get all the partition ids where the given user is partition admin

    .DESCRIPTION
        This Method will allow the user to get all the partition ids where the given user is partition admin
        
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserId will be used to specify the user we want to get the partition ids
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $user = Search-SCEntities -Type Users -Name "user1"

        Get-SCUserPartitionAdmin -UserId $user

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uid = GetIdFromObject $UserId
            Get-SCEntity -EntityId $uid -RelationName PartitionAdmin
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCUserPrivileges {
    <#
    .Synopsis
        Method used to get all the user privileges of the given user
    .DESCRIPTION
        This Method will allow the user to get all user privileges of the given user
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserId will be used to specify the user we want to get the privilege from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUser = New-SCUser -Name "MyNewRestUser" -Password "" | Get-SCUser
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Get-SCUserPrivileges -UserId $myNewUser

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $UserId -RelationName privileges
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias nsu New-SCUser
Function New-SCUser {
    <#
    .Synopsis
        Method used to create a new user with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new security center user with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new user upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        New-SCUser -Name "MyNewUser" -Password "password"
        
        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name,
        [parameter(Mandatory=$true)] [alias("p")] [string]$Password
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -p $Password -t Users
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias rsu Remove-SCUser
Function Remove-SCUser {
    <#
    .Synopsis
        Will remove the user represented by the provided UserId from Security Center
    .DESCRIPTION
        This method will permantly remove the specified user from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The UserId parameter represents the user to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myUser = New-SCUser -Name "MyNewUser" -Password ""
        Remove-SCUser -UserId $myUser
        
        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $UserId
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCUserUserGroups {
    <#
    .Synopsis
        Method used to remove a usergroup from the given user
    .DESCRIPTION
        This Method will allow the user to remove a security center usergroup from a specified user
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserId will be used to specify the user we want to remove the usergroup from

        The parameter UserGroupId represents the Id of the usergroup we want to remove from the user
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUser = New-SCUser -Name "MyNewRestUser" -Password "" | Get-SCUser
        $myNewRestUserGroup = New-SCUserGroup -Name "MyNewRestUserGroup" | Get-SCUserGroup

        Add-SCUserUserGroups -UserId $myNewUser -UserGroupId $myNewRestUserGroup

        Remove-SCUserUserGroups -UserId $myNewUser -UserGroupId $myNewRestUserGroup

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("ugid")] $UserGroupId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $ugid = GetIdFromObject $UserGroupId
            Remove-SCEntityRelation -EntityId $UserId -RelationName "usergroups" -RelationId $ugid   
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias ssu Set-SCUser
Function Set-SCUser() {
    <#
    .Synopsis
        Used to update the properties of a user in Security Center
    .DESCRIPTION
        This method is used to update the properties of a user to Security Center.  All properties that are not read-only will be updated.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter User represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newu = New-SCUser -Name "MyUser" -Password "" | gsu
        $newu.Description = "test"
        
        Set-SCUser -User $newu

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newu = New-SCUser -Name "MyUser" -Password "" | gsu
        $newu.Description = "test"
        
        ssu $newu

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("u")] $User
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $User
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssup Set-SCUserPassword
Function Set-SCUserPassword() {
    <#
    .Synopsis
        Used to update the password of a user in Security Center
    .DESCRIPTION
        This method is used to update the password of a user to Security Center (for administrators only).
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserId represents and contains the properties that will be updated to security Center

        The parameter Password is used to specify the new password to update
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newu = New-SCUser -Name "MyUser" -Password "" | gsu

        Set-SCUserPassword -UserId $newu -Password "pass"

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newu = New-SCUser -Name "MyUser" -Password "" | gsu
        $newu.Description = "test"
        
        ssu $newu

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId,
        [parameter(Mandatory=$true)] [alias("p")] [string]$Password
    )

    SCCmdletImplementation $MyInvocation.InvocationName { 
        #special case for user password, it will be filtered out of the generic Set-User method
        $uid = GetIdFromObject $UserId
        $uri = "Entities/$uid/" 

        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Password", $Password)
        $jsonBody = $jsonObject | ConvertTo-Json

        InvokeSCRestMethod -UriSuffix $uri -Method 'PUT' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Function Set-SCUserPrivileges {
    <#
    .Synopsis
        Method used to set the state of the user privileges of the given user
    .DESCRIPTION
        This Method will set the state of the user privileges of the given user
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UserId will be used to specify the user we want to set to privilege to
        The parameter PrivilegeId will be used to specify the user privilege we want to update
        The parameter PrivilegeState will be used to specify the state of the given privilege
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewUser = New-SCUser -Name "MyNewRestUser" -Password "" | Get-SCUser
        $myUserPriviledge = Get-SCUserPrivileges -UserId $myNewUser
        $myUserPriviledge | foreach{ if($_.State -eq "Undefined"){ Set-SCUserPrivileges -UserId $myNewUser -PrivilegeId $_.Id -PrivilegeState 'Granted'}}

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UserId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PrivilegeId
    )
 
    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'PrivilegeState'
        $ParameterAlias = 'ps'
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $true
        $ParameterAttribute.Position = 3

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        # Generate and set the ValidateSet 
            
        $arrSet = 'Denied', 'Granted', 'Undefined'
        $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

        # Add the ValidateSet to the attributes collection
        $AttributeCollection.Add($ValidateSetAttribute)

         #add the alias to the attributes collection
        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)
        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $PrivilegeState = $PsBoundParameters[$ParameterName]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $uid = GetIdFromObject $UserId
            $pid = GetIdFromObject $PrivilegeId
            $uri = "Entities/$uid/Privileges/$pid"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("State", $PrivilegeState)
            $jsonBody = $jsonObject | ConvertTo-Json

            InvokeSCRestMethod -UriSuffix $uri -Method "Put" -Body $jsonBody
        }
    } 
} 

# -----------------------------------------------------------------------------
Function Show-SCUserProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a user
    .DESCRIPTION
        This method will list the supported properties and relation of a user (the data model, not the actual data).  This method is used
        when you want to know what is available for a given user
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCUserProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    
    SCCmdletImplementation $MyInvocation.InvocationName {
        $uri = "Help/Entities/ApiUser" 
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'