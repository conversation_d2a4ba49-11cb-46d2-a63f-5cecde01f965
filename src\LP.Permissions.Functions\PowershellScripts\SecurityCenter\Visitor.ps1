﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCVisitorCredential {
    <#
    .Synopsis
        Method used to add a credential to the given Visitor
    .DESCRIPTION
        This Method will allow the user to add a security center credential to a specified Visitor
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VisitorId will be used to specify the Visitor we want to add the credential to

        The parameter CredentialId represents the Id of the credential we want to add to the Visitor
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $vis = New-SCVisitor -n "MyNewVisitor"
        
        $cred = New-SCCredentials -n "MyNewCredential"

        Add-SCVisitorCredential -VisitorId $vis -CredentialId $cred
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VisitorId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cid")] $CredentialId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CredentialId
            Set-SCEntityRelation -EntityId $VisitorId -RelationName "credentials" -RelationId $cid   
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias gsvis Get-SCVisitor
Function Get-SCVisitor {
    <#
    .Synopsis
        This method will return all the properties of the Visitor represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the Visitor represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The VisitorId parameter represents the Id of the Visitor to retrieve (The guid representing the Visitor in the Security Center System)
        You can also pass any Visitor object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $vis = New-SCVisitor -n "MyNewVisitor"
        Get-SCVisitor -VisitorId $vis.Id

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $vis = New-SCVisitor -n "MyNewVisitor2" | Get-SCVisitor

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VisitorId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $VisitorId            
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCVisitorCredentials {
    <#
    .Synopsis
        Method used to get all credentials of the given Visitor
    .DESCRIPTION
        This Method will allow the user to get all credentials of the given Visitor
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VisitorId will be used to specify the Visitor we want to get the credentials from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $vis = New-SCVisitor -n "MyNewVisitor"
        
        $cred = New-SCCredentials -n "MyNewCredential"

        Add-SCVisitorCredential -VisitorId $vis -CredentialId $cred

        Get-SCVisitorCredentials -VisitorId $vis
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VisitorId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $VisitorId -RelationName "credentials"
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCVisitorPicture {
    <#
    .Synopsis
        Method used to get the picture of the given Visitor
    .DESCRIPTION
        This Method will allow the user to get the picture in base64String of the given Visitor
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VisitorId will be used to specify the Visitor we want to get the picture from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #make sure a Visitor with the name TestVisitor is in the system with a picture
        $vis = Search-SCEntities -Type Visitors -Name "TestVisitor"

        Get-SCVisitorPicture -VisitorId $vis
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VisitorId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $VisitorId -RelationName "Picture"
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias nsvis New-SCVisitor
Function New-SCVisitor {
    <#
    .Synopsis
        Method used to create a new Visitor with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new Visitor with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new Visitor upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $vis = New-SCVisitor -n "MyNewVisitor"

        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $vis = nsvis "MyNewVisitor"

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Visitors
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias rsvis Remove-SCVisitor
Function Remove-SCVisitor {
    <#
    .Synopsis
        Will remove the Visitor represented by the provided VisitorId from Security Center
    .DESCRIPTION
        This method will permanently remove the specified Visitor from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The VisitorId parameter represents the entity to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $vis = New-SCVisitor -n "MyNewVisitor"

        Remove-SCVisitor -VisitorId $vis.Id

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $vis = nsvis "MyNewVisitor"

        rsvis $vis

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VisitorId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $VisitorId
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCVisitorCredential {
    <#
    .Synopsis
        Method used to remove a credential from the given Visitor
    .DESCRIPTION
        This Method will allow the user to remove a security center credential from a specified Visitor
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VisitorId will be used to specify the Visitor we want to remove the credential from

        The parameter CredentialId represents the Id of the credential we want to remove from the Visitor
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $vis = New-SCVisitor -n "MyNewVisitor"
        
        $cred = New-SCCredentials -n "MyNewCredential"

        Add-SCVisitorCredential -VisitorId $vis -CredentialId $cred

        Remove-SCVisitorCredential -VisitorId $vis -CredentialId $cred
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VisitorId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cid")] $CredentialId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CredentialId
            Remove-SCEntityRelation -EntityId $VisitorId -RelationName "credentials" -RelationId $cid  
        }
    }   
}

# -----------------------------------------------------------------------------
Function Remove-SCVisitorPicture {
    <#
    .Synopsis
        Method used to remove the picture of the given Visitor
    .DESCRIPTION
        This Method will allow the user to remove the picture of the given Visitor
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VisitorId will be used to specify the Visitor we want to get the picture from
        The parameter PictureId will be used to specify the picture we want to remove from the Visitor
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #make sure a Visitor with the name TestVisitor is in the system with a picture
        $vis = Search-SCEntities -Type Visitors -Name "TestVisitor"

        $pic = Get-SCVisitorPicture -VisitorId $vis
        Remove-SCVisitorPicture -VisitorId $vis  -PictureId $pic.Guid
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VisitorId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PictureId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $pid = GetIdFromObject $PictureId
            Remove-SCEntityRelation -EntityId $VisitorId -RelationName "Picture" -RelationId $pid  
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias ssvis Set-SCVisitor
Function Set-SCVisitor() {
    <#
    .Synopsis
        Used to update the properties of an Visitor in Security Center
    .DESCRIPTION
        This method is used to update the properties of a Visitor to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Visitor represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newch = New-SCVisitor -Name "Mych"  | gsch
        $newch.FirstName = "test"
        
        Set-SCVisitor -Visitor $newch

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("ch")] $Visitor
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Visitor
        }
    }
}

# -----------------------------------------------------------------------------
Function Set-SCVisitorPicture {
    <#
    .Synopsis
        Method used to set the picture of the given Visitor
    .DESCRIPTION
        This Method will allow the user to set the picture in base64String of the given Visitor
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VisitorId will be used to specify the Visitor we want to get the picture from
        The parameter PictureInStringBase64 will be used to specify the picture of the Visitor
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #make sure a Visitor with the name TestVisitor is in the system with a picture
        $vis = Search-SCEntities -Type Visitors -Name "TestVisitor"

        #replace the picture file path with a valid one
        $picture = [convert]::ToBase64String((Get-Content D:\Images\image1.jpg -Encoding byte))
        Set-SCVisitorPicture -VisitorId $vis -PictureInStringBase64 $picture
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VisitorId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pic")] $PictureInStringBase64
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            $cid = GetIdFromObject $VisitorId
            $uri = "Entities/$cid/Picture"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Data", $PictureInStringBase64)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Post" -Body $jsonBody 
        }
    }  
}

# -----------------------------------------------------------------------------
Function Search-SCVisitorsAccessStatus {
    <#
    .Synopsis
        Searches security center Visitors and returns all Visitors who's status matches the provided one
    .DESCRIPTION
        This method is used to return all Visitor who's status matches the provided one

        The parameter AccessStatus is the status of the Visitors we want to search for

        The parameter Filter (optional) is used to specify the data returned by the method.  Base value will only return the minimum 
        data and the All Value will return all available data.  Base is the default value
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCVisitorsAccessStatus -AccessStatus Active -f All
                
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCVisitorsAccessStatus -as Active -f All
                
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
    )
 
    DynamicParam {
        $sess = GetSession -Quiet $true

        # Set the dynamic parameters' name
        $ParameterName1 = 'AccessStatus'
        $ParameterAlias1 = "as"
        $ParameterName2 = 'Filter'
        $ParameterAlias2 = "f"
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary
        
        # Create the collection of attributes
        $AttributeCollection1 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
        $AttributeCollection2 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute1 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute1.Mandatory = $true
        $ParameterAttribute1.Position = 1

        $ParameterAttribute2 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute2.Mandatory = $false
        $ParameterAttribute2.Position = 2

        # Add the attributes to the attributes collection
        $AttributeCollection1.Add($ParameterAttribute1)
        $AttributeCollection2.Add($ParameterAttribute2)

        # Generate and set the ValidateSet 
        if ($sess -ne $null) { 
            $arrSet1 = $sess.SCAccessStatus
            $ValidateSetAttribute1 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet1)

            $arrSet2 = $sess.SCFilterCache
            $ValidateSetAttribute2 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet2)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection1.Add($ValidateSetAttribute1)
            $AttributeCollection2.Add($ValidateSetAttribute2)
        }
        # don't validate if we can't retrieve
        
        #add the alias to the attributes collection
        $ParamAlias1 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias1
        $AttributeCollection1.Add($ParamAlias1)
        $ParamAlias2 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias2
        $AttributeCollection2.Add($ParamAlias2)

        # Create and return the dynamic parameter
        $RuntimeParameter1 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName1, [string], $AttributeCollection1)
        $RuntimeParameterDictionary.Add($ParameterName1, $RuntimeParameter1)

        $RuntimeParameter2 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName2, [string], $AttributeCollection2)
        $RuntimeParameterDictionary.Add($ParameterName2, $RuntimeParameter2)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $AccessStatus = $PsBoundParameters[$ParameterName1]
        $Filter = $PsBoundParameters[$ParameterName2]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uriSuffix = "Entities/Visitors?AccessStatus=" + $AccessStatus            

            if($Filter) {
                $uriSuffix = $uriSuffix + "&ValidFlags=" + $Filter
            }

            $result = InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'Get'

            if($IdOnly) {
                $result | foreach {Write-Output $_.Id}
            }
            else {
                return $result
            }
        }
    }
}

# -----------------------------------------------------------------------------
Function Search-SCVisitorsExpirationDate {
    <#
    .Synopsis
        Searches security center Visitors and returns all Visitor who's expiration date falls in the provided range
    .DESCRIPTION
        This method is used to return all Visitor who's expiration date falls in the provided range

        The parameter ExpirationStartTime is the start of the range to search for

        The parameter ExpirationEndTime is the end of the range to search for

        The parameter Filter (optional) is used to specify the data returned by the method.  Base value will only return the minimum 
        data and the All Value will return all available data.  Base is the default value
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCVisitorsExpirationDate -ExpirationStartTime (Get-Date).AddDays(-1) -ExpirationEndTime (Get-Date).AddDays(1) -f all
                
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCVisitorsExpirationDate -est (Get-Date).AddDays(-1) -eet (Get-Date).AddDays(1) -f all
                
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$false,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("est")] [DateTime]$ExpirationStartTime,
        [parameter(Mandatory=$false,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("eet")] [DateTime]$ExpirationEndTime
    )
 
    DynamicParam {
        $sess = GetSession -Quiet $true

        # Set the dynamic parameters' name
        $ParameterName2 = 'Filter'
        $ParameterAlias2 = "f"
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary
        
        # Create the collection of attributes
        $AttributeCollection2 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]

        $ParameterAttribute2 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute2.Mandatory = $false
        $ParameterAttribute2.Position = 2

        # Add the attributes to the attributes collection
        $AttributeCollection2.Add($ParameterAttribute2)

        # Generate and set the ValidateSet 
        if ($sess -ne $null) { 
            $arrSet2 = $sess.SCFilterCache
            $ValidateSetAttribute2 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet2)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection2.Add($ValidateSetAttribute2)
        }
        # don't validate if we can't retrieve
        
        #add the alias to the attributes collection
        $ParamAlias2 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias2
        $AttributeCollection2.Add($ParamAlias2)

        # Create and return the dynamic parameter
        $RuntimeParameter2 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName2, [string], $AttributeCollection2)
        $RuntimeParameterDictionary.Add($ParameterName2, $RuntimeParameter2)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $Filter = $PsBoundParameters[$ParameterName2]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uriSuffix = "Entities/Visitors?ExpirationTimeStart=" + $ExpirationStartTime.ToString("o") + "&ExpirationTimeEnd=" + $ExpirationEndTime.ToString("o")        

            if($Filter) {
                $uriSuffix = $uriSuffix + "&ValidFlags=" + $Filter
            }

            $result = InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'Get'

            if($IdOnly) {
                $result | foreach {Write-Output $_.Id}
            }
            else {
                return $result
            }
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCVisitorActivityReport {
    <#
    .Synopsis
        Used to retrieve a Visitor activity report
    .DESCRIPTION
        This method will return a Visitor activity report for all the activities of Visitors.  The report will contain a Results property
        that contains 2 arrays.  The first array will be the column definition.  This will contain the information this will be in each rows.
        The second array is the actual data of the report matching the column definition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter StartTime contains the start time of the report query

        The parameter EndTime  contains the end time of the report query

    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        $date = Get-Date
        Show-SCVisitorActivityReport -StartTime $date.AddMinutes(-20) -EndTime $date

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("st")] [DateTime]$StartTime,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("et")] [DateTime]$EndTime
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $uri = "Reports/VisitorActivities?TimeStart=" + $StartTime.ToString("o") + "&TimeEnd=" + $EndTime.ToString("o")
            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        }
    }  
}

# -----------------------------------------------------------------------------
Function Show-SCVisitorProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an Visitor
    .DESCRIPTION
        This method will list the supported properties and relation of an Visitor (the data model, not the actual data).  This method is used
        when you want to know what is available for a given Visitor
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCVisitorProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiVisitor" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'