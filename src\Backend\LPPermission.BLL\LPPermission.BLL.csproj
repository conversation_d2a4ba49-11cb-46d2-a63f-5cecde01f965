﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Interfaces\IData.cs" />
    <Compile Remove="Interfaces\ILoginService.cs" />
    <Compile Remove="Sevices\LoginService.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
    <PackageReference Include="Azure.Identity" Version="1.13.2" />
	<PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
	<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.5" />
	<PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />         
	<PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.3.0" />
	<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.2" />
	<PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.5.0" />
	<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LPPermission.DAL\LPPermission.DAL.csproj" />
    <ProjectReference Include="..\LPPermission.Models\LPPermission.Models.csproj" />
  </ItemGroup>

</Project>
