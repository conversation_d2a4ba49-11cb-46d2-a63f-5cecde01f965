﻿using AutoMapper;
using LPPermission.DAL.Interfaces;
using LPPermission.DAL.Models;

namespace LPPermission.BLL.Services
{
    public interface IUser
    {
        Task<bool> IsUserValidAsync(string username, string password);
    }
    public class User : IUser
    {
        private readonly IGenericRepository<UserLogin> _userRepository;  // Use User repository instead of Data
        

        public User(IGenericRepository<UserLogin> userRepository)
        {
            _userRepository = userRepository;  // Correct the repository type
            
        }

        // This method is to validate user credentials
        public async Task<bool> IsUserValidAsync(string username, string password)
        {
            try
            {
                // Fetch the user by username and password using the repository
                var result = await _userRepository.ValidateUserLoginAsync( username , password);
                // If user exists, is active, and password matches
                return result; // Passwords match and user is active
            }
            catch(Exception ex)
            {
                Console.WriteLine(ex.Message);
                return false;
            }
        }
    }
}
