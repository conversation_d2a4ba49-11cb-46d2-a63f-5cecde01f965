using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Azure.Security.KeyVault.Secrets;
using Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using Xunit;
using FunctionApp.LPFunctions;

namespace FunctionApp.LPFunctions.Tests
{
    public class LPManagerRunTests
    {
        private readonly Mock<ILogger<LPManager>> _loggerMock = new();
        private readonly Mock<IConfiguration> _configMock = new();

        public LPManagerRunTests()
        {
            _configMock.Setup(c => c["KeyVaultUrl"]).Returns("https://fake.vault/");
_configMock.Setup(c => c["Telemetry:Enabled"]).Returns("true");
        }

        private LPManager CreateManagerWithSecretClient(SecretClient secretClient)
        {
            
            LPManager manager = new LPManager(_loggerMock.Object, _configMock.Object);
            typeof(LPManager)
                .GetField("_secretClient", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .SetValue(manager, secretClient);
            return manager;
        }

        [Fact]
        public async Task Run_LogsError_WhenSecretIsNull()
        {
            // Arrange
            var secretClientMock = new Mock<SecretClient>(
                new Uri("https://fake.vault/"),
                new FakeCredential()
            );
            secretClientMock
                .Setup(s => s.GetSecretAsync("LPPermissionFunctionURL", null, It.IsAny<CancellationToken>()))
                .ThrowsAsync(new RequestFailedException("Secret not found"));
            var manager = CreateManagerWithSecretClient(secretClientMock.Object);

            // Act
            await manager.Run(null!, _loggerMock.Object);

            // Assert
            _loggerMock.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Secret not found")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task Run_LogsSuccess_WhenHttpOk()
        {
            // Arrange
            var secretValue = "https://test-url";
            var secret = new KeyVaultSecret("LPPermissionFunctionURL", secretValue);
            var secretClientMock = new Mock<SecretClient>(
                new Uri("https://fake.vault/"),
                new FakeCredential()
            );
            secretClientMock
                .Setup(s => s.GetSecretAsync("LPPermissionFunctionURL", null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(Response.FromValue(secret, null));

            // Note: Cannot mock static httpClient, so this will use the real HttpClient.

            var manager = CreateManagerWithSecretClient(secretClientMock.Object);

            // Act
            await manager.Run(null, _loggerMock.Object);

            // Assert
            _loggerMock.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("C# Timer trigger function LPPermssion executed from pipeline")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task Run_LogsFailed_WhenHttpNotOk()
        {
            // Arrange
            var secretValue = "https://test-url";
            var secret = new KeyVaultSecret("LPPermissionFunctionURL", secretValue);
            var secretClientMock = new Mock<SecretClient>(
                new Uri("https://fake.vault/"),
                new FakeCredential()
            );
            secretClientMock
                .Setup(s => s.GetSecretAsync("LPPermissionFunctionURL", null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(Response.FromValue(secret, null));

            // Note: Cannot mock static httpClient, so this will use the real HttpClient.

            var manager = CreateManagerWithSecretClient(secretClientMock.Object);

            // Act
            await manager.Run(null, _loggerMock.Object);

            // Assert
            _loggerMock.Verify(
     l => l.Log(
         LogLevel.Information,
         It.IsAny<EventId>(),
         It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("C# Timer trigger function LPPermssion executed from pipeline")),
         null,
         It.IsAny<Func<It.IsAnyType, Exception, string>>()),
     Times.Once);
        }

        [Fact]
        public async Task Run_LogsException_WhenExceptionThrown()
        {
            // Arrange
            var secretClientMock = new Mock<SecretClient>(
                new Uri("https://fake.vault/"),
                new FakeCredential()
            );
            secretClientMock
                .Setup(s => s.GetSecretAsync("LPPermissionFunctionURL", null, It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Test exception"));
            var manager = CreateManagerWithSecretClient(secretClientMock.Object);

            // Act
            await manager.Run(null, _loggerMock.Object);

            // Assert
            _loggerMock.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Test exception")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }
    }
}
