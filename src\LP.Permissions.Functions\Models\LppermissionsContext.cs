﻿//using System;
//using System.Collections.Generic;
//using Microsoft.EntityFrameworkCore;

//namespace LP.Permissions.StoreSetup.Models;

//public partial class LppermissionsContext : DbContext
//{
//    public LppermissionsContext()
//    {
//    }

//    public LppermissionsContext(DbContextOptions<LppermissionsContext> options)
//        : base(options)
//    {
//    }

//    public virtual DbSet<Container> Containers { get; set; }

//    public virtual DbSet<Geo> Geos { get; set; }

//    public virtual DbSet<Menu> Menus { get; set; }

//    public virtual DbSet<Permission> Permissions { get; set; }

//    public virtual DbSet<Privilege> Privileges { get; set; }

//    public virtual DbSet<State> States { get; set; }

//    public virtual DbSet<Store> Stores { get; set; }

//    public virtual DbSet<StoreUserGroup> StoreUserGroups { get; set; }

//    public virtual DbSet<UserGroup> UserGroups { get; set; }

//    public virtual DbSet<UserLogin> UserLogins { get; set; }

//    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
//#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
//        => optionsBuilder.UseSqlServer("Server=tcp:172.26.234.217,1433;Database=LPPermissions;Trusted_Connection=True;Encrypt=True;TrustServerCertificate=True;");

//    protected override void OnModelCreating(ModelBuilder modelBuilder)
//    {
//        modelBuilder.Entity<Container>(entity =>
//        {
//            entity.HasKey(e => e.ContainerId).HasName("PK__Containe__48725BD9A39B8200");

//            entity.ToTable("Containers", "dbo");

//            entity.Property(e => e.ContainerId)
//                .ValueGeneratedNever()
//                .HasColumnName("container_id");
//            entity.Property(e => e.Description)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("description");
//            entity.Property(e => e.GeoId).HasColumnName("geo_id");
//            entity.Property(e => e.Identifier)
//                .HasMaxLength(10)
//                .IsUnicode(false)
//                .HasColumnName("identifier");
//            entity.Property(e => e.Name)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("name");

//            entity.HasOne(d => d.Geo).WithMany(p => p.Containers)
//                .HasForeignKey(d => d.GeoId)
//                .HasConstraintName("FK__Container__geo_i__38996AB5");
//        });

//        modelBuilder.Entity<Geo>(entity =>
//        {
//            entity.HasKey(e => e.GeoId).HasName("PK__Geos__DEF8D90621292E9B");

//            entity.ToTable("Geos", "dbo");

//            entity.Property(e => e.GeoId)
//                .ValueGeneratedNever()
//                .HasColumnName("geo_id");
//            entity.Property(e => e.Description)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("description");
//            entity.Property(e => e.Name)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("name");
//        });

//        modelBuilder.Entity<Menu>(entity =>
//        {
//            entity.HasKey(e => e.MenuId).HasName("PK__Menu__4CA0FADC9528450F");

//            entity.ToTable("Menu", "dbo");

//            entity.Property(e => e.MenuId)
//                .ValueGeneratedNever()
//                .HasColumnName("menu_id");
//            entity.Property(e => e.Description)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("description");
//            entity.Property(e => e.MenuLevel).HasColumnName("menu_level");
//            entity.Property(e => e.Name)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("name");
//            entity.Property(e => e.ParentId).HasColumnName("parent_id");

//            entity.HasOne(d => d.Parent).WithMany(p => p.InverseParent)
//                .HasForeignKey(d => d.ParentId)
//                .HasConstraintName("FK__Menu__parent_id__440B1D61");
//        });

//        modelBuilder.Entity<Permission>(entity =>
//        {
//            entity.HasKey(e => e.PermissionId).HasName("PK__Permissi__E5331AFA5170F441");

//            entity.ToTable("Permissions", "dbo");

//            entity.Property(e => e.PermissionId)
//                .ValueGeneratedNever()
//                .HasColumnName("permission_id");
//            entity.Property(e => e.PrivilegeId).HasColumnName("privilege_id");
//            entity.Property(e => e.StateId).HasColumnName("state_id");
//            entity.Property(e => e.UserGroupId).HasColumnName("user_group_id");

//            entity.HasOne(d => d.Privilege).WithMany(p => p.Permissions)
//                .HasForeignKey(d => d.PrivilegeId)
//                .HasConstraintName("FK__Permissio__privi__5165187F");

//            entity.HasOne(d => d.State).WithMany(p => p.Permissions)
//                .HasForeignKey(d => d.StateId)
//                .HasConstraintName("FK__Permissio__state__619B8048");

//            entity.HasOne(d => d.UserGroup).WithMany(p => p.Permissions)
//                .HasForeignKey(d => d.UserGroupId)
//                .HasConstraintName("FK__Permissio__user___52593CB8");
//        });

//        modelBuilder.Entity<Privilege>(entity =>
//        {
//            entity.HasKey(e => e.PrivilegeId).HasName("PK__Privileg__F94BCCE25115E887");

//            entity.ToTable("Privileges", "dbo");

//            entity.Property(e => e.PrivilegeId)
//                .ValueGeneratedNever()
//                .HasColumnName("privilege_id");
//            entity.Property(e => e.Description)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("description");
//            entity.Property(e => e.GenetecRef)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("genetec_ref");
//            entity.Property(e => e.MenuId).HasColumnName("menu_id");
//            entity.Property(e => e.Name)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("name");

//            entity.HasOne(d => d.Menu).WithMany(p => p.Privileges)
//                .HasForeignKey(d => d.MenuId)
//                .HasConstraintName("FK__Privilege__menu___46E78A0C");
//        });

//        modelBuilder.Entity<State>(entity =>
//        {
//            entity.HasKey(e => e.StateId).HasName("PK__States__81A474177762845F");

//            entity.ToTable("States", "dbo");

//            entity.Property(e => e.StateId).HasColumnName("state_id");
//            entity.Property(e => e.Description)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("description");
//            entity.Property(e => e.Name)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("name");
//        });

//        modelBuilder.Entity<Store>(entity =>
//        {
//            entity.HasKey(e => e.StoreId).HasName("PK__Stores__A2F2A30C35A4BF12");

//            entity.ToTable("Stores", "dbo");

//            entity.Property(e => e.StoreId)
//                .ValueGeneratedNever()
//                .HasColumnName("store_id");
//            entity.Property(e => e.ContainerId).HasColumnName("container_id");
//            entity.Property(e => e.Name)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("name");
//            entity.Property(e => e.ServerName)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("server_name");

//            entity.HasOne(d => d.Container).WithMany(p => p.Stores)
//                .HasForeignKey(d => d.ContainerId)
//                .OnDelete(DeleteBehavior.SetNull)
//                .HasConstraintName("FK__Stores__containe__3B75D760");
//        });

//        modelBuilder.Entity<StoreUserGroup>(entity =>
//        {
//            entity.HasKey(e => e.StoreUserGroupId).HasName("PK__StoreUse__AE4607A31DB6B93E");

//            entity.ToTable("StoreUserGroups", "dbo");

//            entity.Property(e => e.StoreUserGroupId)
//                .ValueGeneratedNever()
//                .HasColumnName("store_user_group_id");
//            entity.Property(e => e.StoreId).HasColumnName("store_id");
//            entity.Property(e => e.UserGroupId).HasColumnName("user_group_id");

//            entity.HasOne(d => d.Store).WithMany(p => p.StoreUserGroups)
//                .HasForeignKey(d => d.StoreId)
//                .HasConstraintName("FK__StoreUser__store__59063A47");

//            entity.HasOne(d => d.UserGroup).WithMany(p => p.StoreUserGroups)
//                .HasForeignKey(d => d.UserGroupId)
//                .OnDelete(DeleteBehavior.ClientSetNull)
//                .HasConstraintName("FK__StoreUser__user___59FA5E80");
//        });

//        modelBuilder.Entity<UserGroup>(entity =>
//        {
//            entity.HasKey(e => e.UserGroupId).HasName("PK__UserGrou__DB165781E6B6F9B9");

//            entity.ToTable("UserGroups", "dbo");

//            entity.Property(e => e.UserGroupId)
//                .ValueGeneratedNever()
//                .HasColumnName("user_group_id");
//            entity.Property(e => e.Description)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("description");
//            entity.Property(e => e.Name)
//                .HasMaxLength(50)
//                .IsUnicode(false)
//                .HasColumnName("name");
//            entity.Property(e => e.ParentUserGroupId).HasColumnName("parent_user_group_id");

//            entity.HasOne(d => d.ParentUserGroup).WithMany(p => p.InverseParentUserGroup)
//                .HasForeignKey(d => d.ParentUserGroupId)
//                .HasConstraintName("FK__UserGroup__paren__4E88ABD4");
//        });

//        modelBuilder.Entity<UserLogin>(entity =>
//        {
//            entity.HasKey(e => e.Id).HasName("PK__UserLogi__3214EC0715CD2FA7");

//            entity.ToTable("UserLogin", "CORP\\moh03138");

//            entity.Property(e => e.IsActive).HasDefaultValue(true);
//            entity.Property(e => e.Password).HasMaxLength(255);
//            entity.Property(e => e.Username).HasMaxLength(50);
//        });

//        OnModelCreatingPartial(modelBuilder);
//    }

//    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
//}
