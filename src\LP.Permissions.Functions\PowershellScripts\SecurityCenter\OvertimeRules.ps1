# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

Function ConvertTo-SCOvertimeRulesViolation() {
    <#
    .Synopsis
        Used to create a Violation object in powershell session.
    .DESCRIPTION
        
        The parameter Id represent a read-only identifier of the violation. It is generated by the system based on a hash of the object.
        
        The parameter Days is a bit-field representing to which days to apply this violation. The values are:
        Never = 0,
        Monday = 0x01,
        Tuesday = 0x02,
        Wednesday = 0x04,
        Thursday = 0x08,
        Friday = 0x10,
        Saturday = 0x20,
        Sunday = 0x40,
		It support both the bitfield value or a comma separated string representing the value, example: "Monday, Tuesday, Friday"

        The parameter Hours is a string representing to which hours of the days to apply this violation. Example: from 11am to 2pm -> 11:00-14:00

        The parameter GracePeriod is the number of minutes of grace is applied to this violation

        The parameter TimeLimit is the number of minute that is allowed.

    .EXAMPLE
        #
        ConvertTo-SCOvertimeRulesViolation -Days "Monday, Friday" -GracePeriod 15 -Hours '11:00-14:00' -TimeLimit 60

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [int]$Id,
        [byte] $Days,
        [string]$GracePeriod,
        [string]$Hours,
        [string]$TimeLimit
    )
    Process {
        return [PSCustomObject]@{
            Id = $Id
            Days = $Days
            Hours = $Hours
            GracePeriod = $GracePeriod
            TimeLimit = $TimeLimit
            }
    }
}
# -----------------------------------------------------------------------------
Function Get-SCOvertimeRule {
    <#
    .Synopsis
        Method used to create a new overtime rule with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new overtime rule with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new overtime rule upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new overtime rule and get it.
        $myOvertimeRule = New-SCOvertimeRule -Name "MyOvertimeRule" | Get-SCOvertimeRule

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RuleId           
        }
    }
}
# -----------------------------------------------------------------------------
Function New-SCOvertimeRule {
    <#
    .Synopsis
        Method used to create a new overtime rule with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new overtime rule with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new overtime rule upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new overtime rule and get it.
        $myOvertimeRule = New-SCOvertimeRule -Name "MyOvertimeRule" | Get-SCOvertimeRule

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t OvertimeRules
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCOvertimeRule {
     <#
    .Synopsis
        Will remove the overtime rule represented by the provided RuleId parameter from Security Center
    .DESCRIPTION
        This method will permanently remove the specified overtime rule from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the overtime rule to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
      
        Remove-SCOvertimeRule -RuleId $overtimeruleid

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RuleId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $RuleId
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCOvertimeRule() {
    <#
    .Synopsis
        Used to update the properties of a overtime rule in Security Center
    .DESCRIPTION
        This method is used to update the properties of a overtime rule to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter OvertimeRule represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new overtime rule.
        $myOvertimeRule = New-SCOvertimeRule -Name "MyOvertimeRule" | Get-SCOvertimeRule

        # Update the entity.
        $myOvertimeRule.Color = "ffffffff"
        Set-SCOvertimeRule -OvertimeRule $myOvertimeRule 

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("p")] $OvertimeRule
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $OvertimeRule
        }
    }
}
# -----------------------------------------------------------------------------
Function Show-SCOvertimeRuleProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a overtime rule
    .DESCRIPTION
        This method will list the supported properties and relation of an overtime rule (the data model, not the actual data).  This method is used
        when you want to know what is available for a overtime rule
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCOvertimeRuleProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiOvertimeRule" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}
# -----------------------------------------------------------------------------
Function Get-SCOvertimeRuleViolations {
    <#
    .Synopsis
        This method will return all the properties of all the overtime rule violation represented by the RuleId
    .DESCRIPTION
        This method will return all the basic properties of the overtime rule violation contained in a OvertimeRule represented by the RuleId.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the overtime rule that contain the violation to retrieve (The guid representing the overtime rule in the Security Center System)
        You can also pass any overtime rule object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $violations = Get-SCOvertimeRuleViolations -RuleId $myovertimeRule.id       

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $RuleId
    
            InvokeSCRestMethod -Method 'GET' -UriSuffix "Entities/$id/Violations"      
        }
    }
}
# -----------------------------------------------------------------------------
Function Get-SCOvertimeRuleViolation {
    <#
    .Synopsis
        This method will return all the properties of the overtime rule violation represented by the ID in a specified overtime rule.
    .DESCRIPTION
        This method will return all the basic properties of the overtime rule violation represented by the ID contained inside a specified RuleId.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the overtime rule that contain the violation. (The guid representing the overtime rule in the Security Center System)
        You can also pass any overtime rule object that contains an ID as a parameter

        The ViolationId parameter represents the Id of the overtime rule violation to retrieve (The integer representing the overtime rule violation in the Security Center System)
        You can also pass any overtime rule violation object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $violations = Get-SCOvertimeRuleViolation -RuleId $myovertimeRule.id -ViolationId $violationId      

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RuleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("vid")] $ViolationId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $RuleId
            $ovid = GetIdFromObject $ViolationId
    
            InvokeSCRestMethod -Method 'GET' -UriSuffix "Entities/$id/Violations/$ovid"      
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCOvertimeRuleViolation() {
    <#
    .Synopsis
        Used to update the properties of a overtime rule violation in Security Center
    .DESCRIPTION
        This method is used to update the properties of a overtime rule violation to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the overtime rule that contain the violation. (The guid representing the overtime rule in the Security Center System)
        You can also pass any overtime rule object that contains an ID as a parameter

        The Violation parameter represents the overtime rule violation to update.

        The violations object is defined

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $violation = [PSCustomObject]@{
            Id= 12334556
            Days = "Monday, Friday"
            Hours = "00:00-00:00"
            }
        $violationId = Set-SCOvertimeRuleViolation -OvertimeRuleId $myOvertimeRule.Id -Violation $violation

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("oid")] $RuleId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("v")] $Violation
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $id = GetIdFromObject $RuleId
            $vid = GetIdFromObject $Violation
            $jsonBody = $Violation | ConvertTo-Json 
    
            InvokeSCRestMethod -Method 'PUT' -UriSuffix "Entities/$id/Violations/$vid" -Body $jsonBody
        }
    }
}
# -----------------------------------------------------------------------------
Function New-SCOvertimeRuleViolation() {
    <#
    .Synopsis
         Method used to create a new overtime rule violation with the provided object
    .DESCRIPTION
        This method is used to create the object in security center and to fill the properties.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the overtime rule that contain the violation. (The guid representing the overtime rule in the Security Center System)
        You can also pass any overtime rule object that contains an ID as a parameter

        The Violation parameter represents the overtime rule violation to create.
        
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new overtime rule violation
        $violation = [PSCustomObject]@{
            Days = "Monday, Friday"
            Hours = "00:00-00:00"
        }
        $violationId = New-SCOvertimeRuleViolation -OvertimeRuleId $myOvertimeRule.Id -Violation $violation

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("oid")] $RuleId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("v")] $Violation
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $id = GetIdFromObject $RuleId
            $jsonBody = $Violation | ConvertTo-Json 
    
            InvokeSCRestMethod -Method 'POST' -UriSuffix "Entities/$id/Violations" -Body $jsonBody
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCOvertimeRuleViolation {
    <#
    .Synopsis
        Will remove the overtime rule violation represented by the ViolationId in a specific Overtime Rule.
    .DESCRIPTION
        This method will permanently remove the specified overtime rule violation from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the overtime rule that contain the violation. (The guid representing the overtime rule in the Security Center System)
        You can also pass any overtime rule object that contains an ID as a parameter

        The ViolationId parameter represents the overtime rule violation to remove.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
      
        Remove-SCOvertimeRuleViolation -RuleId $overtimeruleid -ViolationId $myRestriction

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RuleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("ovid")] $ViolationId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $RuleId
            $ovid = GetIdFromObject $ViolationId
    
            InvokeSCRestMethod -Method 'DELETE' -UriSuffix "Entities/$id/Violations/$ovid"      
        }
    }
}
# -----------------------------------------------------------------------------
Export-ModuleMember -Function '*-*' -Alias '*'