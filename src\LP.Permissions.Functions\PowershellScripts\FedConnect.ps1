param (
    [string]$IPAddress="**************",
    [string]$Username="Samurai",
    [string]$Credential="Password1!",
    [string]$ServerPassword="",
    [string]$LogFilePath=""
)
#$parentDirectory = Split-Path -Parent $PSScriptRoot
#$logFilePath = Join-Path $parentDirectory "ps_logfile.log"
function Write-Message {
    param (
        [string]$message
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
   # Add-Content -Path $LogFilePath -Value "$timestamp - $message"
}
# Dynamically resolve the path to the Genetec module
#$modulePath = Join-Path $PSScriptRoot "SecurityCenter"

# Import the Genetec module
#if (Test-Path $modulePath) {
 #   Import-Module $modulePath -Force
  #  Write-Message "Genetec module imported successfully from path: $modulePath"
#} else {
 #   Write-Message "Genetec module not found at path: $modulePath"
  #  exit 1
#}



try {
    # Attempt to enter the SC session
    Write-Message "Attempting to enter SC session for $IPAddress..."
    Enter-SCSession -ComputerName $IPAddress -User $Username -DirectoryPassword $Credential -GenetecServerPassword $ServerPassword

    # Check if the session was established
    if ($?) {
        Write-Message "SC session established successfully for $IPAddress."

        $FederationRoles = Get-SCRoles -Type Federation | Get-SCRole
        if ($null -eq $FederationRoles) {
            $errorMessage = "Failed to retrieve roles. No roles found."
            Write-Message $errorMessage
            throw $errorMessage
        } else {
            foreach ($Role in $FederationRoles) {
                $StoreName = $Role.Name
                $StoreIP = $Role.IP
                Write-Message "Store Name = $StoreName, Store IP = $StoreIP"
                # Output the Store IP
                $StoreIP
            }
        }

        Exit-SCSession
        Write-Message "SC session exited successfully for $IPAddress."
    } else {
        $errorMessage = "Failed to establish SC session for $IPAddress."
        Write-Message $errorMessage
        throw $errorMessage
    }
} catch {
    $errorMessage = "An error occurred for $IPAddress. Exception: $_"
    Write-Message $errorMessage
    exit 1
}