﻿using Microsoft.JSInterop;

namespace LPPermission.UI.Services
{
    /// <summary>
    /// Service to manage authentication state and notify components of changes.
    /// </summary>
    public class AuthService
    {
        /// <summary>
        /// Indicates whether the user is authenticated.
        /// </summary>
        public bool IsAuthenticated { get; private set; }

        /// <summary>
        /// Event triggered when the authentication state changes.
        /// </summary>
        public event Action OnChange;

        /// <summary>
        /// Updates the authentication state and notifies subscribers of the change.
        /// </summary>
        /// <param name="isAuthenticated">The new authentication state.</param>
        public void SetIsAuthenticated(bool isAuthenticated)
        {
            IsAuthenticated = isAuthenticated;
            NotifyStateChanged();
        }

        /// <summary>
        /// Notifies all subscribers about a change in the authentication state.
        /// </summary>
        private void NotifyStateChanged() => OnChange?.Invoke();
    }
}