﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCPartitionMembers {
    <#
    .Synopsis
        Method used to add a member to the given partition
    .DESCRIPTION
        This Method will allow the user to add a security center entity to a specified partition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter PartitionId will be used to specify the partition we want to add the entity to

        The parameter MemberId represents the Id of the entity we want to add to the partition
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewPartition = New-SCPartition -Name "MyNewPartition" | Get-SCPartition
        $myNewUser = New-SCUser -Name "MyNewUser" | Get-SCUser
        
        Add-SCPartitionMembers -PartitionId $myNewPartition -MemberId $myNewUser

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("Id")] $PartitionId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("mid")] $MemberId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $mid = GetIdFromObject $MemberId
            Set-SCEntityRelation -EntityId $PartitionId -RelationName "members" -RelationId $mid   
        }
    }    
}

# -----------------------------------------------------------------------------
Function Add-SCPartitionParentPartitions {
    <#
    .Synopsis
        Method used to add a parent partition to the given partition
    .DESCRIPTION
        This Method will allow the user to add a security center partition as a parent to a specified partition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter PartitionId will be used to specify the partition we want to add the parent partition to

        The parameter PartitionIdToAdd represents the Id of the partition we want to add as the parent partition
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewPartitionParent = New-SCPartition -Name "MyNewPartitionParent" | Get-SCPartition

        $myNewPartitionChild = New-SCPartition -Name "MyNewPartitionChild" | Get-SCPartition
        
        Add-SCPartitionPartitions -PartitionId $myNewPartitionChild -PartitionIdToAdd $myNewPartitionParent

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("Id")] $PartitionId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("pid")] $PartitionIdToAdd
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $pid = GetIdFromObject $PartitionIdToAdd
            Set-SCEntityRelation -EntityId $PartitionId -RelationName "partitions" -RelationId $pid   
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsp Get-SCPartition
Function Get-SCPartition {
    <#
    .Synopsis
        This method will return all the properties of the partition represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the partition represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The PartitionId parameter represents the Id of the partition to retrieve (The guid representing the partition in the Security Center System)
        You can also pass any partition object that contains an ID as a parameter
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewPartition = New-SCPartition -Name "MyNewPartition"
        Get-SCPartition -PartitionId $myNewPartition
        
        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        New-SCPartition -Name "MyNewPartition" | Get-SCPartition
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        nsp "MyNewPartition" | gsp
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("Id")] $PartitionId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $PartitionId  
        }
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCPartitionMembers {
    <#
    .Synopsis
        Method used to get all members of the given partition
    .DESCRIPTION
        This Method will allow the user to get all members of the given partition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter PartitionId will be used to specify the partition we want to get the members from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewPartition = New-SCPartition -Name "MyNewPartition" | Get-SCPartition
        $myNewUser = New-SCUser -Name "MyNewUser" | Get-SCUser
        
        Add-SCPartitionMembers -PartitionId $myNewPartition -MemberId $myNewUser

        Get-SCPartitionMembers -PartitionId $myNewPartition

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("Id")] $PartitionId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $PartitionId -RelationName "members"
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCPartitionParentPartitions {
    <#
    .Synopsis
        Method used to get all parent partitions of the given partition
    .DESCRIPTION
        This Method will allow the user to get all parent partition of the given partition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter PartitionId will be used to specify the partition we want to get the parent partitions from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewPartitionParent = New-SCPartition -Name "MyNewPartitionParent" | Get-SCPartition

        $myNewPartitionChild = New-SCPartition -Name "MyNewPartitionChild" | Get-SCPartition
        
        Add-SCPartitionPartitions -PartitionId $myNewPartitionChild -PartitionIdToAdd $myNewPartitionParent

        Get-SCPartitionPartitions -PartitionId $myNewPartitionChild

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("Id")] $PartitionId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $PartitionId -RelationName "partitions"
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias nsp New-SCPartition
Function New-SCPartition {
    <#
    .Synopsis
        Method used to create a new partition with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new partition with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new partition upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        New-SCPartition -Name "MyNewPartition"
        
        Exit-SCSession  
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        nsp "MyNewPartition"
        
        Exit-SCSession  
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Partitions
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias rsp Remove-SCPartition
Function Remove-SCPartition {
    <#
    .Synopsis
        Will remove the partition represented by the provided PartitionId from Security Center
    .DESCRIPTION
        This method will permanently remove the specified partition from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The PartitionId parameter represents the partition to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myPartition = New-SCPartition -Name "MyNewPartition"

        Remove-SCPartition -PartitionId $myPartition.Id

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myPartition = New-SCPartition -Name "MyNewPartition"

        rsp $myPartition

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true)] [alias("Id")] $PartitionId
        )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $PartitionId
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCPartitionMembers {
    <#
    .Synopsis
        Method used to remove a member from the given partition
    .DESCRIPTION
        This Method will allow the user to remove a security center member from a specified partition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter PartitionId will be used to specify the partition we want to remove the member from

        The parameter MemberId represents the Id of the entity we want to remove from the partition
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewPartition = New-SCPartition -Name "MyNewPartition" | Get-SCPartition
        $myNewUser = New-SCUser -Name "MyNewUser" | Get-SCUser
        
        Add-SCPartitionMembers -PartitionId $myNewPartition -MemberId $myNewUser

        Remove-SCPartitionMembers -PartitionId $myNewPartition -MemberId $myNewUser

        Exit-SCSession  
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("Id")] $PartitionId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("mid")] $MemberId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $mid = GetIdFromObject $MemberId
            Remove-SCEntityRelation -EntityId $PartitionId -RelationName "members" -RelationId $mid   
        }
    }   
}

# -----------------------------------------------------------------------------
Function Remove-SCPartitionParentPartitions {
    <#
    .Synopsis
        Method used to remove a parent partition from the given partition
    .DESCRIPTION
        This Method will allow the user to remove a security center parent partition from a specified partition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter PartitionId will be used to specify the partition we want to remove the parent partition from

        The parameter PartitionIdToRemove represents the Id of the parent partition we want to remove from the partition
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewPartitionParent = New-SCPartition -Name "MyNewPartitionParent" | Get-SCPartition

        $myNewPartitionChild = New-SCPartition -Name "MyNewPartitionChild" | Get-SCPartition
        
        Add-SCPartitionPartitions -PartitionId $myNewPartitionChild -PartitionIdToAdd $myNewPartitionParent

        Remove-SCPartitionPartitions -PartitionId $myNewPartitionChild -PartitionIdToRemove $myNewPartitionParent

        Exit-SCSession  
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("Id")] $PartitionId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true)] [alias("pid")] $PartitionIdToRemove
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $pid = GetIdFromObject $PartitionIdToRemove
            Remove-SCEntityRelation -EntityId $PartitionId -RelationName "partitions" -RelationId $pid   
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias ssp Set-SCPartition
Function Set-SCPartition() {
    <#
    .Synopsis
        Used to update the properties of a partition in Security Center
    .DESCRIPTION
        This method is used to update the properties of a partition to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Partition represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newp = New-SCPartition -Name "Myp"  | gsp
        $newp.Description = "test"
        
        Set-SCPartition -Partition $newp

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newp = New-SCPartition -Name "Myp"  | gsp
        $newp.Description = "test"
        
        ssp $newp

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("p")] $Partition
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Partition
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCPartitionProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a partition
    .DESCRIPTION
        This method will list the supported properties and relation of a partition (the data model, not the actual data).  This method is used
        when you want to know what is available for a given partition
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCPartitionProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiPartition" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'
