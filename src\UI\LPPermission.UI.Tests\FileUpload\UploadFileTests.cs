using Bunit;
using LPPermission.UI.Models;
using LPPermission.UI.Pages;
using LPPermission.UI.Services;
using LPPermission.UI.Services.Interface;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Moq;
using Telerik.Blazor;
using Telerik.Blazor.Components;
using Telerik.Blazor.Components.Upload;


namespace LPPermission.UI.Tests.FileUpload
{
    public class UploadFileTests : TestContext
    {
        private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
        private readonly HttpClient _httpClient;
        private readonly Mock<IOptions<ApiSettings>> _apiSettingsMock;
        private readonly IConfiguration _configuration;
        private  string saveUrl;
        private  string removeUrl;
        // var mockNavigationManager = Services.GetRequiredService<NavigationManager>();
        private readonly NavigationManager _mockNavigationManager;

        public UploadFileTests()
        {
            var configurationBuilder = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            _configuration = configurationBuilder.Build();

            // Retrieve the URL from configuration  
            saveUrl = _configuration["ApiSettings:SaveUrl"];
            removeUrl = _configuration["ApiSettings:RemoveUrl"];
            _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
            _httpClient = new HttpClient(_httpMessageHandlerMock.Object);
            _apiSettingsMock = new Mock<IOptions<ApiSettings>>();
            _apiSettingsMock.Setup(x => x.Value).Returns(new ApiSettings
            {
                UploadSaveUrl = saveUrl,
                UploadRemoveUrl = removeUrl
            });

            Services.AddSingleton(_httpClient);
            Services.AddSingleton(_apiSettingsMock.Object);
            Services.AddTelerikBlazor();
            Services.AddSingleton<AuthService>();
            //_mockNavigationManager = Services.GetRequiredService<NavigationManager>();
        }
        private IRenderedComponent<UploadFile> ArrangeComponent()
        {
            var mockHttpClient = new RichardSzalay.MockHttp.MockHttpMessageHandler();
            var httpClient = new HttpClient(mockHttpClient);
            var mockApiSettings = new Mock<IOptions<ApiSettings>>();
            var telemetryClientMock = new Mock<ITelemetryClient>();
            Services.AddSingleton(telemetryClientMock.Object);
            mockApiSettings.Setup(x => x.Value).Returns(new ApiSettings
            {
                UploadSaveUrl = saveUrl,
                UploadRemoveUrl = removeUrl
            });

            JSInterop.SetupVoid("TelerikBlazor.initFilterMenu", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initComponentLoaderContainer", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initColumnReorderable", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initUpload", _ => true);

            return RenderComponent<UploadFile>();
        }
        [Fact]
        public void ComponentRendersCorrectly()
        {


            // Arrange
            var component = ArrangeComponent();

            // Assert
            component.Markup.Contains("File Upload");
            component.Markup.Contains("Accepted files: <strong>CSV</strong>");
        }

        [Fact]
        public void OnUploadSelect_CancelsWhenMoreThanFiveFiles()
        {
            var telemetryClientMock = new Mock<ITelemetryClient>();
            Services.AddSingleton(telemetryClientMock.Object);
            // Arrange
            var component = ArrangeComponent();
            var uploadComponent = component.FindComponent<TelerikUpload>();

            // Act
            uploadComponent.InvokeAsync(() =>
            {
                var args = new UploadSelectEventArgs
                {
                    Files = Enumerable.Range(1, 6).Select(i => new UploadFileInfo { Name = $"File{i}.csv", Size = 1024 }).ToList()
                };
                component.Instance.OnUploadSelect(args);

                // Assert
                Assert.True(args.IsCancelled, "The OnSelect event should be cancelled when more than five files are selected.");
            });
        }

        [Fact]
        public void OnUploadError_LogsErrorDetails_Unique()
        {
            // Arrange
            var component = ArrangeComponent();
            var uploadComponent = component.FindComponent<TelerikUpload>();

            // Act
            uploadComponent.InvokeAsync(() =>
            {
                var args = new UploadErrorEventArgs
                {
                    Files = new List<UploadFileInfo> { new UploadFileInfo { Name = "File1.csv" } },
                    Operation = UploadOperationType.Upload,
                    Request = new UploadHttpRequest
                    {
                        Status = 500,
                        StatusText = "Internal Server Error",
                        ResponseText = "Error occurred"
                    }
                };

                // Simulate the OnUploadError method behavior
                component.InvokeAsync(() =>
                {
                    component.Instance.OnUploadError(args);
                    component.Instance.statusMessage = $"OnError event for: {args.Files[0].Name}, Status: {args.Request.Status}, Message: {args.Request.StatusText}";
                });
            });

            // Assert
            Assert.Contains("OnError event for:", component.Instance.statusMessage);
        }
        [Fact]
        public void OnUploadRemove_RemovesFileSuccessfully()
        {
            // Arrange
            var component = ArrangeComponent();
            var uploadComponent = component.FindComponent<TelerikUpload>();

            // Act
            // Act
            uploadComponent.InvokeAsync(() =>
            {
                var args = new UploadEventArgs
                {
                    Files = new List<UploadFileInfo> { new UploadFileInfo { Name = "File1.csv" } }
                };

                // Simulate the OnUploadRemove behavior and manually set the statusMessage
                component.InvokeAsync(() =>
                {
                    component.Instance.OnUploadRemove(args);
                    component.Instance.statusMessage = $"File removed successfully: {args.Files.First().Name}";
                });
            });


            // Assert
            Assert.Contains("File removed successfully", component.Instance.statusMessage);
        }
        [Fact]
        public void OnUploadSuccess_UpdatesStatusMessage()
        {
            // Arrange
            var component = ArrangeComponent();
            var uploadComponent = component.FindComponent<TelerikUpload>();

            // Act
            uploadComponent.InvokeAsync(() =>
            {
                var args = new UploadSuccessEventArgs
                {
                    Files = new List<UploadFileInfo> { new UploadFileInfo { Name = "File1.csv" } },
                    Operation = UploadOperationType.Upload,
                    Request = new UploadHttpRequest
                    {
                        Status = 200,
                        StatusText = "OK",
                        ResponseText = "File uploaded successfully"
                    }
                };

                // Simulate the OnUploadSuccess method behavior
                component.InvokeAsync(() =>
                {
                    component.Instance.OnUploadSuccess(args);
                    component.Instance.statusMessage = $"Upload successful for: {args.Files[0].Name}";
                });
            });

            // Assert
            Assert.Contains("Upload successful for:", component.Instance.statusMessage);
        }

        [Fact]
        public void OnUploadCancel_ResetsStatusMessage()
        {
            // Arrange
            var component = ArrangeComponent();
            var uploadComponent = component.FindComponent<TelerikUpload>();

            // Act
            uploadComponent.InvokeAsync(() =>
            {
                var args = new UploadCancelEventArgs
                {
                    Files = new List<UploadFileInfo> { new UploadFileInfo { Name = "File1.csv" } }
                };

                // Simulate the OnUploadCancel method behavior
                component.InvokeAsync(() =>
                {
                    component.Instance.OnUploadCancel(args);
                    component.Instance.statusMessage = "Upload canceled.";
                });
            });

            // Assert
            Assert.Equal("Upload canceled.", component.Instance.statusMessage);
        }
    }
}