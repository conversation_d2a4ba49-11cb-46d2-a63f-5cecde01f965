﻿using LPPermission.DAL;
using LPPermission.DAL.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LPPermission.BLL
{
    public class GetPermission : IGetPermission
    {
        private readonly IGenericRepository<PermissionDto> _permissionRepository;
        public GetPermission(IGenericRepository<PermissionDto> permissionRepository)
        {
            _permissionRepository = permissionRepository;
        }
        public async Task<IEnumerable<PermissionDto>> GetAllPermissions()
        {
            try
            {
                // Get the IQueryable result from the repository
                var permissionsQuery = _permissionRepository.GetAllPermissions();

                // Execute the query and materialize the result
                var permissions = await permissionsQuery;  // Materializing the result asynchronously

                // Project the result to PermissionDto1
                var permissionDtos = permissions
                    .Select(p => new PermissionDto
                    {
                        ContainerName = p.ContainerName,
                        GeoName = p.GeoName,
                        MenuName = p.MenuName,
                        PrivilegeName = p.PrivilegeName,
                        StateName = p.StateName,
                        UserGroupName = p.UserGroupName,
                        StoreName = p.StoreName,
                        GenetecReference = p.GenetecReference
                    })
                    .ToList();
                return permissionDtos ?? Enumerable.Empty<PermissionDto>();

            }
            catch (Exception ex)
            {
                // Handle exceptions (e.g., log them)
                Console.WriteLine($"An error occurred: {ex.Message}");
                return Enumerable.Empty<PermissionDto>();
            }
            // Return the result (empty if null)
        }
    }
}
