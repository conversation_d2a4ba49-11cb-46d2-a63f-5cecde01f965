﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias dbsam Debug-SCAccessManagerRole
Function Debug-SCAccessManagerRole {
    <#
    .Synopsis
        This will launch the diagnose command for the selected access manager role
    .DESCRIPTION
        This method will launch the diagnose command for the given access manager role  This method is used
        when you want to troubleshoot a given access manager role.

        The AccessManagerId parameter represents the Id of the access manager role to diagnose (The guid representing the role in the Security Center System)
        You can also pass any access manager role object that contains an ID as a parameter
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will retreive the access manager role
        $entity = Get-SCRoles -t AccessManager
        
        Debug-SCAccessManagerRole -AccessManagerId $entity

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("Id")] $AccessManagerId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AccessManagerId -RelationName "diagnostics" 
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias gsame Get-SCAccessManagerExtensions
Function Get-SCAccessManagerExtensions {
    <#
    .Synopsis
        This method will return all the access control extensions that an access manager role has configured.
    .DESCRIPTION
        This method is used to return all the access control extensions that an access manager role has configured.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The AccessManagerId parameter represents the Id of the access manager role to retrieve the extensions from(The guid representing the role in the Security Center System).
        You can also pass any access manager role object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myAM = Get-SCRoles -Type AccessManager 
        Get-SCArchiverExtensions $myAM.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AccessManagerId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AccessManagerId -RelationName "Extensions" 
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias ssamp Show-SCAccessManagerProperties
Function Show-SCAccessManagerProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an access manager role.
    .DESCRIPTION
        This method will list the supported properties and relation of an access manager role (the data model, not the actual data).  This method is used
        when you want to know what is available for a given access manager role.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCAccessManagerProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiAccessManagerRole" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}

Export-ModuleMember -Function '*-*' -Alias '*'
