﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCCameraEncryptionCertificates {
    <#
    .Synopsis
        This method will add a certificate for video encryption for the given camera Id.
    .DESCRIPTION
        This method is called when a user wants to add a certificate for video encryption of a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System).
        You can also pass any camera object that contains an ID as a parameter.

        The Certificate parameter represents the full string of the certificate to add to the camera.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        $certificatesOnGenetecServer = Get-SCServerAvailableCertificates
        Add-SCCameraEncryptionCertificates -CameraId $cams -Certificate $certificatesOnGenetecServer[0].RawData

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("c")] $Certificate
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/EncryptionCertificates"
         
            $jsonObject = [ordered]@{}
            $jsonObject.Add("cert", $Certificate)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Add-SCCameraMotionDetectionConfig {
    <#
    .Synopsis
        This method will add a motion detection config for the given schedule on the given camera Id.
    .DESCRIPTION
        This method is called when a user wants to add a motion detection config for the given schedule on the given camera Id.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System).
        You can also pass any camera object that contains an ID as a parameter.

        The ScheduleId parameter represents the id of a SC schedule for when the motion detection will be applied.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        #must have a schedule named myschedule in SC
        $sched = Search-SCEntities -Name "mySchedule" -Type Schedules -Filter 'All'

        Add-SCCameraMotionDetectionConfig -CameraId $cams -ScheduleId $sched.id

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sId")] $ScheduleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/MotionDetections"
         
            $jsonObject = [ordered]@{}
            $jsonObject.Add("ScheduleId", $ScheduleId)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Add-SCCameraColorConfig {
    <#
    .Synopsis
        This method will add a color config for the given schedule on the given camera Id.
    .DESCRIPTION
        This method is called when a user wants to add a color config for the given schedule on the given camera Id.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to add the color config to (The guid representing the camera in the Security Center System).
        You can also pass any camera object that contains an ID as a parameter.

        The ScheduleId parameter represents the id of a SC schedule for when the color config will be applied.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        #must have a schedule named myschedule in SC
        $sched = Search-SCEntities -Name "mySchedule" -Type Schedules -Filter 'All'

        Add-SCCameraColorConfig -CameraId $cams -ScheduleId $sched.id

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sId")] $ScheduleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/VideoAttributes"
         
            $jsonObject = [ordered]@{}
            $jsonObject.Add("ScheduleId", $ScheduleId)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias gsc Get-SCCamera
Function Get-SCCamera {
    <#
    .Synopsis
        This method will return all the properties of the camera represented by the ID.
    .DESCRIPTION
        This method will return all the basic properties of the camera represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        $cam = Get-SCCamera -CameraId $cams

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cam = srse -n "My camera" -t Cameras | gsc

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId   
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCCameraArchivedStreams {
    <#
    .Synopsis
        This method will return the archived stream associated with the given camera.
    .DESCRIPTION
        This method is used to retrieve all archived streams associated with a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Get-SCCameraArchivedStreams -CameraId $cams

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId -RelationName archivedstreams
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCCameraAttachedCashRegisters {
    <#
    .Synopsis
        This method will return all the attached cash registers associated with the given camera.
    .DESCRIPTION
        This method is used to retrieve all the attached cash registered associated with a given camera.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Get-SCCameraAttachedCashRegisters -CameraId $cams

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId -RelationName attachedcashregisters
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCCameraEncryptionCertificates {
    <#
    .Synopsis
        Method used to get the encryption certificates of the given camera id.
    .DESCRIPTION
        Method used to get the available certificate used to encrypt the video of a given camera.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Get-SCServerAvailableCertificates

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
 
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId -RelationName EncryptionCertificates
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCCameraHardwareConfig {
    <#
    .Synopsis
        This method will return the hardware config data associated with the given camera.
    .DESCRIPTION
        This method is used to retrieve all hardware config data associated with a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Get-SCCameraHardwareConfig -CameraId $cams

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId -RelationName HardwareConfigData
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCCameraMetadataStreams {
    <#
    .Synopsis
        This method will return the meta data streams associated with the given camera.
    .DESCRIPTION
        This method is used to retrieve all meta data streams associated with a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Get-SCCameraMetadataStreams -CameraId $cams

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId -RelationName metadatastreams
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCCameraMotionDetectionConfigs {
    <#
    .Synopsis
        This method will return the motion detection configs associated with the given camera.
    .DESCRIPTION
        This method is used to retrieve all the motion detection configs associated with a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Get-SCCameraMotionDetectionConfigs -CameraId $cams

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId -RelationName MotionDetections
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCCameraColorConfigs {
    <#
    .Synopsis
        This method will return the color configs associated with the given camera.
    .DESCRIPTION
        This method is used to retrieve all the color configs associated with a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve the color config from (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Get-SCCameraColorConfigs -CameraId $cams

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId -RelationName VideoAttributes
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCCameraStreams {
    <#
    .Synopsis
        This method will return the video streams associated with the given camera.
    .DESCRIPTION
        This method is used to retrieve all video streams associated with a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Get-SCCameraStreams -CameraId $cams

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId -RelationName streams
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCCameraRecordingMode {
    <#
    .Synopsis
        This method will return the video recording mode associated with the given camera.
    .DESCRIPTION
        This method is used to retrieve the video recording mode associated with a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Get-SCCameraRecordingMode -CameraId $cams

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId -RelationName RecordingModes
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCCameraTransferGroups {
    <#
    .Synopsis
        This method will return the transfer groups associated with the given camera
    .DESCRIPTION
        This method is used to retrieve all the transfer groups associated with a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Get-SCCameraTransferGroups -CameraId $cams

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraId -RelationName transfergroups
        }
    } 
}

# -----------------------------------------------------------------------------
Function Invoke-SCCameraStartRecording {
    <#
    .Synopsis
        This method will trigger the manual recording for a given camera.
    .DESCRIPTION
        This method is called when a user wants to trigger the manual recording of a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Invoke-SCCameraStartRecording -CameraId $cams

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/StartRecording"
         
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", "")
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Invoke-SCCameraStopRecording {
    <#
    .Synopsis
        This method will stop the manual recording for a given camera.
    .DESCRIPTION
        This method is called when a user wants to stop the manual recording of a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Invoke-SCCameraStopRecording -CameraId $cams

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/StopRecording"       
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", "")
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias isab Invoke-SCCameraAddBookmark
Function Invoke-SCCameraAddBookmark {
    <#
    .Synopsis
        This method will add a bookmark for a given camera.
    .DESCRIPTION
        This method is called when a user wants to add a bookmark at a given time for a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.

        The Bookmark parameter is used to specify the string to enter as a bookmark.

        The BookmarkTime (optional, current time is the default value) is used to set the time at which the bookmark is inserted.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Invoke-SCCameraAddBookmark -CameraId $cams -Bookmark "My test bookmark" -BookmarkTime Get-Date

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true)] [alias("b")] [string]$Bookmark,
        [parameter(Mandatory=$false)] [alias("bt")] [DateTime]$BookmarkTime
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            if($BookmarkTime -eq $null){
                $BookmarkTime = Get-Date
            }

            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/AddBookmark"
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("description", $Bookmark)
            $jsonObject.Add("timestamp", $BookmarkTime.ToString("o"))
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias isem Invoke-SCCameraEnableMaintenance
Function Invoke-SCCameraEnableMaintenance {
    <#
    .Synopsis
        This method will enable maintenance mode for a given camera.
    .DESCRIPTION
        This method is called when a user wants to enable maintenance for a given camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.

        The EndTime (optional) parameter is used to specify the future end time of the maintenance. No value sets maintenance indefinitely

        The Reason (optional) parameter is used to set the reason why the camera is in maintenance.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Invoke-SCCameraAddBookmark -CameraId $cams -EndTime $DateTime  -Reason "Maintenance Reason"

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$false)] [alias("met")] [DateTime]$EndTime,
        [parameter(Mandatory=$false)] [alias("r")] [string]$Reason
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            if($EndTime -eq $null){
                $EndTime = [DateTime]::MaxValue.ToUniversalTime();
            }

            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/EnableMaintenance"
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("endtime", $EndTime.ToString("o"))
            $jsonObject.Add("reason", $Reason)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "PUT" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Invoke-SCCameraGoToPreset {
    <#
    .Synopsis
        This method will send a go to preset command to a camera supporting a ptz.
    .DESCRIPTION
        This method is called when a user wants to send a ptz camera to a specified preset.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.

        The Preset parameter is used to specify the preset to send to the ptz camera.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Invoke-SCCameraGoToPreset -CameraId $cams -Preset 1

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true)] [alias("p")] [int]$Preset
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/Presets/$Preset/Goto"
         
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", "")
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Invoke-SCCameraRunPattern {
    <#
    .Synopsis
        This method will send a run pattern command to a camera supporting a ptz.
    .DESCRIPTION
        This method is called when a user wants to run a pattern on a camera supporting a ptz.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.

        The Pattern parameter is used to specify which pattern to send to the ptz camera.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Invoke-SCCameraRunPattern -CameraId $cams -Pattern 1

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true)] [alias("p")] [int]$Pattern
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/Patterns/$Pattern/Run"
         
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", "")
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Invoke-SCCameraGoHome {
    <#
    .Synopsis
        This method will send a go home command to a camera supporting a ptz.
    .DESCRIPTION
        This method is called when a user wants send a ptz camera to the home position.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Invoke-SCCameraGoHome -CameraId $cams -Pattern 1

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$CameraId/GotoHome"
         
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", "")
            $jsonBody = $jsonObject | ConvertTo-Json   
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

#------------------------------------------------------------------------------
Function Invoke-SCCameraDonateArchives {
	<#
	.Synopsis
		This method will move the recorded video ownership from one camera to another.
	.DESCRIPTION
		This method is called when the user wants to move recorded video ownership to a different camera.  This will only work
        if the source camera if offline.

		Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.
		
		The CameraId parameter represents the video camera to move the archives from.

		The CameraDestinationId parameter represents the id of the camera to move the video archives to.
	.EXAMPLE
		# Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""
		
        $cams = Get-SCEntities -Type Cameras -Filter All
		Invoke-SCCameraDonateArchives -CameraId $cams[0].Id -CameraDestinationId $cams[1].Id

	    #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
	#>
	[CmdletBinding()]
	param(
		[parameter(Mandatory=$true,Position=0)] [alias("Id")] $CameraId,
		[parameter(Mandatory=$true,Position=1)] [alias("dest") ] $CameraDestinationId
	)
	Begin {
	}

	Process{
		SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CameraId
			$uri = "entities/$cid/DonateArchives"
            
            $dest = GetIdFromObject $CameraDestinationId

			$jsonObject = [ordered]@{}
			$jsonObject.Add("Id", $dest)
			$jsonBody = $jsonObject | ConvertTo-Json

			InvokeSCRestMethod -Method 'POST' -UriSuffix $uri -Body $jsonBody	
		}
	}
}

# -----------------------------------------------------------------------------
Function Remove-SCCameraMotionDetectionConfig {
    <#
    .Synopsis
        This method will remove a motion detection config for the given schedule on the given camera Id.
    .DESCRIPTION
        This method is called when a user wants to remove a motion detection config for the given schedule on the given camera Id.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to remove the config from (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.

        The ScheduleId parameter represents the id of the SC schedule to delete the motion detection config from.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'
        #must have a schedule named myschedule in SC
        $sched = Search-SCEntities -Name "mySchedule" -Type Schedules -Filter 'All'

        #must ahave a motion detection config based on this schedule
        Remove-SCCameraMotionDetectionConfig -CameraId $cams -ScheduleId $sched.id

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sId")] $ScheduleId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntityRelation -EntityId $CameraId -RelationName "MotionDetections" -RelationId $ScheduleId  
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCCameraColorConfig {
    <#
    .Synopsis
        This method will remove a color config for the given schedule on the given camera Id.
    .DESCRIPTION
        This method is called when a user wants to remove a color config for the given schedule on the given camera Id.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraId parameter represents the Id of the camera to remove the config from (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter.

        The ScheduleId parameter represents the id of the SC schedule to delete the motion detection config from.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'
        #must have a schedule named myschedule in SC
        $sched = Search-SCEntities -Name "mySchedule" -Type Schedules -Filter 'All'

        #must ahave a motion detection config based on this schedule
        Remove-SCCameraColorConfig -CameraId $cams -ScheduleId $sched.id

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sId")] $ScheduleId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntityRelation -EntityId $CameraId -RelationName "VideoAttributes" -RelationId $ScheduleId  
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCCameraEncryptionCertificates {
    <#
    .Synopsis
        Method used to remove an encryption certificate from the given camera.
    .DESCRIPTION
        This Method will allow the user to remove an encryption certificate from the camera encryption certificate list
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter CameraId will be used to specify the camera we want to remove the certificate from.

        The parameter CertificatListPositionId represents the Id of the certificate, in the camera certificate list, we want to remove from to the camera.
 
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        $certificatesOnGenetecServer = Get-SCServerAvailableCertificates
        Add-SCCameraEncryptionCertificates -CameraId $cams -Certificate $certificatesOnGenetecServer[0].RawData

        Remove-SCCameraEncryptionCertificates -CameraId $cams -CertificatListPositionId 0

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true)] [alias("clpid")] $CertificatListPositionId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntityRelation -EntityId $CameraId -RelationName "EncryptionCertificates" -RelationId $CertificatListPositionId  
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssc Set-SCCamera
Function Set-SCCamera() {
    <#
    .Synopsis
        Used to update the properties of an camera in Security Center.
    .DESCRIPTION
        This method is used to update the properties of a camera to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build.
        the parameter caches for auto-complete

        The parameter camera represents and contains the properties that will be updated to security Center.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'
                
        $cams.Description = "My new Alarm"

        #update config in SecurityCenter
        Set-SCCamera $cams

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("c")] $Camera
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Camera
        }
    }
}

# -----------------------------------------------------------------------------
Function Set-SCCameraRecordingModes {
    <#
    .Synopsis
        This method will set the camera recording modes that the camera has configured for the given Id.
    .DESCRIPTION
        This method is used to set the camera recording modes.  This will only work if the UseCustomRecordingSettings property of the camera is set to True.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The CamId parameter represents the Id of the camera to set the recording modes to
        You can also pass any camera object that contains an ID as a parameter.

        The RecModeId parameter is the 0 base ID of the configured recording mode. This Id can be retreived by calling  Get-SCCameraRecordingMode
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All' 
        Set-SCCameraRecordingModes $cams.Id -RecModeId "0" -RecordingMode OnMotionOrManual

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CamId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("rmId")] $RecModeId
    )

    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'RecordingMode'
        $ParameterAlias = 'rm'
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $false
        $ParameterAttribute.Position = 2

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        # Generate and set the ValidateSet 
            
        $arrSet = 'Continuous', 'Manual', 'Off', 'OnMotionOrManual'
        $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

        # Add the ValidateSet to the attributes collection
        $AttributeCollection.Add($ValidateSetAttribute)

         #add the alias to the attributes collection
        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)
        return $RuntimeParameterDictionary
    }
 
    Begin {
    }

    Process {
        $RecordingMode = $PsBoundParameters[$ParameterName]

        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CamId
            $uri = "Entities/$cid/RecordingModes/$RecModeId"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Mode", $RecordingMode)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Put" -Body $jsonBody 
        }
    }   
}

# -----------------------------------------------------------------------------
Function Set-SCCameraStreamUsage {
    <#
    .Synopsis
        This method will set the camera stream usage to on for the given stream Id.
    .DESCRIPTION
        This method is used to set the camera stream usage.  This will only work for video streams of the camera.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The CamId parameter represents the Id of the camera to set the stream usage
        You can also pass any camera object that contains an ID as a parameter

        The StreamId parameter is id of the video stream you want to turn on the stream usage.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All' 

        #the stream Id can be found by using the method Get-SCCameraStreams
        Set-SCCameraStreamUsage $cams.Id -StreamId 00000001-0000-0202-0000-9de9c9b43dd0 -StreamUsage Live

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CamId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sId")] $StreamId
    )

    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'StreamUsage'
        $ParameterAlias = "su"    
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]

        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $true
        $ParameterAttribute.Position = 0

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        $sess = GetSession -Quiet $true
        if ($sess -ne $null)
        {
            $arrSet = $sess.SCStreamUsage
            $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection.Add($ValidateSetAttribute)
        }

        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $StreamUsage = $PsBoundParameters[$ParameterName]
    }

    Process {
        $RecordingMode = $PsBoundParameters[$ParameterName]

        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CamId
            $uri = "Entities/$cid/Streams/$StreamId"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("StreamUsage", $StreamUsage)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Put" -Body $jsonBody 
        }
    }   
}

# -----------------------------------------------------------------------------
Function Set-SCCameraMotionDetectionConfigs {
    <#
    .Synopsis
        This method will set the camera motion detection config for a given schedule Id.
    .DESCRIPTION
        This method will set the camera motion detection config for a given schedule Id.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The CamId parameter represents the Id of the camera to set the stream usage
        You can also pass any camera object that contains an ID as a parameter.

        The ScheduleId parameter is id of the schedule when the config is applied.

        The Enabled parameter is to turn on or off the motion detection

        The HardwareDetection parameter is to configure the motion detection in software or hardware mode.

        The Sensitivity parameter is to configurer the sensitivity paramter of the motion detection config
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        
        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'
        #must have a schedule named myschedule in SC
        $sched = Search-SCEntities -Name "mySchedule" -Type Schedules -Filter 'All'

        #must ahave a motion detection config based on this schedule

        Set-SCCameraMotionDetectionConfigs -CameraId $cams -ScheduleId $sched.id -Enabled $true -HardwareDetection $false -Sensitivity 95

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sId")] $ScheduleId,
        [parameter(Mandatory=$true,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("e")] [bool]$Enabled,
        [parameter(Mandatory=$true,Position=3,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("hd")] [bool]$HardwareDetection,
        [parameter(Mandatory=$true,Position=4,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("s")] [int]$Sensitivity
    )

    begin {
    }

    Process {

        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/MotionDetections/$ScheduleId"

            $jsonObject = [ordered]@{} 

            $jsonObject.Add("Enabled", $Enabled)
            $jsonObject.Add("HardwareDetection", $HardwareDetection)
            $jsonObject.Add("Sensitivity", $Sensitivity)
                
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Put" -Body $jsonBody 
        }
    }   
}

# -----------------------------------------------------------------------------
Function Set-SCCameraColorConfig {
    <#
    .Synopsis
        This method will set the camera color config of the camera for the given schedule id.
    .DESCRIPTION
        This method will set the camera color config of the camera for the given schedule id.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete .

        The CameraId parameter represents the Id of the camera to set the stream usage
        You can also pass any camera object that contains an ID as a parameter.

        The ScheduleId parameter is id of the schedule when the config is applied.

        The Brightness parameter is used to configure the brightness in the color config of the given schedule .

        The Contrast parameter is used to configure the contrast in the color config of the given schedule.

        The Hue parameter is used to configure the hue in the color config of the given schedule. 

        The Saturation parameter is used to configure the saturation in the color config of the given schedule. 

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        #must have a schedule named myschedule in SC
        $sched = Search-SCEntities -Name "mySchedule" -Type Schedules -Filter 'All'

        Set-SCCameraColorConfig -CameraId $cams -ScheduleId $sched.id -Brightness 50 -Contrast 50 -Hue 50 -Saturation 50

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sId")] $ScheduleId,
        [parameter(Mandatory=$true,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("b")] [int]$Brightness,
        [parameter(Mandatory=$true,Position=3,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("c")] [int]$Contrast,
        [parameter(Mandatory=$true,Position=4,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("h")] [int]$Hue,
        [parameter(Mandatory=$true,Position=5,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("s")] [int]$Saturation
    )

    begin {
    }

    Process {

        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$cid/VideoAttributes/$ScheduleId"

            $jsonObject = [ordered]@{} 

            $jsonObject.Add("Brightness", $Brightness)
            $jsonObject.Add("Contrast", $Contrast)
            $jsonObject.Add("Hue", $Hue)
            $jsonObject.Add("Saturation", $Saturation)
                
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Put" -Body $jsonBody 
        }
    }   
}

# -----------------------------------------------------------------------------
Function Show-SCCameraBookmarkReport {
    <#
    .Synopsis
        Used to retrieve a camera bookmark report.
    .DESCRIPTION
        This method will return a camera bookmark report for all the bookmarks between the specified start time en end time.  The report will contain a Results property
        that contains 2 arrays.  The first array will be the column definition.  This will contain the information this will be in each rows.
        The second array is the actual data of the report matching the column definition.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter

        The second parameter is StartTime which contains the start time of the report query

        The third parameter is EndTime which contains the end time of the report query
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'
        $date = Get-Date

        Invoke-SCCameraAddBookmark -CameraId $cams -Bookmark "My test bookmark" -BookmarkTime $date.AddMinutes(-10)

        Show-SCCameraBookmarkReport -CameraId $cams -StartTime $date.AddMinutes(-20) -EndTime $date

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$false,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId,
        [parameter(Mandatory=$false,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("st")] [DateTime]$StartTime,
        [parameter(Mandatory=$false,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("et")] [DateTime]$EndTime
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CameraId 
            $uri = "Reports/Bookmarks?Cameras="  + $cid +"&TimeStart=" + $StartTime.ToString("o") + "&TimeEnd=" + $EndTime.ToString("o")
            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        }
    }  
}

# -----------------------------------------------------------------------------
Function Show-SCCameraEventReport {
    <#
    .Synopsis
        Used to retrieve a camera event report.
    .DESCRIPTION
        This method will return an camera event report for all the events for the specified camera.  The report will contain a Results property
        that contains 2 arrays.  The first array will be the column definition.  This will contain the information this will be in each rows.
        The second array is the actual data of the report matching the column definition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Show-SCCameraEventReport -CameraId $cams

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CameraId   
            $uri = "Reports/CameraEvents?Cameras="  + $cid
            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        }
    }  
}

# -----------------------------------------------------------------------------
Function Show-SCCameraProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a camera.
    .DESCRIPTION
        This method will list the supported properties and relation of a camera (the data model, not the actual data).  This method is used
        when you want to know what is available for a given camera.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCCameraProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    Begin {
    }

    Process {
        $uri = "Help/Entities/ApiCamera" 

        SCCmdletImplementation $MyInvocation.InvocationName {
            $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

            if($result.Fields -and $result.Relations)
            {
                $result.Fields
                $result.Relations
            }
            else
            {
                $result
            }
        }
    }   
}

# -----------------------------------------------------------------------------
Function Show-SCCameraVideoSequenceReport {
    <#
    .Synopsis
        Used to retrieve a camera video sequence report.
    .DESCRIPTION
        This method will return a camera video sequence report for all the video sequences of the specified camera.  The report will contain a Results property
        that contains 2 arrays.  The first array will be the column definition.  This will contain the information this will be in each rows.
        The second array is the actual data of the report matching the column definition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CameraId parameter represents the Id of the camera to retrieve (The guid representing the camera in the Security Center System)
        You can also pass any camera object that contains an ID as a parameter
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        Show-SCCameraVideoSequenceReport -CameraId $cams

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {  
            $cid = GetIdFromObject $CameraId
            $uri = "Reports/VideoSequences?Cameras="  + $cid
            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        }
    }  
}

Export-ModuleMember -Function '*-*' -Alias '*'