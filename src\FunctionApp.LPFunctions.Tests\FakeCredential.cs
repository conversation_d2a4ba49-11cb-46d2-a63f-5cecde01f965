using Azure.Core;
using Azure.Security.KeyVault.Secrets;
using Moq;

public class FakeCredential : TokenCredential
{
    // At the top of your test class, add:
    private static readonly TokenCredential _fakeCredential = new FakeCredential();

    // Remove the following line, as 'var' cannot be used here and this is not valid as a field or property:
    // var secretClientMock = new Mock<SecretClient>(new Uri("https://fake.vault/"), _fakeCredential);

    public override AccessToken GetToken(TokenRequestContext requestContext, CancellationToken cancellationToken)
    {
        // Return a dummy AccessToken for testing purposes
        return new AccessToken("fake-token", DateTimeOffset.MaxValue);
    }

    public override ValueTask<AccessToken> GetTokenAsync(TokenRequestContext requestContext, CancellationToken cancellationToken)
    {
        // Return a dummy AccessToken asynchronously for testing purposes
        return new ValueTask<AccessToken>(new AccessToken("fake-token", DateTimeOffset.MaxValue));
    }
}

