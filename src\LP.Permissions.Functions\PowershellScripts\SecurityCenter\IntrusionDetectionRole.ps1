# ==========================================================================
# Copyright (C) 1989-2020 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias gside Get-SCIntrusionDetectionExtensions
Function Get-SCIntrusionDetectionExtensions {
    <#
    .Synopsis
        This method will return all the Intrusion detection extensions that an Intrusion detection role has configured.
    .DESCRIPTION
        This method is used to return all the Intrusion detection extensions that an Intrusion detection role has configured.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The IntrusionManagerId parameter represents the Id of the Intrusion detection role to retrieve the extensions from(The guid representing the role in the Security Center System).
        You can also pass any Intrusion detection role object that contains an ID as a parameter.
    .EXIRPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myIR = Get-SCRoles -Type IntrusionDetection 
        Get-SCIntrusionExtensions $myIR.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory = $true, Position = 0, ValueFromPipelineByPropertyName = $true, ValueFromPipeline = $true)] [alias("Id")] $IntrusionManagerId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $IntrusionManagerId -RelationName "ExtensionSettings" 
        }
    }   
}


# -----------------------------------------------------------------------------
Set-Alias gsidr Get-SCIntrusionDetectionRole
Function Get-SCIntrusionDetectionRole {
    <#
    .Synopsis
        This method will return all the Intrusion detection  that an Intrusion detection role has configured.
    .DESCRIPTION
        This method is used to return all the Intrusion detection  that an Intrusion detection role has configured.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The IntrusionManagerId parameter represents the Id of the Intrusion detection role to retrieve
        You can also pass any Intrusion detection role object that contains an ID as a parameter.
    .EXIRPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myIR = Get-SCRoles -Type IntrusionDetection 
        Get-SCIntrusionDetectionRole $myIR.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory = $true, Position = 0, ValueFromPipelineByPropertyName = $true, ValueFromPipeline = $true)] [alias("Id")] $IntrusionManagerId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $IntrusionManagerId
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias ssidp Show-SCIntrusionDetectionProperties
Function Show-SCIntrusionDetectionProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an intrusion detection role.
    .DESCRIPTION
        This method will list the supported properties and relation of an intrusion detection role (the data model, not the actual data).  This method is used
        when you want to know what is available for a given intrusion detection role.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCIntrusionDetectionProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiIntrusionDetectionRole" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if ($result.Fields -and $result.Relations) {
            $result.Fields
            $result.Relations
        }
        else {
            $result
        }
    }    
}

# -----------------------------------------------------------------------------
Set-Alias gsiup Get-SCIntrusionUnits
Function Get-SCIntrusionUnits {
    <#
    .Synopsis
        This method will return all the Intrusion units that are child to the given Intrusion Manager
    .DESCRIPTION
        This method will return all the Intrusion units that are child to the given Intrusion Manager represented by the ID.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Import-Module .\SecurityCenter.psm1 -Force

        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""
        $myIR = Get-SCRoles -Type IntrusionDetection 
        $IntrusionUnits = Get-SCIntrusionUnits $myIR.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory = $true, Position = 0, ValueFromPipelineByPropertyName = $true, ValueFromPipeline = $true)] [alias("Id")] $UnitId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $ChildEntities = Get-SCEntity -EntityId $UnitId -RelationName OwnershipEntities
            $IntrusionUnitsArray = $ChildEntities | Where-Object { $_.EntityType -eq "IntrusionUnits" }
            # $IntrusionUnitsArray
            foreach ($i in $IntrusionUnitsArray) {
                $result = Get-SCIntrusionUnit $i.Id
                $result
                "------------------------------------------------------"
            }
        }
    }    
}
# -----------------------------------------------------------------------------
Set-Alias asidr Add-SCIntrusionDetectionRole
Function Add-SCIntrusionDetectionRole {
    <#
    .Synopsis
        Method used to add an intrusion detection role in Security Center
    .DESCRIPTION
        This method is used to add an intrusion detection role in Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
		# Call the following in order to import all the modules. Otherwise, Enter-SCSession will not work.
        # Import-Module ".\SDK\REST\Powershell Scripts\Modules\SecurityCenter\SecurityCenter.psm1" -Force
        # Import-Module .\SecurityCenter.psm1 -Force
        
		# Must enter a valid Security Center session before calling any method.
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""


        Exit-SCSession

    .NOTES        
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory = $true, Position = 1)] [alias("nme")] $name,
        [parameter(Mandatory = $false, Position = 2)] [alias("sId")] $serverId
    )
 
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            if(!$PSBoundParameters.ContainsKey('serverId'))
            {
                $servers = Get-SCEntities -t Servers
                if($servers -is [array])
                {
                    $serverId = $servers[0].Id
                }
                else 
                {
                    $serverId=$servers.Id
                }
            }

            New-SCRole -Name $name -Type IntrusionDetection -ServerId $serverId 
        }
    }
}