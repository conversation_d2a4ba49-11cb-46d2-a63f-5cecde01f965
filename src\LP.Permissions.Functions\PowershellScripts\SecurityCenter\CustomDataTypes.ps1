# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================


# -----------------------------------------------------------------------------
Set-Alias nscd New-SCCustomDataType
Function New-SCCustomDataType {
    <#
    .Synopsis
        Method used to create a new custom data type
    .DESCRIPTION
        This Method will allow the user to create a security center custom data type
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        #Create a new custom data type
        $cdt = New-SCCustomDataType -n "MyCustomDataType" -t "Text" -de "This data type represents some text" -v @("1", "2", "blue", "red")
        
        #If the operation succedded the $cdt variable will contain the Name(string) of the newly created custom field
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    
    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)]  [alias("n")]  [string]$Name,
        [parameter(Mandatory=$true)]  [alias("t")]  $DataType,
        [parameter(Mandatory=$true)]  [alias("v")]  [array]$Values,
        [parameter(Mandatory=$false)] [alias("de")] [string]$Description
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $EntitySourceId = GetSystemConfigurationId
        $id = GetIdFromObject $EntitySourceId
        $uriSuffix = "Entities/$id/customdatatypes/"

        $jsonObject = [ordered]@{}
        $jsonObject.Add("Name", $Name)
        $jsonObject.Add("DataType", $DataType)
        $jsonObject.Add("Values", $Values)
        if($Description){$jsonObject.Add("Description", $Description)}
        $jsonBody = $jsonObject | ConvertTo-Json

        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias rscd Remove-SCCustomDataType
Function Remove-SCCustomDataType {
    <#
    .Synopsis
        Method used to remove a custom data type
    .DESCRIPTION
        This Method will allow the user to remove a security center custom data type
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom data type
        $cdt = New-SCCustomDataType -n "MyCustomDataType" -t "Text" -de "This data type represents some text" -v @("1", "2", "blue", "red")

        Remove-SCCustomDataType -id $cdt.Name
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)] [alias("id")] $CustomDataTypeId
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $EntitySourceId = GetSystemConfigurationId
        $id = GetIdFromObject $EntitySourceId
        $uriSuffix = "Entities/$id/customdatatypes/$CustomDataTypeId"
        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'DELETE'
    }
}

# -----------------------------------------------------------------------------
Set-Alias sscd Set-SCCustomDataType
Function Set-SCCustomDataType {
    <#
    .Synopsis
        Method used to update a custom data type
    .DESCRIPTION
        This Method will allow the user to update a security center custom data type
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom data type
        $cdt = New-SCCustomDataType -n "MyCustomDataType" -t "Text" -de "This data type represents some text" -v @("1", "2", "blue", "red")

        #Update the created custom data type
        Set-SCCustomDataType -id $cdt.Name -n "AvailableCookieCount" -de "This data type represents available number of cookies" -v @("0", "1", "2")
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)]  [alias("id")] $CustomDataTypeId,
        [parameter(Mandatory=$false)] [alias("n")]  [string]$Name,
        [parameter(Mandatory=$false)] [alias("v")]  [array]$Values,
        [parameter(Mandatory=$false)] [alias("de")] [string]$Description
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $EntitySourceId = GetSystemConfigurationId
        $id = GetIdFromObject $EntitySourceId
        $uriSuffix = "Entities/$id/customdatatypes/$CustomDataTypeId"

        $jsonObject = [ordered]@{}
        if($Name)        {$jsonObject.Add("Name", $Name)}
        if($Values)      {$jsonObject.Add("Values", $Values)}
        if($Description) {$jsonObject.Add("Description", $Description)}
        $jsonBody = $jsonObject | ConvertTo-Json

        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'PUT' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias gscds Get-SCCustomDataTypes
Function Get-SCCustomDataTypes {
    <#
    .Synopsis
        Method used to get all the custom data types
    .DESCRIPTION
        This Method will allow the user to get all of the security center custom data types
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        Get-SCCustomDataTypes
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    SCCmdletImplementation $MyInvocation.InvocationName {
        $EntitySourceId = GetSystemConfigurationId
        $id = GetIdFromObject $EntitySourceId
        $uriSuffix = "Entities/$id/customdatatypes"
        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'GET'
    }
}

# -----------------------------------------------------------------------------
Set-Alias gscd Get-SCCustomDataType
Function Get-SCCustomDataType {
    <#
    .Synopsis
        Method used to get a custom data types
    .DESCRIPTION
        This Method will allow the user to get a security center custom data type
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete        
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom data type
        $cdt = New-SCCustomDataType -n "MyCustomDataType" -t "Text" -de "This data type represents some text" -v @("1", "2", "blue", "red")

        Get-SCCustomDataType -id $cdt.Name
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)] [alias("id")] $CustomDataTypeId
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $EntitySourceId = GetSystemConfigurationId
        $id = GetIdFromObject $EntitySourceId
        $uriSuffix = "Entities/$id/customdatatypes/$CustomDataTypeId"
        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'GET'
    }
}