﻿using LPPermission.DAL;
using LPPermission.DAL.Models;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Data;

public class GenericRepository<T> : IGenericRepository<T> where T : class
{
    private readonly string _connectionString;

    // Constructor that accepts a connection string
    public GenericRepository(string connectionString)
    {
        _connectionString = connectionString;
    }
    public async Task<List<PermissionDto>> GetAllPermissions()
    {
        var permissions = new List<PermissionDto>();

        try
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                using (var command = new SqlCommand("GetAllPermissions", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    await connection.OpenAsync();

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var permission = new PermissionDto
                            {
                                UserGroupName = reader["UserGroupName"] as string ?? string.Empty,
                                MenuName = reader["MenuName"] as string ?? string.Empty,
                                PrivilegeName = reader["PrivilegeName"] as string ?? string.Empty,
                                StateName = reader["StateName"] as string ?? string.Empty,
                                GenetecReference = reader["GenetecReference"] as string ?? string.Empty
                            };
                            permissions.Add(permission);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // Handle exceptions (e.g., log them)
            Console.WriteLine($"An error occurred while fetching permissions: {ex.Message}");
            return new List<PermissionDto>(); // Return an empty list on error
        }

        return permissions;
    }
}