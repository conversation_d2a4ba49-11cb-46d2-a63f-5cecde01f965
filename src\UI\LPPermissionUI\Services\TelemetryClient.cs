﻿using LPPermission.UI.Services.Interface;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;

namespace LPPermission.UI.Services
{
    /// <summary>
    /// Wrapper class for the Application Insights TelemetryClient to track events and exceptions.
    /// </summary>
    public class TelemetryClientWrapper : ITelemetryClient
    {
        // Instance of the Application Insights TelemetryClient
        private readonly TelemetryClient _telemetryClient;

        /// <summary>
        /// Constructor to initialize the TelemetryClientWrapper with a TelemetryClient instance.
        /// </summary>
        /// <param name="telemetryClient">The TelemetryClient instance for tracking telemetry data.</param>
        public TelemetryClientWrapper(TelemetryClient telemetryClient)
        {
            _telemetryClient = telemetryClient;
        }

        /// <summary>
        /// Tracks a custom event using Application Insights.
        /// </summary>
        /// <param name="telemetryE">The event telemetry data to track.</param>
        public void TrackEvent(EventTelemetry telemetryE)
        {
            _telemetryClient.TrackEvent(telemetryE);
        }

        /// <summary>
        /// Tracks an exception using Application Insights.
        /// </summary>
        /// <param name="telemetryEx">The exception telemetry data to track.</param>
        public void TrackException(ExceptionTelemetry telemetryEx)
        {
            _telemetryClient.TrackException(telemetryEx);
        }
    }
}