﻿using LPPermission.UI.Services.Interface;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace LPPermission.UI.Services
{
    /// <summary>
    /// Service for handling user login and authentication-related operations.
    /// </summary>
    public class LoginService : ILoginService
    {
        // HttpClient for making HTTP requests
        private readonly HttpClient _httpClient;

        // Base URI for the API, configured in appsettings.json
        private readonly string _baseUri;

        // Service for securely storing JWT tokens
        private readonly JwtTokenStorageService _JwtTokenStorageService;

        /// <summary>
        /// Constructor to initialize the LoginService with dependencies.
        /// </summary>
        /// <param name="httpClient">HttpClient instance for making HTTP requests.</param>
        /// <param name="configuration">Configuration object for retrieving API base URI.</param>
        /// <param name="jwtTokenStorageService">Service for managing JWT tokens.</param>
        public LoginService(HttpClient httpClient, IConfiguration configuration, JwtTokenStorageService jwtTokenStorageService)
        {
            _httpClient = httpClient;
            _baseUri = configuration["ApiBaseUri"];
            _JwtTokenStorageService = jwtTokenStorageService;
        }

        /// <summary>
        /// Validates the user's credentials by calling the login API.
        /// </summary>
        /// <param name="username">The username of the user.</param>
        /// <param name="password">The password of the user.</param>
        /// <returns>True if the credentials are valid, otherwise false.</returns>
        public async Task<bool> ValidateUserAsync(string username, string password)
        {
            // Call the login API with the provided username and password
            var response = await _httpClient.PostAsJsonAsync("api/auth/login", new { Username = username, Password = password });

            // Check if the response was successful and return true, else false
            if (response.IsSuccessStatusCode)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// Validates the user's credentials and retrieves a token from the API.
        /// </summary>
        /// <typeparam name="T">The type of the content to send in the request body.</typeparam>
        /// <param name="requestUri">The relative URI of the API endpoint.</param>
        /// <param name="content">The content to send in the request body.</param>
        /// <returns>The token as a string if the validation is successful, otherwise null.</returns>
        public async Task<string> ValidateUserAsync<T>(string requestUri, T content)
        {
            // Call the external API to validate the user's credentials
            var response = await _httpClient.PostAsJsonAsync($"{_baseUri}/{requestUri}", content);

            try
            {
                if (response.IsSuccessStatusCode)
                {
                    // Retrieve the response body as a string
                    var responseBody = await response.Content.ReadAsStringAsync();

                    // If the response body is not empty, store the token
                    if (!string.IsNullOrEmpty(responseBody))
                    {
                        await _JwtTokenStorageService.SetTokenAsync(responseBody);
                        return responseBody;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log or handle the exception as needed
                return null;
            }

            return null;
        }

        /// <summary>
        /// Model to deserialize the token from the API response.
        /// </summary>
        public class TokenResponse
        {
            /// <summary>
            /// The JWT token returned by the API.
            /// </summary>
            public string Token { get; set; }
        }
    }
}