﻿using System;
using System.Collections.Generic;

namespace LPPermission.DAL.Models;

public partial class Store
{
    public int StoreId { get; set; }

    public int? ContainerId { get; set; }

    public string? ServerName { get; set; }

    public string? Name { get; set; }

    public virtual Container? Container { get; set; }

    public virtual ICollection<StoreUserGroup> StoreUserGroups { get; set; } = new List<StoreUserGroup>();
}
