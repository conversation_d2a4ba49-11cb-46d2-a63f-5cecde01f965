﻿using LPPermission.DAL.Interfaces;
using LPPermission.DAL.Models;
using LPPermission.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Linq.Expressions;

namespace LPPermission.DAL.Repositories
{
        /// <summary>
    /// A generic repository implementation for performing CRUD operations on entities.
    /// </summary>
    /// <typeparam name="T">The type of the entity.</typeparam>
    public class GenericRepository<T> : IGenericRepository<T> where T : class
    {
        private readonly LppermissionsContext _context;
        private readonly DbSet<T> _dbSet;

        public GenericRepository()
        {

        }
        /// <summary>
        /// Constructor to initialize the repository with a database context.
        /// </summary>
        /// <param name="context">The database context.</param>
        public GenericRepository(LppermissionsContext context)
        {
            _context = context;
            _dbSet = context.Set<T>();
        }
        /// <summary>
        /// Retrieves menus based on a specified condition.
        /// </summary>
        /// <param name="predicate">The condition to filter menus.</param>
        /// <returns>An IQueryable of menus.</returns>
        public IQueryable<Menu> GetByConditionAsyncMenu(Expression<Func<Menu, bool>> predicate)
        {
            return _context.Menus.Where(predicate);
        }
        /// <summary>
        /// Adds a new entity to the database.
        /// </summary>
        /// <param name="entity">The entity to add.</param>
        public async Task AddAsync(T entity)
        {
            await _dbSet.AddAsync(entity);
            await SaveAsync();
        }
        /// <summary>
        /// Saves changes to the database.
        /// </summary>
        public async Task SaveAsync()
        {
            await _context.SaveChangesAsync();
        }

                /// <summary>
        /// Retrieves privileges based on a specified condition.
        /// </summary>
        /// <param name="predicate">The condition to filter privileges.</param>
        /// <returns>An IQueryable of privileges.</returns>
        public IQueryable<Privilege> GetByConditionAsyncPrivelege(Expression<Func<Privilege, bool>> predicate)
        {
            return _context.Privileges.Where(predicate);
        }

                /// <summary>
        /// Retrieves permissions based on a specified condition.
        /// </summary>
        /// <param name="predicate">The condition to filter permissions.</param>
        /// <returns>An IQueryable of permissions.</returns>
        public IQueryable<Permission> GetByConditionAsyncPermission(Expression<Func<Permission, bool>> predicate)
        {
            return _context.Permissions.Where(predicate);
        }

        /// <summary>
        /// Retrieves user groups based on a specified condition.
        /// </summary>
        /// <param name="predicate">The condition to filter user groups.</param>
        /// <returns>An IQueryable of user groups.</returns>
        public IQueryable<UserGroup> GetByConditionAsyncUserGroup(Expression<Func<UserGroup, bool>> predicate)
        {
            return _context.UserGroups.Where(predicate);
        }
        /// <summary>
        /// Retrieves all states directly from the database.
        /// </summary>
        /// <returns>A list of states.</returns>
        public async Task<IEnumerable<State>> GetAllStatesDirectAsync()
        {
            // Fetch all states directly from the context
            return await _context.States.ToListAsync();
        }
        /// <summary>
        /// Retrieves a single entity based on a specified condition.
        /// </summary>
        /// <param name="predicate">The condition to filter the entity.</param>
        /// <returns>The first entity that matches the condition, or null if none found.</returns>
        public async Task<T> GetByConditionAsync(Expression<Func<T, bool>> predicate)
        {

            return await _dbSet.FirstOrDefaultAsync(predicate);
        }
        /// <summary>
        /// Validates a user's login credentials.
        /// </summary>
        /// <param name="username">The username to validate.</param>
        /// <param name="password">The password to validate.</param>
        /// <returns>True if the credentials are valid; otherwise, false.</returns>
        public async Task<bool> ValidateUserLoginAsync(string username, string password)
        {
            var userLogin = await _context.UserLogins
                .FirstOrDefaultAsync(u => u.Username == username && u.Password == password && u.IsActive);

            return userLogin != null;
        }
        
                /// <summary>
        /// Deletes all permissions from the database.
        /// </summary>
        public async Task DeletePermissions()
        {
            _context.Permissions.RemoveRange(_context.Permissions);
            await _context.SaveChangesAsync();
        }


        /// <summary>
        /// Retrieves all permissions and maps them to DTOs.
        /// </summary>
        /// <returns>A list of permission DTOs.</returns>
        public async Task<List<PermissionDto>> GetAllPermissions()
        {
            var query = from p in _context.Permissions
                        join pr in _context.Privileges on p.PrivilegeId equals pr.PrivilegeId
                        join m in _context.Menus on pr.MenuId equals m.MenuId
                        join u in _context.UserGroups on p.UserGroupId equals u.UserGroupId
                        join st in _context.States on p.StateId equals st.StateId
                        join c in _context.Containers on p.ContainerId equals c.ContainerId into containerJoin
                        from c in containerJoin.DefaultIfEmpty()
                        join g in _context.Geos on p.GeoId equals g.GeoId into geoJoin
                        from g in geoJoin.DefaultIfEmpty()
                        orderby g.Name, c.Name, u.Name, m.Name
                        select new PermissionDto
                        {
                            GeoName = g != null ? g.Name : "",
                            ContainerName = c != null ? c.Name : "",
                            StoreName = "", // Update if you have store info
                            UserGroupName = u.Name,
                            MenuName = m.Name,
                            PrivilegeName = pr.Name,
                            StateName = st.Name
                        };


            return await query.ToListAsync();
        }

                /// <summary>
        /// Retrieves all geo locations.
        /// </summary>
        /// <returns>An IQueryable of geo locations.</returns>
        public IQueryable<Geo> GetAllGeos()
        {
            return _context.Geos;
        }
        /// <summary>
        /// Retrieves containers by GeoId.
        /// </summary>
        /// <param name="geoId">The GeoId to filter containers.</param>
        /// <returns>An IQueryable of containers.</returns>
        public IQueryable<Container> GetContainersByGeoId(int geoId)
        {
            return _context.Containers.Where(c => c.GeoId == geoId);
        }

                /// <summary>
        /// Retrieves all geo locations and maps them to DTOs.
        /// </summary>
        /// <returns>A list of geo DTOs.</returns>
        public async Task<List<GeoDto>> GetAllGeosAsync()
        {
            var geos = await _context.Geos
                .Select(g => new GeoDto
                {
                    GeoId = g.GeoId,
                    Name = g.Name,
                    Description = g.Description
                })
                .ToListAsync();
            return geos;


        }

                /// <summary>
        /// Retrieves containers by GeoId and maps them to DTOs.
        /// </summary>
        /// <param name="geoId">The GeoId to filter containers.</param>
        /// <returns>A list of container DTOs.</returns>
        public async Task<List<ContainerDto>> GetContainersByGeoIdAsync(int geoId)
        {
            var containers = await _context.Containers
                .Where(c => c.GeoId == geoId)
                .Select(c => new ContainerDto
                {
                    ContainerId = c.ContainerId,
                    ContainerName = c.Name,
                    Description = c.Description
                })
                .ToListAsync();
            return containers;
        }
    }
}
