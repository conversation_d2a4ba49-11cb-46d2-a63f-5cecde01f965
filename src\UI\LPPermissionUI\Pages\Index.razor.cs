﻿using LPPermission.UI.Models;
using LPPermission.UI.Services;
using LPPermission.UI.Services.Interface;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Options;
using TelerikUI_App.Models;

namespace LPPermission.UI.Pages
{
    public partial class Index
    {
        // Property to toggle password visibility
        public bool HidePassword { get; set; } = true;

        // Property to toggle menu visibility
        public bool ShowMenu { get; set; } = false;

        // Represents the login details entered by the user
        public Login person { get; set; }

        // Flags to show success messages or error messages
        private bool ShowSuccessMessage { get; set; }
        private string ErrorMessage { get; set; }

        // Injected KeyVault secrets for secure configuration
        [Inject]
        private Dictionary<string, string> KeyVaultSecrets { get; set; }

        // URL for the LPPermission function
        private string LPPermissionFunctionUrl;

        // Flag to check if the component is initialized
        private bool _isInitialized;

        // Injected telemetry client for logging exceptions and events
        [Inject]
        private ITelemetryClient TelemetryClient { get; set; }

        // Injected API settings from configuration
        [Inject]
        private IOptions<ApiSettings> ApiSettings { get; set; }

        /// <summary>
        /// Lifecycle method called when the component is initialized.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                // Initialize default login details
                GetDefaultPerson();
                await base.OnInitializedAsync();
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during OnInitializedAsync");
            }
        }

        /// <summary>
        /// Handles form submission for login.
        /// </summary>
        public async Task HandleValidSubmit()
        {
            try
            {
                Logger.LogInformation("Form submitted successfully with UserName: {UserName}", person.UserName);

                // Create a login request object
                var loginRequest = new
                {
                    username = person.UserName,
                    password = person.Password
                };

                // Use the LoginUrl from appsettings.json
                var loginUrl = ApiSettings.Value.LoginUrl;

                // Send login request to the server
                var response = await Http.PostAsJsonAsync(loginUrl, loginRequest);

                if (response.IsSuccessStatusCode)
                {
                    // Handle successful login
                    var token = await response.Content.ReadAsStringAsync();

                    ShowSuccessMessage = true;
                    authser.SetIsAuthenticated(true);
                    await Task.Delay(2000);
                    ShowSuccessMessage = false;

                    // Redirect to the upload file page
                    Navigation.NavigateTo("/UploadFile");
                    Logger.LogInformation("Redirecting to upload file page");
                }
                else
                {
                    // Handle login failure
                    Logger.LogWarning("Login failed");
                    ErrorMessage = "Invalid credentials, or account is inactive.";
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during HandleValidSubmit");
                ErrorMessage = "An unexpected error occurred. Please try again later.";
                StateHasChanged();
            }
        }

        /// <summary>
        /// Helper method to log exceptions using Application Insights.
        /// </summary>
        /// <param name="ex">The exception to log.</param>
        /// <param name="message">A custom message to include with the exception.</param>
        private void LogException(Exception ex, string message)
        {
            var exceptionTelemetry = new ExceptionTelemetry(ex)
            {
                SeverityLevel = SeverityLevel.Error
            };
            exceptionTelemetry.Properties.Add("CustomMessage", message);
            TelemetryClient.TrackException(exceptionTelemetry);

            Logger.LogError(ex, message);
        }

        /// <summary>
        /// Initializes the default login details.
        /// </summary>
        public void GetDefaultPerson()
        {
            person = new Login()
            {
                UserName = "",
                Password = ""
            };
            ErrorMessage = string.Empty;
        }
    }
}