# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias nscf New-SCCustomField
Function New-SCCustomField {
    <#
    .Synopsis
        Method used to create a new custom field
    .DESCRIPTION
        This Method will allow the user to create a security center custom field
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom field
        $cf = New-SCCustomField -t Alarms -n "CustomAlarm" -dt "Text"
        
        #If the operation succedded the $cf variable will contain the guid of the newly created custom field
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    
    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)]  [alias("t")]   $EntityType,
        [parameter(Mandatory=$true)]  [alias("n")]   [string]$Name,
        [parameter(Mandatory=$true)]  [alias("dt")]  $ValueType,
        [parameter(Mandatory=$false)] [alias("df")]  $DefaultValue,
        [parameter(Mandatory=$false)] [alias("gn")]  [string]$GroupName,
        [parameter(Mandatory=$false)] [alias("p")]   [int]$GroupPriority,
        [parameter(Mandatory=$false)] [alias("u")]   [switch]$Unique,
        [parameter(Mandatory=$false)] [alias("m")]   [switch]$Mandatory,
        [parameter(Mandatory=$false)] [alias("o")]   $Owner
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $EntitySourceId = GetSystemConfigurationId
        $id = GetIdFromObject $EntitySourceId
        $uriSuffix = "Entities/$id/customfields/"

        $jsonObject = [ordered]@{}
        $jsonObject.Add("EntityType", $EntityType)
        $jsonObject.Add("Name", $Name)
        $jsonObject.Add("ValueType", $ValueType)
        if($DefaultValue){$jsonObject.Add("DefaultValue", $DefaultValue)}
        if($GroupName)   {$jsonObject.Add("GroupName", $GroupName)}
        if($GroupPriority)    {$jsonObject.Add("GroupPriority", $GroupPriority)}
        $jsonObject.Add("Unique", $Unique.IsPresent)
        $jsonObject.Add("Mandatory", $Mandatory.IsPresent)
        $jsonBody = $jsonObject | ConvertTo-Json

        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias rscf Remove-SCCustomField
Function Remove-SCCustomField {
    <#
    .Synopsis
        Method used to remove a custom field
    .DESCRIPTION
        This Method will allow the user to remove a security center custom field
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom field
        $cf = New-SCCustomField -t Alarms -n "CustomAlarm" -dt "Text"
        
        #We remove the newly created custom field
        Remove-SCCustomField -id $cf.Id
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)] [alias("id")]  [string]$CustomFieldId
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $EntitySourceId = GetSystemConfigurationId
        $id = GetIdFromObject $EntitySourceId
        $uriSuffix = "Entities/$id/customfields/$CustomFieldId"
        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'DELETE'
    }
}

# -----------------------------------------------------------------------------
Set-Alias sscf Set-SCCustomField
Function Set-SCCustomField {
    <#
    .Synopsis
        Method used to update a custom field
    .DESCRIPTION
        This Method will allow the user to update a security center custom field
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom field
        $cf = New-SCCustomField -t Alarms -n "CustomAlarm" -dt "Text"

        #Here we update the custom field use the Set-SCCustomField method
        #Note that -Unique and Mandatory are switches, the absence of thoses parameters indicate a false value.

        #Change the name for "AChangeOfName", the priority for 3 and set the custom field as unique.
        #Setting a custom field unique also for it to be mandatory
        Set-SCCustomField -id $cf.Id -n "AChangeOfName" -p 3 -Unique

        #Set the custom field to mandatory only (removing the unique)
        Set-SCCustomField -id $cf.Id -Mandatory
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)]  [alias("id")]  [string]$CustomFieldId,
        [parameter(Mandatory=$false)] [alias("n")]   [string]$Name,
        [parameter(Mandatory=$false)] [alias("df")]  $DefaultValue,
        [parameter(Mandatory=$false)] [alias("gn")]  [string]$GroupName,
        [parameter(Mandatory=$false)] [alias("p")]   [int]$GroupPriority,
        [parameter(Mandatory=$false)] [alias("u")]   [switch]$Unique,
        [parameter(Mandatory=$false)] [alias("m")]   [switch]$Mandatory,
        [parameter(Mandatory=$false)] [alias("o")]   $Owner
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $EntitySourceId = GetSystemConfigurationId
        $id = GetIdFromObject $EntitySourceId
        $uriSuffix = "Entities/$id/customfields/$CustomFieldId"

        $jsonObject = [ordered]@{}
        if($Name)        {$jsonObject.Add("Name", $Name)}
        if($DefaultValue){$jsonObject.Add("DefaultValue", $DefaultValue)}
        if($GroupName)   {$jsonObject.Add("GroupName", $GroupName)}
        if($GroupPriority)    {$jsonObject.Add("GroupPriority", $GroupPriority)}
        $jsonObject.Add("Unique", $Unique.IsPresent)
        $jsonObject.Add("Mandatory", $Mandatory.IsPresent)
        $jsonBody = $jsonObject | ConvertTo-Json

        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'PUT' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias gscfs Get-SCCustomFields
Function Get-SCCustomFields {
    <#
    .Synopsis
        Method used to get all of the custom fields
    .DESCRIPTION
        This Method will allow the user to get all the security center custom fields
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom field
        $cf = New-SCCustomField -t Alarms -n "CustomAlarm" -dt "Text"

        #Get all the custom fields
        Get-SCCustomFields
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    SCCmdletImplementation $MyInvocation.InvocationName {
        $EntitySourceId = GetSystemConfigurationId
        $id = GetIdFromObject $EntitySourceId
        $uriSuffix = "Entities/$id/customfields"
        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'GET'
    }
}

# -----------------------------------------------------------------------------
Set-Alias gscf Get-SCCustomField
Function Get-SCCustomField {
    <#
    .Synopsis
        Method used to get all of the custom fields
    .DESCRIPTION
        This Method will allow the user to get all the security center custom fields
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom field
        $cf = New-SCCustomField -t Alarms -n "CustomAlarm" -dt "Text"

        #Get the custom field
        Get-SCCustomField -id $cf.Id
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)] [alias("id")]  [string]$CustomFieldId
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $EntitySourceId = GetSystemConfigurationId
        $id = GetIdFromObject $EntitySourceId
        $uriSuffix = "Entities/$id/customfields/$CustomFieldId"
        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'GET'
    }
}

# -----------------------------------------------------------------------------
Set-Alias gcfvs Get-SCCustomFieldValues
Function Get-SCCustomFieldValues {
    <#
    .Synopsis
        Method used to get the values of all the custom fields of an entity
    .DESCRIPTION
        This Method will allow the user to get the values of a security center's entity custom fields
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom field
        $cf = New-SCCustomField -t Alarms -n "CustomAlarm" -dt "Text"

        #Get the custom field values
        Get-SCCustomFieldValues -eid <some-alarm-entity-guid>
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)] [alias("eid")] [string]$EntityId
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $uriSuffix = "Entities/$EntityId/CustomFieldValues/"
        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'GET'
    }
}

# -----------------------------------------------------------------------------
Set-Alias gcfv Get-SCCustomFieldValue
Function Get-SCCustomFieldValue {
    <#
    .Synopsis
        Method used to get the value of a specific custom field of an entity
    .DESCRIPTION
        This Method will allow the user to get the value of a security center's entity custom field
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom field
        $cf = New-SCCustomField -t Alarms -n "CustomAlarm" -dt "Text"

        #Get the custom field value
        Get-SCCustomFieldValue -eid <some-alarm-entity-guid> -id $cf.Id
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)] [alias("eid")] [string]$EntityId,
        [parameter(Mandatory=$true)] [alias("id")] [string]$CustomFieldId
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $uriSuffix = "Entities/$EntityId/CustomFieldValues/$CustomFieldId"
        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'GET'
    }
}

# -----------------------------------------------------------------------------
Set-Alias scfv Set-SCCustomFieldValue
Function Set-SCCustomFieldValue {
    <#
    .Synopsis
        Method used to set the value of a custom field
    .DESCRIPTION
        This Method will allow the user to set a custom field value to a security center entity
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        #Create a new custom field
        $cf = New-SCCustomField -t Alarms -n "CustomAlarm" -dt "Text"

        #Set the custom field
        Set-SCCustomFieldValue -eid <some-alarm-entity-guid> -id $cf.Id -v "This is the value the some-alarm-entity-guid will have for this custom field"
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param(
        [parameter(Mandatory=$true)] [alias("eid")] [string]$EntityId,
        [parameter(Mandatory=$true)] [alias("id")] [string]$CustomFieldId,
        [parameter(Mandatory=$true)] [alias("v")] [string]$CustomFieldValue
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $uriSuffix = "Entities/$EntityId/CustomFieldValues/$CustomFieldId"

        $jsonObject = [ordered]@{}
        $jsonObject.Add("Value", $CustomFieldValue)
        $jsonBody = $jsonObject | ConvertTo-Json

        InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'PUT' -Body $jsonBody
    }
}