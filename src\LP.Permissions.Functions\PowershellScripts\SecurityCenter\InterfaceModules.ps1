﻿# ==========================================================================
# Copyright (C) 1989-2020 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias gscimx Get-SCInterfaceModuleXml
Function Get-SCInterfaceModuleXml {
    <#
    .Synopsis
        This method will return the interface module xml for the specified interface module.
    .DESCRIPTION
        This method is used to get an interface module xml for the specified interface module.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The InterfaceModuleType parameter represents the type of interface module xml to retrieve.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        Get-SCInterfaceModuleXml -InterfaceModuleType = "EP2500"

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory = $true, Position = 0, ValueFromPipelineByPropertyName = $true, ValueFromPipeline = $true)] [alias("Type")] $InterfaceModuleType
    )
 
    Begin {
    }

    Process {
        $uri = "Entities/InterfaceModules/InterfaceModuleSpecific"
        $jsonObject = [ordered]@{} 
            $jsonObject.Add("InterfaceModuleType",$InterfaceModuleType )
            $jsonBody = $jsonObject | ConvertTo-Json
        SCCmdletImplementation $MyInvocation.InvocationName {
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias sscim Set-SCInterfaceModule
Function Set-SCInterfaceModule {
    <#
    .Synopsis
        This method updates the interface module.
    .DESCRIPTION
        This method is used to update an interface module.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The InterfaceModule parameter represents the interface module to update
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        Get-SCInterfaceModule -InterfaceId $interfaceModuleId
        $interfaceModule.PhysicalName = "Updated EP1501 Name"
        Set-SCInterfaceModule -InterfaceModule $InterfaceModuleXml

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory = $true, Position = 0)] [alias("module")] $InterfaceModule
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Set-SCEntity -EntityToSet $interfaceModule
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias sscimp Show-SCInterfaceModuleProperties
Function Show-SCInterfaceModuleProperties {
    <#
    .Synopsis
        This will show all properties and possible relations of an interface module
    .DESCRIPTION
        This method will list the supported properties and relations of an interface module.  This method is used
        when you want to know what is available for a given interface module.
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCInterfaceModuleProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiInterfaceModule" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}

# -----------------------------------------------------------------------------
Set-Alias gscim Get-SCInterfaceModule
Function Get-SCInterfaceModule {
    <#
    .Synopsis
        This method will return all the properties of the interface module represented by the ID.
    .DESCRIPTION
        This method will return all the properties of the interface module represented by the ID.  For object properties that represent a relation
        with another entity, use the specific methods.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The InterfaceModuleId parameter represents the Id of the interface module to retrieve (The guid representing the interface module in the Security Center System)
        You can also pass any interface module object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        Get-SCInterfaceModule -InterfaceId $interfaceId

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $InterfaceModuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $InterfaceModuleId 
        }
    }       
}

# -----------------------------------------------------------------------------
Set-Alias gscimd Get-SCInterfaceModuleDevices
Function Get-SCInterfaceModuleDevices {
    <#
    .Synopsis
        Method used to get all the devices of the given interface module.
    .DESCRIPTION
        This Method will allow the user to get all the devices of the given interface module.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter InterfaceModuleId will be used to specify the interface module we want to get the devices from.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        Get-SCInterfaceModuleDevices -InterfaceModuleId $interfaceModuleId 

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $InterfaceModuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $InterfaceModuleId -RelationName Devices
        }
    }    
}

Export-ModuleMember -Function '*-*' -Alias '*'