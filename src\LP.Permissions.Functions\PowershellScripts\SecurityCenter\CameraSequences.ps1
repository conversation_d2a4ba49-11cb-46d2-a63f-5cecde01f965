﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCCameraSequenceCamera {
    <#
    .Synopsis
        Method used to add a camera to the given camera sequence.
    .DESCRIPTION
        This Method will allow the user to add a security center camera  to a security center camera sequence.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter CameraSequenceId will be used to specify the camera sequence we want to add a camera to

        The parameter CameraId represents the Id of the camera we want to add to the camera list of the camera sequence

        The parameter DwellTimeInSec is the dwell time that will be used in the camera rotation of the sequence.  This parameter is optional with a default of 3

        The parameter PTZComand identifies which ptz command to trigger when the camera is displayed

        The parameter PatternPresetNb is the the number used to identify the pattern or the preset to trigger when the camera is displayed

        The parameter PTZAuxiliary identifies which auxiliary command to trigger when the camera is displayed

        The parameter AuxiliaryNb is the the number used to identify the auxiliary to trigger when the camera is displayed
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type camera sequence with the name MyNewCamSequence
        $camSeq = New-SCCameraSequence -n "MyNewCamSequence"
        
        #Will create a new entity of type user with the name MyNewUser
        $camera = Get-SCEntities -t cameras

        Add-SCCameraSequenceCamera -CameraSequenceId $camSeq -CameraId $camera[0]
       
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $CameraSequenceId,
        [parameter(Mandatory=$true)] [alias("cid")] $CameraId,
        [parameter(Mandatory=$false)] [alias("d")] $DwellTimeInSec=3,
        [parameter(Mandatory=$false)] [alias("ppnb")] $PatternPresetNb=0,
        [parameter(Mandatory=$false)] [alias("anb")] $AuxiliaryNb=0
    )
    DynamicParam {
        $sess = GetSession -Quiet $true

        # Set the dynamic parameters' name
        $ParameterName1 = 'PTZComand'
        $ParameterAlias1 = "ptzc"
        $ParameterName2 = 'PTZAuxiliary'
        $ParameterAlias2 = "ptza"
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary
        
        # Create the collection of attributes
        $AttributeCollection1 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
        $AttributeCollection2 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute1 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute1.Mandatory = $false
        $ParameterAttribute1.Position = 3

        $ParameterAttribute2 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute2.Mandatory = $false
        $ParameterAttribute2.Position = 4

        # Add the attributes to the attributes collection
        $AttributeCollection1.Add($ParameterAttribute1)
        $AttributeCollection2.Add($ParameterAttribute2)

        # Generate and set the ValidateSet 
        if ($sess -ne $null) { 
            $arrSet1 = $sess.SCPatternPresetCommand
            $ValidateSetAttribute1 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet1)

            $arrSet2 = $sess.SCAuxiliaryCommand
            $ValidateSetAttribute2 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet2)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection1.Add($ValidateSetAttribute1)
            $AttributeCollection2.Add($ValidateSetAttribute2)
        }
        # don't validate if we can't retrieve
        
        #add the alias to the attributes collection
        $ParamAlias1 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias1
        $AttributeCollection1.Add($ParamAlias1)
        $ParamAlias2 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias2
        $AttributeCollection2.Add($ParamAlias2)

        # Create and return the dynamic parameter
        $RuntimeParameter1 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName1, [string], $AttributeCollection1)
        $RuntimeParameterDictionary.Add($ParameterName1, $RuntimeParameter1)

        $RuntimeParameter2 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName2, [string], $AttributeCollection2)
        $RuntimeParameterDictionary.Add($ParameterName2, $RuntimeParameter2)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $PTZComand = $PsBoundParameters[$ParameterName1]
        $PTZAuxiliary = $PsBoundParameters[$ParameterName2]
    }
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $csid = GetIdFromObject $CameraSequenceId
            $cid = GetIdFromObject $CameraId

            $uri = "Entities/$csid/Members/"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("CameraId", $cid)
            $jsonObject.Add("Dwell", $DwellTimeInSec)

            if($PTZComand)
            {
                $jsonObject.Add("Command", $PTZComand)
                $jsonObject.Add("CommandNumber", $PatternPresetNb)
            }

            if($PTZAuxiliary)
            {
                $jsonObject.Add("Auxiliary", $PTZAuxiliary)
                $jsonObject.Add("AuxiliaryNumber", $AuxiliaryNb)
            }

            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias gscs Get-SCCameraSequence
Function Get-SCCameraSequence {
    <#
    .Synopsis
        This method will return all the properties of the camera sequence represented by the ID.
    .DESCRIPTION
        This method will return all the basic properties of the camera sequence represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraSequenceId parameter represents the Id of the camera sequence to retrieve (The guid representing the entity in the Security Center System)
        You can also pass any camera sequence object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        #Will create a new entity of type camera sequence with the name MyNewCamSequence
        $camSeq = New-SCCameraSequence -n "MyNewCamSequence"

        Get-SCCameraSequence -CameraSequenceId $camSeq.Id

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        New-SCEntity -n "MyNewCamSequence" -t Sequences | Get-SCCameraSequence

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        nscs "MyNewCamSequence" | gscs

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraSequenceId
    )
    begin{
    }
    process{
        SCCmdletImplementation $MyInvocation.InvocationName {
            $csid = GetIdFromObject $CameraSequenceId
            Get-SCEntity -EntityId $csid
        }
    }
        
}

# -----------------------------------------------------------------------------
Function Get-SCCameraSequenceCameras {
    <#
    .Synopsis
        Method used to retrieve the camera list of the given camera sequence.
    .DESCRIPTION
        This Method will allow the user to retrieve the camera list of a security center camera sequence.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter CameraSequenceId will be used to specify the camera sequence we want to retrieve the camera list from

    .EXAMPLE
    #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type camera sequence with the name MyNewCamSequence
        $camSeq = New-SCCameraSequence -n "MyNewCamSequence"
        
        #Will create a new entity of type user with the name MyNewUser
        $camera = Get-SCEntities -t cameras

        Add-SCCameraSequenceCamera -CameraSequenceId $camSeq -CameraId $camera[0]

        Get-SCCameraSequenceCameras -CameraSequenceId $camSeq

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraSequenceId
    )
    begin{
    }
    process{
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CameraSequenceId -RelationName "Members"
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias nscs New-SCCameraSequence
Function New-SCCameraSequence {
    <#
    .Synopsis
        Will create a new camera sequence with the provided name.
    .DESCRIPTION
        This method will create an new camera sequence in Security Center with the given name.  The return value will contain the Id of the new camera sequence.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new camera sequence upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type camera sequence with the name MyNewCameraSequence
        New-SCCameraSequence -n "MyNewCameraSequence"
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type camera sequence with the name MyNewCameraSequence
        nscs "MyNewCameraSequence"
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Sequences
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias rscs Remove-SCCameraSequence
Function Remove-SCCameraSequence {
    <#
    .Synopsis
        This method will remove the camera sequence represented by the provided CameraSequenceId from Security Center.
    .DESCRIPTION
        This method will permanently remove the specified camera sequence from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The CameraSequenceId parameter represents the entity to remove from Security Center.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $newSeq = New-SCCameraSequence -Name "MyCamSeq"

        #remove the new sequence by providing the user object
        Remove-SCCameraSequence -id $newSeq
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $newSeq = nscs "MyCamSeq"

        #remove the new user by providing the user object
        rscs $newSeq
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CameraSequenceId
        )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $CameraSequenceId
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCCameraSequenceCamera {
    <#
    .Synopsis
        Method used to remove a camera from the given camera sequence.
    .DESCRIPTION
        This Method will allow the user to remove a security center camera from the camera list of a security center camera sequence.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter CameraSequenceId will be used to specify the camera sequence we want to remove the camera from.

        The parameter CameraListPositionId represents the Id of the camera we want to remove from to the camera list of the camera sequence.
 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type camera sequence with the name MyNewCamSequence
        $camSeq = New-SCCameraSequence -n "MyNewCamSequence"
        
        #Will create a new entity of type user with the name MyNewUser
        $camera = Get-SCEntities -t cameras

        Add-SCCameraSequenceCamera -CameraSequenceId $camSeq -CameraId $camera[0]

        Remove-SCCameraSequenceCamera -CameraSequenceId $camSeq -CameraListPositionId 1
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $CameraSequenceId,
        [parameter(Mandatory=$true)] [alias("clpid")] $CameraListPositionId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntityRelation -EntityId $CameraSequenceId -RelationName "Members" -RelationId $CameraListPositionId  
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias sscs Set-SCCameraSequence
Function Set-SCCameraSequence() {
    <#
    .Synopsis
        Used to update the properties of a camera sequence in Security Center.
    .DESCRIPTION
        This method is used to update the properties of a camera sequence entity to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods

        The parameter CameraSequence represents and contains the properties that will be updated to security Center.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        #Will create a new entity of type camera sequence with the name MyNewCameraSequence
        $cs = New-SCCameraSequence -n "MyNewCameraSequence"
                
        $cs = Get-SCCameraSequence $cs

        #update it's description
        $cs.Description = "My new camera sequence"

        #update config in SecurityCenter
        Set-SCCameraSequence -CameraSequence $cs

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        #Will create a new entity of type camera sequence with the name MyNewCameraSequence2
        $cs = nscs -n "MyNewCameraSequence2" | gscs
                
        #update it's description
        $cs.Description = "My new camera sequence 2"

        #update config in SecurityCenter
        sscs $cs

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("cs")] $CameraSequence
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $CameraSequence
        }
    }
}

# -----------------------------------------------------------------------------
Function Set-SCCameraSequenceCamera {
    <#
    .Synopsis
        Method used to set the camera properties of a camera in a given camera sequence
    .DESCRIPTION
        This Method will allow the user to update the camera properties of a camera in a given camera sequence.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CameraSequenceId will be used to specify the camera sequence we want to add a camera to

        The parameter CameraId represents the Id of the camera we want to add to the camera list of the camera sequence

        The parameter DwellTimeInSec is the dwell time that will be used in the camera rotation of the sequence.  This parameter is optional with a default of 3

        The parameter PTZComand identifies which ptz command to trigger when the camera is displayed

        The parameter PatternPresetNb is the the number used to identify the pattern or the preset to trigger when the camera is displayed

        The parameter PTZAuxiliary identifies which auxiliary command to trigger when the camera is displayed

        The parameter AuxiliaryNb is the the number used to identify the auxiliary to trigger when the camera is displayed
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will create a new entity of type alarm with the name MyNewAlarm
        $camSeq = New-SCCameraSequence -n "MyNewCamSequence"
        
        #Will create a new entity of type user with the name MyNewUser
        $camera = Get-SCEntities -t cameras

        Add-SCCameraSequenceCamera -CameraSequenceId $camSeq -CameraId $camera[0]

        Set-SCCameraSequenceCamera -CameraSequenceId $camSeq -CameraListPositionId 1 -DwellTimeInSec 5
        

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $CameraSequenceId,
        [parameter(Mandatory=$true)] [alias("cid")] $CameraId,
        [parameter(Mandatory=$true)] [alias("clpid")] $CameraListPositionId,
        [parameter(Mandatory=$false)] [alias("d")] $DwellTimeInSec=3,
        [parameter(Mandatory=$false)] [alias("ppnb")] $PatternPresetNb=0,
        [parameter(Mandatory=$false)] [alias("anb")] $AuxiliaryNb=0
    )
    DynamicParam {
        $sess = GetSession -Quiet $true

        # Set the dynamic parameters' name
        $ParameterName1 = 'PTZComand'
        $ParameterAlias1 = "ptzc"
        $ParameterName2 = 'PTZAuxiliary'
        $ParameterAlias2 = "ptza"
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary
        
        # Create the collection of attributes
        $AttributeCollection1 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
        $AttributeCollection2 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute1 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute1.Mandatory = $false
        $ParameterAttribute1.Position = 7

        $ParameterAttribute2 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute2.Mandatory = $false
        $ParameterAttribute2.Position = 8

        # Add the attributes to the attributes collection
        $AttributeCollection1.Add($ParameterAttribute1)
        $AttributeCollection2.Add($ParameterAttribute2)

        # Generate and set the ValidateSet 
        if ($sess -ne $null) { 
            $arrSet1 = $sess.SCPatternPresetCommand
            $ValidateSetAttribute1 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet1)

            $arrSet2 = $sess.SCAuxiliaryCommand
            $ValidateSetAttribute2 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet2)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection1.Add($ValidateSetAttribute1)
            $AttributeCollection2.Add($ValidateSetAttribute2)
        }
        # don't validate if we can't retrieve
        
        #add the alias to the attributes collection
        $ParamAlias1 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias1
        $AttributeCollection1.Add($ParamAlias1)
        $ParamAlias2 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias2
        $AttributeCollection2.Add($ParamAlias2)

        # Create and return the dynamic parameter
        $RuntimeParameter1 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName1, [string], $AttributeCollection1)
        $RuntimeParameterDictionary.Add($ParameterName1, $RuntimeParameter1)

        $RuntimeParameter2 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName2, [string], $AttributeCollection2)
        $RuntimeParameterDictionary.Add($ParameterName2, $RuntimeParameter2)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $PTZComand = $PsBoundParameters[$ParameterName1]
        $PTZAuxiliary = $PsBoundParameters[$ParameterName2]
    }
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $csid = GetIdFromObject $CameraSequenceId
            $cid = GetIdFromObject $CameraId
            $uri = "Entities/$csid/Members/$CameraListPositionId"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", $CameraListPositionId)
            $jsonObject.Add("CameraId", $cid)
            $jsonObject.Add("Dwell", $DwellTimeInSec)

            if($PTZComand)
            {
                $jsonObject.Add("Command", $PTZComand)
                $jsonObject.Add("CommandNumber", $PatternPresetNb)
            }

            if($PTZAuxiliary)
            {
                $jsonObject.Add("Auxiliary", $PTZAuxiliary)
                $jsonObject.Add("AuxiliaryNumber", $AuxiliaryNb)
            }

            $jsonBody = $jsonObject | ConvertTo-Json
            $seqResult = InvokeSCRestMethod -UriSuffix $uri -Method "PUT" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCCameraSequenceProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a camera sequence.
    .DESCRIPTION
        This method will list the supported properties and relation of a camera sequence (the data model, not the actual data).  This method is used
        when you want to know what is available for a given camera sequence
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCCameraSequenceProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiSequence" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'