﻿using Microsoft.Extensions.Configuration;

namespace LPPermission.UI.API.Helper
{
    /// <summary>
    /// This function is used to connect with Azure Key Vault 
    /// </summary>
    public class KeyVaultService
    {
        private readonly IConfiguration _configuration;

        public KeyVaultService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        // Retrieve a specific secret by key
        public string GetSecret(string key)
        {
            return _configuration[key];
        }

        // Retrieve all secrets as a dictionary
        public Dictionary<string, string> GetAllSecrets()
        {
            return _configuration.AsEnumerable()
                .Where(kvp => !string.IsNullOrEmpty(kvp.Value)) // Filter out null or empty values
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }
    }
}
