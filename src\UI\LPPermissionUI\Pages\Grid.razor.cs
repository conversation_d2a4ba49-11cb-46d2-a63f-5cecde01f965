﻿using LPPermission.UI.Models;
using LPPermission.UI.Services.Interface;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Options;
using Telerik.Blazor.Components;

namespace LPPermission.UI.Pages
{
    public partial class Grid
    {
        // Injected NavigationManager for navigation between pages
        [Inject] private NavigationManager Navigation { get; set; }

        // Injected HttpClient for making HTTP requests
        [Inject] private HttpClient Http { get; set; }

        // Injected API settings from configuration
        [Inject] private IOptions<ApiSettings> ApiSettings { get; set; }

        // Injected telemetry client for logging exceptions and events
        [Inject] private ITelemetryClient TelemetryClient { get; set; }

        // List of privileges to be displayed in the grid
        [Parameter]
        public List<PrivilegeDto> Privileges { get; set; } = new List<PrivilegeDto>();

        // Service provider for dependency injection
        [Parameter]
        public IServiceProvider ServiceProvider { get; set; }

        public int PageSize { get; set; }  // Default page size for the grid   
        /// <summary>
        /// Lifecycle method called when the component is initialized.
        /// Fetches the list of privileges from the API.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                if (ApiSettings?.Value?.GetPermissionUrl == null)
                {
                    throw new InvalidOperationException("GetPermissionUrl is not configured.");
                }

                // Fetch privileges from the API
                var url = ApiSettings.Value.GetPermissionUrl;
                PageSize = ApiSettings.Value.PageSize;
                Privileges = await Http.GetFromJsonAsync<List<PrivilegeDto>>(url);
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during OnInitializedAsync in Grid component.");
            }
        }

        /// <summary>
        /// Handles the update operation for a privilege in the grid.
        /// </summary>
        /// <param name="args">Event arguments containing the privilege to update.</param>
        public void UpdateHandler(GridCommandEventArgs args)
        {
            try
            {
                var privilege = (PrivilegeDto)args.Item;
                // Implement update logic here
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during UpdateHandler in Grid component.");
            }
        }

        /// <summary>
        /// Handles the delete operation for a privilege in the grid.
        /// </summary>
        /// <param name="args">Event arguments containing the privilege to delete.</param>
        public void DeleteHandler(GridCommandEventArgs args)
        {
            try
            {
                var privilege = (PrivilegeDto)args.Item;
                Privileges.Remove(privilege);
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during DeleteHandler in Grid component.");
            }
        }

        /// <summary>
        /// Handles the create operation for a new privilege in the grid.
        /// </summary>
        /// <param name="args">Event arguments containing the privilege to create.</param>
        public void CreateHandler(GridCommandEventArgs args)
        {
            try
            {
                var privilege = (PrivilegeDto)args.Item;
                Privileges.Add(privilege);
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during CreateHandler in Grid component.");
            }
        }

        /// <summary>
        /// Calls an external function app using the configured URL.
        /// </summary>
        private async Task CallFunctionApp()
        {
            try
            {
                var url = ApiSettings.Value.FunctionAppUrl;

                // Make an HTTP GET request to the function app
                var response = await Http.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Function app called successfully.");
                }
                else
                {
                    Console.WriteLine($"Error: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                LogException(ex, "Error during CallFunctionApp in Grid component.");
            }
        }

        /// <summary>
        /// Helper method to log exceptions using Application Insights.
        /// </summary>
        /// <param name="ex">The exception to log.</param>
        /// <param name="message">A custom message to include with the exception.</param>
        private void LogException(Exception ex, string message)
        {
            var exceptionTelemetry = new ExceptionTelemetry(ex)
            {
                SeverityLevel = SeverityLevel.Error
            };
            exceptionTelemetry.Properties.Add("CustomMessage", message);
            TelemetryClient.TrackException(exceptionTelemetry);

            Console.WriteLine($"{message}: {ex.Message}");
        }
    }
}