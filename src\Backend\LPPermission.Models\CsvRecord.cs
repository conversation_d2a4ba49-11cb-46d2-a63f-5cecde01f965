﻿namespace LPPermission.Models
{
    public class CsvRecord
    {
        public string Parent { get; set; }
        public string Privilege { get; set; }
        public string State { get; set; }
        public string InheritedFrom { get; set; } // Property for "Inherited from" header
        public string Description { get; set; }
        public Dictionary<string, string> GroupPermissions { get; set; } = new Dictionary<string, string>();
    }
}
