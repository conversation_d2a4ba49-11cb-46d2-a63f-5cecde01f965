using System;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Azure.Security.KeyVault.Secrets;
using Azure.Identity;

namespace FunctionApp.LPFunctions
{
    public class LPManager
    {
        private readonly ILogger<LPManager> _logger;
        private static readonly HttpClient httpClient = new HttpClient();
        private readonly IConfiguration _configuration;
        private SecretClient _secretClient;
        private string systemInfo;
        private bool _telemetryEnabled;
        public LPManager(ILogger<LPManager> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            string keyVaultUrl = configuration["KeyVaultUrl"];
            _secretClient = new SecretClient(new Uri(keyVaultUrl), new DefaultAzureCredential());
	_telemetryEnabled = Convert.ToBoolean(configuration["Telemetry:Enabled"]);
        }

        [Function("LPManager")]
        public async Task Run([TimerTrigger("0 0 14 * * *" ,RunOnStartup = true)] TimerInfo myTimer, ILogger<LPManager> log)
        {
            _logger.LogInformation($"C# Timer trigger function LPPermssion executed from pipeline at: {DateTime.Now}");
            try
            {
                // Fetch the LPPermissionFunctionURL from Key Vault
                string lpPermissionUrl = await GetCredential("LPPermissionFunctionURL");

                if (string.IsNullOrEmpty(lpPermissionUrl))
                {
                    _logger.LogError("LPPermissionFunctionURL is not configured in Key Vault.");
                    return;
                }

                HttpResponseMessage response = await httpClient.GetAsync(lpPermissionUrl);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    _logger.LogInformation("Success");
                }
                else
                {
                    _logger.LogInformation("Failed");
                }

                _logger.LogInformation($"C# Timer trigger function LPPermssion finish execution at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
            }
        }
        private async Task<string> GetCredential(string credentialName)
        {
            var log = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger("GetCredential");
            try
            {
                KeyVaultSecret secret = await _secretClient.GetSecretAsync(credentialName);
                return secret.Value;
            }
            catch (Exception ex)
            {
                log.LogError(ex, $"Failed to get credential: {credentialName}");
                throw;
            }
        }
    }
}
