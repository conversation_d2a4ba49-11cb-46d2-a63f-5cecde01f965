﻿/* General Body Styling */
body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f7fb;
    color: #333;
    margin: 0;
    padding: 0;
    height: 100vh;
}

/* Flexbox layout for the page content */
.page-content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* Align content to the top */
    align-items: center;
    padding: 40px;
    height: 100%;
}

/* Styling the file upload container */
.file-upload-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 30px; /* Increased padding */
    max-width: 600px;
    width: 100%;
    box-sizing: border-box;
    text-align: center; /* Center align the content inside the container */
    margin-bottom: 20px; /* Add margin to separate from other content */
}

    /* File Upload label and hint */
    .file-upload-container .k-form-label {
        color: #85151e;
        font-weight: bold;
        font-size: 16px; /* Increased font size */
        margin-bottom: 10px; /* Added margin */
    }

    .file-upload-container .k-form-hint {
        color: #85151e;
        font-size: 14px; /* Increased font size */
        margin-top: 10px; /* Added margin */
    }

/* File Upload Button (Select Files) */
.k-upload-button {
    font-size: 12px; /* Smaller font size */
    padding: 4px 8px; /* Reduced padding */
    height: 30px; /* Set a fixed height */
    min-width: 100px; /* Reduced minimum width */
    width: auto; /* Let the width be automatically determined by the content */
    border-radius: 5px;
    background-color: #85151e;
    color: white;
    border: none;
    cursor: pointer;
    line-height: 18px; /* Adjusts the button's inner text */
}

    .k-upload-button:hover {
        background-color: #6a0c12;
    }

/* Styling the Submit Button */
.k-button {
    background-color: #85151e;
    color: white;
    border: none;
    padding: 4px 8px; /* Reduced padding */
    height: 30px; /* Set a fixed height */
    min-width: 100px; /* Reduced minimum width */
    border-radius: 5px;
    font-size: 12px; /* Smaller font size */
    cursor: pointer;
    width: auto;
    margin-top: 20px; /* Added margin */
}

    .k-button:hover {
        background-color: #6a0c12;
    }

/* Optional: Additional styling for required field indicator */
.k-required {
    color: #d32f2f;
}

/* Logo styling */
.logo {
    text-align: center;
    margin-bottom: 30px;
}

    .logo img {
        width: 150px; /* Larger logo size */
        height: auto;
    }

/* Optional: Add a footer if needed */
.footer {
    margin-top: 40px;
    text-align: center;
    font-size: 14px;
    color: #555;
}

    .footer a {
        color: #85151e;
        text-decoration: none;
    }

        .footer a:hover {
            text-decoration: underline;
        }
/* Styling for the status message */
.status-message {
    margin-top: 20px;
    font-size: 14px;
    color: #d32f2f; /* Red color for error messages */
}

    .status-message.success {
        color: #4caf50; /* Green color for success messages */
    }
.grid-page {
    background-color: #85151e;
    color: white;
    height: 100vh;
    padding: 20px;
}

.grid-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-top: 20px;
    color: #333;
}

.grid-header {
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: white;
    font-family: 'Arial', sans-serif;
}

.k-grid-header {
    background-color: #85151e;
    color: white;
    font-size: 12px;
}

.k-button {
    background-color: #85151e;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
}

    .k-button:hover {
        background-color: #6a0c12;
    }

.k-grid-content {
    font-size: 12px;
}

/* Styling the file upload container */
.file-upload-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 30px; /* Increased padding */
    max-width: 600px;
    width: 100%;
    box-sizing: border-box;
    text-align: center; /* Center align the content inside the container */
    margin-bottom: 20px; /* Add margin to separate from other content */
}

    /* File Upload label and hint */
    .file-upload-container .k-form-label {
        color: #85151e;
        font-weight: bold;
        font-size: 16px; /* Increased font size */
        margin-bottom: 10px; /* Added margin */
    }

    .file-upload-container .k-form-hint {
        color: #85151e;
        font-size: 14px; /* Increased font size */
        margin-top: 10px; /* Added margin */
    }

/* File Upload Button (Select Files) */
.k-upload-button {
    font-size: 12px; /* Smaller font size */
    padding: 4px 8px; /* Reduced padding */
    height: 30px; /* Set a fixed height */
    min-width: 100px; /* Reduced minimum width */
    width: auto; /* Let the width be automatically determined by the content */
    border-radius: 5px;
    background-color: #85151e;
    color: white;
    border: none;
    cursor: pointer;
    line-height: 18px; /* Adjusts the button's inner text */
}

    .k-upload-button:hover {
        background-color: #6a0c12;
    }

/* Styling the Submit Button */
.k-button {
    background-color: #85151e;
    color: white;
    border: none;
    padding: 4px 8px; /* Reduced padding */
    height: 30px; /* Set a fixed height */
    min-width: 100px; /* Reduced minimum width */
    border-radius: 5px;
    font-size: 12px; /* Smaller font size */
    cursor: pointer;
    width: auto;
    margin-top: 20px; /* Added margin */
}

    .k-button:hover {
        background-color: #6a0c12;
    }

/* Optional: Additional styling for required field indicator */
.k-required {
    color: #d32f2f;
}

/* Styling for the status message */
.status-message {
    margin-top: 20px;
    font-size: 14px;
    color: #d32f2f; /* Red color for error messages */
}

    .status-message.success {
        color: #4caf50; /* Green color for success messages */
    }