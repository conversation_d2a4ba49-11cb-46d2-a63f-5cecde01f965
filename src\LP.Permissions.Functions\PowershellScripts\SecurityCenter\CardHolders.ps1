﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCCardholderCredential {
    <#
    .Synopsis
        Method used to add a credential to the given cardholder
    .DESCRIPTION
        This Method will allow the user to add a security center credential a specified cardholder
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CardholderId will be used to specify the cardholder we want to add the credential to

        The parameter CredentialId represents the Id of the credential we want to add to the cardholder
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $ch = New-SCCardholder -n "MyNewCardHolder"
        
        $cred = New-SCCredentials -n "MyNewCredential"

        Add-SCCardholderCredential -CardholderId $ch -CredentialId $cred
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cid")] $CredentialId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CredentialId
            Set-SCEntityRelation -EntityId $CardholderId -RelationName "credentials" -RelationId $cid   
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias gsch Get-SCCardholder
Function Get-SCCardholder {
    <#
    .Synopsis
        This method will return all the properties of the cardholder represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the cardholder represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CardholderId parameter represents the Id of the cardholder to retrieve (The guid representing the cardholder in the Security Center System)
        You can also pass any cardholder object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ch = New-SCCardholder -n "MyNewCardHolder"
        Get-SCCardholder -CardholderId $ch.Id

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ch = New-SCCardholder -n "MyNewCardHolder2" | Get-SCCardholder

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ch = nsch -n "MyNewCardHolder2" | gsch

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CardholderId            
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCCardholderCardholderGroups {
    <#
    .Synopsis
        Method used to get all cardholder groups a given cardholder is part of.
    .DESCRIPTION
        This Method will allow the user to get all the cardholder groups of the given cardholder.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter CardholderId will be used to specify the cardholder we want to get the cardholder groups from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $ch = New-SCCardholder -n "MyNewCardHolder"
        
        $chg = New-SCCardholderGroup -n "MyNewGroup"

        Add-SCCardholderGroupMembers -CardholderGroupId $chg -Memberid $ch

        Get-SCCardholderCardholderGroups -CardholderId $ch
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $CardholderId -RelationName "Cardholdergroups"
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCCardholderCredentials {
    <#
    .Synopsis
        Method used to get all credentials of the given cardholder
    .DESCRIPTION
        This Method will allow the user to get all credentials of the given cardholder
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CardholderId will be used to specify the cardholder we want to get the credentials from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $ch = New-SCCardholder -n "MyNewCardHolder"
        
        $cred = New-SCCredentials -n "MyNewCredential"

        Add-SCCardholderCredential -CardholderId $ch -CredentialId $cred

        Get-SCCardholderCredentials -CardholderId $ch
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $CardholderId -RelationName "credentials"
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCCardholderPicture {
    <#
    .Synopsis
        Method used to get the picture of the given cardholder.
    .DESCRIPTION
        This Method will allow the user to get the picture in base64String of the given cardholder
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CardholderId will be used to specify the cardholder we want to get the picture from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #make sure a cardholder with the name TestCardholder is in the system with a picture
        $ch = Search-SCEntities -Type Cardholders -Name "TestCardholder"

        Get-SCCardholderPicture -CardholderId $ch
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $CardholderId -RelationName "Picture"
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias nsch New-SCCardholder
Function New-SCCardholder {
    <#
    .Synopsis
        Method used to create a new cardholder with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new cardholder with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new cardholder upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $ch = New-SCCardholder -n "MyNewCardHolder"

        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $ch = nsch "MyNewCardHolder"

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Cardholders
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias rsch Remove-SCCardholder
Function Remove-SCCardholder {
    <#
    .Synopsis
        Will remove the cardholder represented by the provided CardholderId from Security Center
    .DESCRIPTION
        This method will permanently remove the specified cardholder from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CardholderId parameter represents the entity to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $ch = New-SCCardholder -n "MyNewCardHolder"

        Remove-SCCardholder -CardholderId $ch.Id

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $ch = nsch "MyNewCardHolder"

        rsch $ch

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $CardholderId
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCCardholderCredential {
    <#
    .Synopsis
        Method used to remove a credential from the given cardholder
    .DESCRIPTION
        This Method will allow the user to remove a security center credential from a specified cardholder
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CardholderId will be used to specify the cardholder we want to remove the credential from

        The parameter CredentialId represents the Id of the credential we want to remove from the cardholder
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $ch = New-SCCardholder -n "MyNewCardHolder"
        
        $cred = New-SCCredentials -n "MyNewCredential"

        Add-SCCardholderCredential -CardholderId $ch -CredentialId $cred

        Remove-SCCardholderCredential -CardholderId $ch -CredentialId $cred
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cid")] $CredentialId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CredentialId
            Remove-SCEntityRelation -EntityId $CardholderId -RelationName "credentials" -RelationId $cid  
        }
    }   
}

# -----------------------------------------------------------------------------
Function Remove-SCCardholderPicture {
    <#
    .Synopsis
        Method used to remove the picture of the given cardholder
    .DESCRIPTION
        This Method will allow the user to remove the picture of the given cardholder
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CardholderId will be used to specify the cardholder we want to get the picture from
        The parameter PictureId will be used to specify the picture we want to remove from the cardholder
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #make sure a cardholder with the name TestCardholder is in the system with a picture
        $ch = Search-SCEntities -Type Cardholders -Name "TestCardholder"

        $pic = Get-SCCardholderPicture -CardholderId $ch
        Remove-SCCardholderPicture -CardholderId $ch  -PictureId $pic.Guid
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PictureId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $pid = GetIdFromObject $PictureId
            Remove-SCEntityRelation -EntityId $CardholderId -RelationName "Picture" -RelationId $pid  
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias ssch Set-SCCardholder
Function Set-SCCardholder() {
    <#
    .Synopsis
        Used to update the properties of an cardholder in Security Center
    .DESCRIPTION
        This method is used to update the properties of a cardholder to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Cardholder represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newch = New-SCCardholder -Name "Mych"  | gsch
        $newch.FirstName = "test"
        
        Set-SCCardholder -Cardholder $newch

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newch = New-SCCardholder -Name "Mych"  | gsch
        $newch.FirstName = "test"
        
        ssch $newch

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("ch")] $Cardholder
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Cardholder
        }
    }
}

# -----------------------------------------------------------------------------
Function Set-SCCardholderPicture {
    <#
    .Synopsis
        Method used to set the picture of the given cardholder
    .DESCRIPTION
        This Method will allow the user to set the picture in base64String of the given cardholder
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CardholderId will be used to specify the cardholder we want to get the picture from
        The parameter PictureInStringBase64 will be used to specify the picture of the cardholder
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #make sure a cardholder with the name TestCardholder is in the system with a picture
        $ch = Search-SCEntities -Type Cardholders -Name "TestCardholder"

        #replace the picture file path with a valid one
        $picture = [convert]::ToBase64String((Get-Content D:\Images\image1.jpg -Encoding byte))
        Set-SCCardholderPicture -CardholderId $ch -PictureInStringBase64 $picture
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pic")] $PictureInStringBase64
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            $cid = GetIdFromObject $CardholderId
            $uri = "Entities/$cid/Picture"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Data", $PictureInStringBase64)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Post" -Body $jsonBody 
        }
    }  
}

# -----------------------------------------------------------------------------
Function Search-SCCardholdersAccessStatus {
    <#
    .Synopsis
        Searches security center Cardholders and returns all cardholders who's status matches the provided one
    .DESCRIPTION
        This method is used to return all cardholder who's status matches the provided one

        The parameter AccessStatus is the status of the cardholders we want to search for

        The parameter Filter (optional) is used to specify the data returned by the method.  Base value will only return the minimum 
        data and the All Value will return all available data.  Base is the default value
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCCardholdersAccessStatus -AccessStatus Active -f All
                
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCCardholdersAccessStatus -as Active -f All
                
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
    )
 
    DynamicParam {
        $sess = GetSession -Quiet $true

        # Set the dynamic parameters' name
        $ParameterName1 = 'AccessStatus'
        $ParameterAlias1 = "as"
        $ParameterName2 = 'Filter'
        $ParameterAlias2 = "f"
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary
        
        # Create the collection of attributes
        $AttributeCollection1 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
        $AttributeCollection2 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute1 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute1.Mandatory = $true
        $ParameterAttribute1.Position = 1

        $ParameterAttribute2 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute2.Mandatory = $false
        $ParameterAttribute2.Position = 2

        # Add the attributes to the attributes collection
        $AttributeCollection1.Add($ParameterAttribute1)
        $AttributeCollection2.Add($ParameterAttribute2)

        # Generate and set the ValidateSet 
        if ($sess -ne $null) { 
            $arrSet1 = $sess.SCAccessStatus
            $ValidateSetAttribute1 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet1)

            $arrSet2 = $sess.SCFilterCache
            $ValidateSetAttribute2 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet2)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection1.Add($ValidateSetAttribute1)
            $AttributeCollection2.Add($ValidateSetAttribute2)
        }
        # don't validate if we can't retrieve
        
        #add the alias to the attributes collection
        $ParamAlias1 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias1
        $AttributeCollection1.Add($ParamAlias1)
        $ParamAlias2 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias2
        $AttributeCollection2.Add($ParamAlias2)

        # Create and return the dynamic parameter
        $RuntimeParameter1 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName1, [string], $AttributeCollection1)
        $RuntimeParameterDictionary.Add($ParameterName1, $RuntimeParameter1)

        $RuntimeParameter2 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName2, [string], $AttributeCollection2)
        $RuntimeParameterDictionary.Add($ParameterName2, $RuntimeParameter2)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $AccessStatus = $PsBoundParameters[$ParameterName1]
        $Filter = $PsBoundParameters[$ParameterName2]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uriSuffix = "Entities/Cardholders?AccessStatus=" + $AccessStatus            

            if($Filter) {
                $uriSuffix = $uriSuffix + "&ValidFlags=" + $Filter
            }

            $result = InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'Get'

            if($IdOnly) {
                $result | foreach {Write-Output $_.Id}
            }
            else {
                return $result
            }
        }
    }
}

# -----------------------------------------------------------------------------
Function Search-SCCardholdersExpirationDate {
    <#
    .Synopsis
        Searches security center Cardholders and returns all cardholder who's expiration date falls in the provided range
    .DESCRIPTION
        This method is used to return all cardholder who's expiration date falls in the provided range

        The parameter ExpirationStartTime is the start of the range to search for

        The parameter ExpirationEndTime is the end of the range to search for

        The parameter Filter (optional) is used to specify the data returned by the method.  Base value will only return the minimum 
        data and the All Value will return all available data.  Base is the default value
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCCardholdersExpirationDate -ExpirationStartTime (Get-Date).AddDays(-1) -ExpirationEndTime (Get-Date).AddDays(1) -f all
                
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCCardholdersExpirationDate -est (Get-Date).AddDays(-1) -eet (Get-Date).AddDays(1) -f all
                
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$false,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("est")] [DateTime]$ExpirationStartTime,
        [parameter(Mandatory=$false,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("eet")] [DateTime]$ExpirationEndTime
    )
 
    DynamicParam {
        $sess = GetSession -Quiet $true

        # Set the dynamic parameters' name
        $ParameterName2 = 'Filter'
        $ParameterAlias2 = "f"
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary
        
        # Create the collection of attributes
        $AttributeCollection2 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]

        $ParameterAttribute2 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute2.Mandatory = $false
        $ParameterAttribute2.Position = 2

        # Add the attributes to the attributes collection
        $AttributeCollection2.Add($ParameterAttribute2)

        # Generate and set the ValidateSet 
        if ($sess -ne $null) { 
            $arrSet2 = $sess.SCFilterCache
            $ValidateSetAttribute2 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet2)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection2.Add($ValidateSetAttribute2)
        }
        # don't validate if we can't retrieve
        
        #add the alias to the attributes collection
        $ParamAlias2 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias2
        $AttributeCollection2.Add($ParamAlias2)

        # Create and return the dynamic parameter
        $RuntimeParameter2 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName2, [string], $AttributeCollection2)
        $RuntimeParameterDictionary.Add($ParameterName2, $RuntimeParameter2)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $Filter = $PsBoundParameters[$ParameterName2]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uriSuffix = "Entities/Cardholders?ExpirationTimeStart=" + $ExpirationStartTime.ToString("o") + "&ExpirationTimeEnd=" + $ExpirationEndTime.ToString("o")        

            if($Filter) {
                $uriSuffix = $uriSuffix + "&ValidFlags=" + $Filter
            }

            $result = InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'Get'

            if($IdOnly) {
                $result | foreach {Write-Output $_.Id}
            }
            else {
                return $result
            }
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCCardholderActivityReport {
    <#
    .Synopsis
        Used to retrieve a cardholder activity report
    .DESCRIPTION
        This method will return a cardholder activity report for all the activities of the specified cardholders.  The report will contain a Results property
        that contains 2 arrays.  The first array will be the column definition.  This will contain the information this will be in each rows.
        The second array is the actual data of the report matching the column definition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The cardholder parameter represents the Id of the cardholder to retrieve (The guid representing the cardholder in the Security Center System)
        You can also pass any cardholder object that contains an ID as a parameter
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $ch = Search-SCCardholdersAccessStatus -AccessStatus Active

        Show-SCCardholderActivityReport $ch[0]

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $Cardholder
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $Cardholder   
            $uri = "Reports/CardholderActivities?Cardholders="  + $cid
            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        }
    }  
}

# -----------------------------------------------------------------------------
Function Show-SCCardholderProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an cardholder
    .DESCRIPTION
        This method will list the supported properties and relation of an cardholder (the data model, not the actual data).  This method is used
        when you want to know what is available for a given cardholder
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCCardholderProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiCardholder" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'