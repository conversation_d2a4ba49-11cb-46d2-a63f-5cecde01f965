# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------

Function Add-SCReverseTunnelSite {
    <#
    .Synopsis
        This method will add a new remote site to an existing ReverseTunnelServer role.
    .DESCRIPTION
        This method will add a new remote site to an existing ReverseTunnelServer role. Once done, it will output a 
        unique keyfile that can then be sent to client to establish a connection. If a remote site with the same name as
        the one provided already exists, an error will be thrown.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 
    .PARAMETER RoleId 
        Unique identifier of the role to which the site will be added (The guid representing the role in the Security Center System).
    .PARAMETER SiteName 
        The name of the remote site that will be added. It must be unique.
    .PARAMETER MandatoryExistingEntity 
        A GUID from an entity in the system that will be included in the keyfile. This will prevent customers from using the wrong 
        keyfile.
    .PARAMETER FriendlyDescription
        A description that will be displayed during the setup process of the client role.
    .PARAMETER ForceReset
        If a Reverse Tunnel site with the same already exists, it will be replaced by this new one. 
	.PARAMETER EncryptionMode
		Optional, a value of 'Encrypted', 'PreferEncryption', or 'Disabled'.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myTunnel = Get-SCRoles -Type ReverseTunnelServer 
        Add-SCReverseTunnelSite -RoleId $myTunnel.Id -SiteName "MontrealOffice"

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("Name")] $SiteName,
        [parameter(Mandatory=$false)] $MandatoryExistingEntity,
        [parameter(Mandatory=$false)] $FriendlyDescription,
        [parameter(Mandatory=$false)] $EncryptionMode,
        [parameter(Mandatory=$false)] [switch] $ForceReset
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uri = "Entities/$RoleId/Sites"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Name", $SiteName)

			if ($ForceReset) {
				$jsonObject.Add("forceReset", "true")
			}
			if ($MandatoryExistingEntity) {
				$jsonObject.Add("mandatoryExistingEntity", $MandatoryExistingEntity)
			}
			if ($EncryptionMode) {
				$jsonObject.Add("encryptionMode", $EncryptionMode)
			}
			if ($FriendlyDescription) {
				$jsonObject.Add("friendlyDescription", $FriendlyDescription)
			}

            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Post" -Body $jsonBody
        }
    }
}

Function Remove-SCReverseTunnelSite {
    <#
    .Synopsis
        This method will remove a remote site from an existing ReverseTunnelServer role.
    .DESCRIPTION
        This method will remove a remote site from an existing ReverseTunnelServer role.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 
    .PARAMETER RoleId 
        Unique identifier of the role from which the site will be removed (The guid representing the role in the Security Center System).
    .PARAMETER SiteName 
        The name of the remote site that will be removed. It must be unique.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myTunnel = Get-SCRoles -Type ReverseTunnelServer 
        Remove-SCReverseTunnelSite -RoleId $myTunnel.Id -SiteName "MontrealOffice"

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("Name")] $SiteName
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uri = "Entities/$RoleId/Sites/$SiteName"
            InvokeSCRestMethod -UriSuffix $uri -Method "Delete"
        }
    }
}