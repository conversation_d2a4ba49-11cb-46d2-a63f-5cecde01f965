using Azure.Identity;
using LPPermission.BLL;
using LPPermission.BLL.Interfaces;
using LPPermission.BLL.Services;

using LPPermission.DAL.Interfaces;
using LPPermission.DAL.Models;
using LPPermission.DAL.Repositories;
using LPPermission.UI.API;
using LPPermission.UI.API.Helper;
using Microsoft.Azure.Functions.Worker.Builder;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using System.Text;

var builder = FunctionsApplication.CreateBuilder(args);

builder.ConfigureFunctionsWebApplication();
var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("local.settings.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables()
                .Build();

builder.Services.AddScoped<IGenericRepository<Menu>, GenericRepository<Menu>>();
builder.Services.AddScoped<IGenericRepository<Privilege>, GenericRepository<Privilege>>();
builder.Services.AddScoped<IGenericRepository<Permission>, GenericRepository<Permission>>();
builder.Services.AddScoped<IGenericRepository<UserGroup>, GenericRepository<UserGroup>>();
builder.Services.AddScoped<IGenericRepository<State>, GenericRepository<State>>();
builder.Services.AddScoped<IGenericRepository<UserLogin>, GenericRepository<UserLogin>>();
builder.Services.AddScoped<IGenericRepository<Geo>, GenericRepository<Geo>>();
builder.Services.AddScoped<IGenericRepository<Container>, GenericRepository<Container>>();
builder.Services.AddDbContext<LppermissionsContext>(options =>
    options.UseSqlServer(configuration["ConnectionStrings:LppermissionsDatabase"]));


builder.Services.AddScoped<IUpload, UploadService>();
builder.Services.AddSingleton<IFunctionsWorkerMiddleware, LoggerMiddleware>();

// Register generic repository services
builder.Services.AddScoped<IGenericRepository<UploadService>, GenericRepository<UploadService>>();

// Register function-specific middlewares
builder.Services.AddScoped<IFunctionsWorkerMiddleware, LoggerMiddleware>();
builder.Services.AddScoped<IFunctionsWorkerMiddleware, AuthenticationMiddleware>();

// Register other services for dependency injection
builder.Services.AddScoped<IJwtTokenGenerateService, JwtTokenGenerateService>();
builder.Services.AddScoped<IUser, User>();

// Register JWT authentication
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = "JwtBearer";
    options.DefaultChallengeScheme = "JwtBearer";
})
.AddJwtBearer("JwtBearer", options =>
{
    var secretKey = configuration["JwtSecretKey"];
    var key = Encoding.ASCII.GetBytes(secretKey);

    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = configuration["JwtIssuer"],
        ValidAudience = configuration["JwtAudience"],
        IssuerSigningKey = new SymmetricSecurityKey(key)
    };
});

// Enable middleware for authentication
builder.UseMiddleware<AuthenticationMiddleware>();

// Add authorization services
builder.Services.AddAuthorization();

// Register logging services
builder.Services.AddLogging();

builder.UseMiddleware<LoggerMiddleware>();

builder.Build().Run();
