﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias asaa Add-SCArchiverAgent
Function Add-SCArchiverAgent {
    <#
    .Synopsis
        This method will add a archiver agent to an archiver role.
    .DESCRIPTION
        This method is used when the user wants to add an archiver agent to an archiver role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter RoleId is used to specify the role you want to add the agent to.

        The parameter ServerId is used to specify the server where the agent will run.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myArc = Get-SCRoles -Type Archiver | Get-SCRole

        $serverToAdd = Get-SCEntities -Type Servers

        #make sure to select the server entity that you need to add to the archiver
        Add-SCArchiverAgent -Id $myArc -ServerId $serverToAdd[0]
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sid")] $ServerId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $sid = GetIdFromObject $ServerId

            $uri = "Entities/$rid/Agents/"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("ServerId", $sid)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias bsaad Backup-SCArchiverAgentDatabase
Function Backup-SCArchiverAgentDatabase {
    <#
    .Synopsis
        Method used to start the backup of a Archiver Agent database.
    .DESCRIPTION
        This method will trigger a backup of the database of the specified Archiver Agent.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter ArchiverAgentId is used to specify the archiver agent you want to backup the db from

        The parameter BackupFolder is the location where the backup will be created.  This folder must be accessible from the
        server where the role is running.

        The parameter EnableCompression is optional (false by default) and is used to compress or not the backup upon completion.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myArc = Get-SCRoles -Type Archiver
        $myAgent = Get-SCArchiverAgents $myArc.Id

        Backup-SCArchiverAgentDatabase -ArchiverAgentId $myAgent -BackupFolder "C:\SecurityCenterBackup" -EnableCompression $false
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $ArchiverAgentId,
        [parameter(Mandatory=$true)] [alias("bf")][string]$BackupFolder,
        [parameter(Mandatory=$false)] [alias("ec")] [bool]$EnableCompression=$false
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $ArchiverAgentId
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Folder", $BackupFolder)
            $jsonObject.Add("EnableCompression", $EnableCompression)
            $jsonBody = $jsonObject | ConvertTo-Json
            $uri = "Entities/$rid/CreateDatabaseBackup"
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias dbsa Debug-SCArchiverRole
Function Debug-SCArchiverRole {
    <#
    .Synopsis
        This will launch the diagnose command for the selected archiver role
    .DESCRIPTION
        This method will launch the diagnose command for the given archiver role  This method is used
        when you want to troubleshoot a given archiver role.

        The ArchiverId parameter represents the Id of the archiver role to diagnose (The guid representing the role in the Security Center System)
        You can also pass any archiver role object that contains an ID as a parameter
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will retreive the archiver role
        $entity = Get-SCRoles -t Archiver
        
        Debug-SCArchiverRole -ArchiverId $entity

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("Id")] $ArchiverId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $ArchiverId -RelationName "diagnostics" 
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias gsarm Get-SCArchiverRecordingModes
Function Get-SCArchiverRecordingModes {
    <#
    .Synopsis
        This method will return the archiver recording modes that the archiver has configured.
    .DESCRIPTION
        This method is used to return all the archiver recording modes that the archiver has configured.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver to retrieve the recording modes from(The guid representing the archiver in the Security Center System)
        You can also pass any archiver role object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        Get-SCArchiverRecordingModes $myArc.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "RecordingModes"  
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsac Get-SCArchiverCameras
Function Get-SCArchiverCameras {
    <#
    .Synopsis
        This method will return all the cameras controlled by the given archiver.

    .DESCRIPTION
        This method is used to return all the cameras under the given archiver.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The RoleId parameter represents the Id of the archiver to retrieve the cameras from (The guid representing the archiver in the Security Center System)
        You can also pass any archiver role object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        Get-SCArchiverCameras $myArc.Id

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.

    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "Cameras"  
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCArchiverVideoUnits {
    <#
    .Synopsis
        This method will return all the video units controlled by the given archiver.

    .DESCRIPTION
        This method is used to return all the video units under the given archiver.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The RoleId parameter represents the Id of the archiver to retrieve the cameras from (The guid representing the archiver in the Security Center System)
        You can also pass any archiver role object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        Get-SCArchiverVideoUnits  $myArc.Id

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.

    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "videounits"  
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCArchiverAgent {
    <#
    .Synopsis
        This method will return the specified archiver agent.
    .DESCRIPTION
        This method is used to return the specified archiver agents .
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The AgentId parameter represents the Id of the agent of the archiver role to retrieve(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        $myAgent = Get-SCArchiverAgents $myArc.Id
        Get-SCArchiverAgent $myAgent[0]

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AgentId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AgentId
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsaa Get-SCArchiverAgents
Function Get-SCArchiverAgents {
    <#
    .Synopsis
        This method will return all the agents that an archiver role has configured.
    .DESCRIPTION
        This method is used to return all the agents that the archiver roles has configured.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver role to retrieve the agents from(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        Get-SCArchiverAgents $myArc.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "agents" 
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsae Get-SCArchiverExtensions
Function Get-SCArchiverExtensions {
    <#
    .Synopsis
        This method will return all the camera extension that an archiver role has configured.
    .DESCRIPTION
        This method is used to return all the camera extension that an archiver roles has configured.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver role to retrieve the extensions from(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        Get-SCArchiverExtensions $myArc.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "Extensions" 
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsaadg Get-SCArchiverAgentDiskGroup
Function Get-SCArchiverAgentDiskGroup {
    <#
    .Synopsis
        This method will return all the disk group that an archiver agent has configured.
    .DESCRIPTION
        This method is used to return all the disk groups that an archiver agent has configured.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The ArchiverAgentId parameter represents the Id of the archiver agent to retrieve the disk group from(The guid representing the agent in the Security Center System).
        You can also pass any archiving agent object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        $myAgent = Get-SCArchiverAgents $myArc.Id
        Get-SCArchiverAgentDiskGroup $myAgent.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ArchiverAgentId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $ArchiverAgentId -RelationName "DriveGroups"  
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsaas Get-SCArchiverAgentStats
Function Get-SCArchiverAgentStats {
    <#
    .Synopsis
        This method will return the archiver agent statistics.
    .DESCRIPTION
        This method is used to return the statistics of archiver agent.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The ArchiverAgentId parameter represents the Id of the archiver agent to retrieve the statistics from(The guid representing the agent in the Security Center System).
        You can also pass any archiving agent object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver
        $myAgent = Get-SCArchiverAgents $myArc.Id
        Get-SCArchiverAgentStats $myAgent.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ArchiverAgentId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $ArchiverAgentId -RelationName "ArchiverStats"  
        }
    }   
}

#------------------------------------------------------------------------------
Set-Alias isamavu Invoke-SCArchiverMoveAllVideoUnit
Function Invoke-SCArchiverMoveAllVideoUnit {
	<#
	.Synopsis
		This method will move all video units from a source archiver to a destination archiver.
	.DESCRIPTION
		This method is called when the user wants to move all the video units from one archiver to another archiver.

		Note: We must move each video unit from the source archiver to the destination archiver one by one so that the destination archiver can set the running state of
		the video unit to active after successfully it has been successfully moved.

		Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.
		
		The ArchiverId parameter represents the id of the archiver to move the video units from.

		The DestinationId parameter represents the id of the archiver to move the video units to.
	.EXAMPLE
		# Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""
		
		Invoke-SCArchiverMoveAllVideoUnit -SourceId "70940ac8-b348-44bf-9c5c-9710c8e868ce" -DestinationId "16794100-669c-4aa7-8b06-15b1b5278ae1"

	    #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
	#>
	[CmdletBinding()]
	param(
		[parameter(Mandatory=$true,Position=0)] [alias("Id")] $ArchiverId,
		[parameter(Mandatory=$true,Position=1)] [alias("dest") ] $DestinationId
	)
	Begin {
	}

	Process{
		SCCmdletImplementation $MyInvocation.InvocationName {
            $arcId = GetIdFromObject $ArchiverId
            $destId = GetIdFromObject $DestinationId
			$videoUnits = InvokeSCRestMethod -Method 'GET' -UriSuffix "entities/$arcId/VideoUnits"

			ForEach($unit in $videoUnits){
				$videoUnitId = GetIdFromObject $unit

			    $uri = "entities/$videoUnitId/MoveUnit"
			    
			    $jsonObject = [ordered]@{}
			    $jsonObject.Add("Id", $destId)
			    $jsonBody = $jsonObject | ConvertTo-Json
			    
			    InvokeSCRestMethod -Method 'POST' -UriSuffix $uri -Body $jsonBody	
			}
		}
	}
}

# -----------------------------------------------------------------------------
Set-Alias nsaadb New-SCArchiverAgentDatabase
Function New-SCArchiverAgentDatabase {
    <#
    .Synopsis
        This method will create a new database for the given Archiver Agent.
    .DESCRIPTION
        This method will create a new database for the given Archiver Agent.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete .

        The ArchiverAgentId parameter represents the Id of the Archiver Agent to create the database on (The guid representing the agent in the Security Center System).
        You can also pass any Archiver Agent object that contains an ID as a parameter.

        The DatabaseName parameter will be the name given to the new database.

        The DatabaseFolder parameter (optional, empty string by default, will resolve to SQl folder) is the folder where the database is created.

        The OverwriteDatabase parameter (optional, false by default) will force an overwrite of the database if one with the same name exists.

        The DatabaseServer parameter (optional, will be local machine if not specified) is the server that hosts the database.

        The Username parameter (optional, will use default windows credentials if not specified) is the server username, intended if the role is configured with a Microsoft Azure database.

        The Password parameter (optional, will use default windows credentials if not specified) is the server password, intended if the role is configured with a Microsoft Azure database.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver
        $myAgent = Get-SCArchiverAgents $myArc.Id

        New-SCArchiverAgentDatabase $myAgent -DatabaseName "testDB"

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $ArchiverAgentId,
        [parameter(Mandatory=$true)] [alias("dbn")][string]$DatabaseName = "",
        [parameter(Mandatory=$false)] [alias("dbf")][string]$DatabaseFolder = "",
        [parameter(Mandatory=$false)] [alias("odb")] [bool]$OverwriteDatabase=$false,
        [parameter(Mandatory=$false)] [alias("dbs")][string]$DatabaseServer = "",
		[parameter(Mandatory=$false)] [alias("dbun")][string]$Username = "",
		[parameter(Mandatory=$false)] [alias("dbp")][string]$Password = ""
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $rid = GetIdFromObject $ArchiverAgentId
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Database", $DatabaseName)
        $jsonObject.Add("Folder", $DatabaseFolder)
        $jsonObject.Add("Overwrite", $OverwriteDatabase)
        $jsonObject.Add("Server", $DatabaseServer)
        $jsonObject.Add("Username", $Username)
        $jsonObject.Add("Password", $Password)
        $jsonBody = $jsonObject | ConvertTo-Json

        $uri = "Entities/$rid/CreateDatabase"
        InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias nsaadg New-SCArchiverAgentDiskGroup
Function New-SCArchiverAgentDiskGroup {
    <#
    .Synopsis
        This method will create a new disk group configuration for an archiving agent.
    .DESCRIPTION
        This method is used to create a new disk group configuration for an archiving agent.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The ArchiverAgentId parameter represents the Id of the archiver agent to set the disk group to(The guid representing the agent in the Security Center System).
        You can also pass any archiving agent object that contains an ID as a parameter.

        The Name parameter is the object containing the name of the disk group to create.

        The NetworkPath parameter is the object containing the complete network path of the disk group.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver
        $myAgent = Get-SCArchiverAgents $myArc.Id
        $dg = New-SCArchiverAgentDiskGroup $myAgent.Id -Name "MyDrive" -NetworkPath \\MyPC\MyFolder\

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ArchiverAgentId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("n")] $Name,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("np")] $NetworkPath
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $id = GetIdFromObject $ArchiverAgentId
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Name", $Name)

            $jbody = ConvertTo-Json $jsonObject -Depth 3 -Compress
    
            $dgid = InvokeSCRestMethod -Method 'POST' -UriSuffix "Entities/$id/DriveGroups/" -Body $jbody

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", $dgid.Id)
            $jsonObject.Add("Name", $Name)

            $drives=@()
            $drive=@{}
            $drive.add("MinDriveSpace", 2048)
            $drive.add("Enabled", $true)
            $drive.add("Local", $false)
            $drive.add("FilePath", $NetworkPath)
            $drive.add("DestinationDirectoryName", "VideoArchives")
            $drives += $drive    
            $jsonObject.Add("Drives", $drives) 

            Set-SCArchiverAgentDiskGroup $id -DiskGroup $jsonObject
            $dgid
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias rsaadb Remove-SCArchiverAgentDatabase
Function Remove-SCArchiverAgentDatabase {
    <#
    .Synopsis
        This method will delete the database of the given Archiver Agent.
    .DESCRIPTION
        This method will delete the database of the given Archiver Agent.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The ArchiverAgentId parameter represents the Id of the Archiver Agent to remove the database from (The guid representing the role in the Security Center System).
        You can also pass any Archiver Agent object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver
        $myAgent = Get-SCArchiverAgents $myArc.Id

        Remove-SCArchiverAgentDatabase $myAgent

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $ArchiverAgentId
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $rid = GetIdFromObject $ArchiverAgentId
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Id", "")
        $jsonBody = $jsonObject | ConvertTo-Json
        $uri = "Entities/$rid/DeleteDatabase"
        InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias rsaadg Remove-SCArchiverAgentDiskGroup
Function Remove-SCArchiverAgentDiskGroup {
    <#
    .Synopsis
        This method will remove a disk group configuration for an archiving agent.
    .DESCRIPTION
        This method is used to remove a  disk group configuration from an archiving agent.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete .

        The ArchiverAgentId parameter represents the Id of the archiver agent to remove the disk group from(The guid representing the agent in the Security Center System).
        You can also pass any archiving agent object that contains an ID as a parameter.

        The DiskGroupId parameter is the id of the diskgroup to remove.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver
        $myAgent = Get-SCArchiverAgents $myArc.Id
        $dg = New-SCArchiverAgentDiskGroup $myAgent.Id -Name "MyDrive" -NetworkPath \\MyPC\MyFolder\

        Remove-SCArchiverAgentDiskGroup $myAgent.Id -DiskGroupId $dg.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ArchiverAgentId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("dgid")] $DiskGroupId
    )
 
    Begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntityRelation -EntityId $ArchiverAgentId -RelationName "DriveGroups" -RelationId $DiskGroupId  
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias rsaa Remove-SCArchiverAgent
Function Remove-SCArchiverAgent {
    <#
    .Synopsis
        This method will remove an archiver agent from an archiver role.
    .DESCRIPTION
        This method is used when you want to remove an archiver agent from a archiver role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter RoleId is used to specify the archiver role you want to remove the archiver agent from
        
        The parameter ServerId is used to specify the server where the agent to remove is running on.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myArc = Get-SCRoles -Type Archiver | Get-SCRole

        $serverToAdd = Get-SCEntities -Type Servers

        #make sure to select the server entity that you need to add to the archiver
        Remove-SCArchiverAgent -Id $myArc -ServerId $serverToAdd[0]
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("sid")] $ServerId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $sid = GetIdFromObject $ServerId
            $uri = "Entities/$rid/Agents/$sid"
            InvokeSCRestMethod -UriSuffix $uri -Method "DELETE"
        }
    }    
}

# -----------------------------------------------------------------------------
Set-Alias resaadb Restore-SCArchiverAgentDatabase
Function Restore-SCArchiverAgentDatabase {
    <#
    .Synopsis
        This method will restore a given database on the selected Archiver Agent.
    .DESCRIPTION
        This method will restore a database on the given Archiver Agent.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete .

        The ArchiverAgentId parameter represents the Id of the Archiver Agent to restore the db to (The guid representing the agent in the Security Center System).
        You can also pass any Archiver Agent object that contains an ID as a parameter.

        The parameter BackupFileName represents the full path and backup file name used for the restore operation.  This path must be accesible from the archiver agent
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver
        $myAgent = Get-SCArchiverAgents $myArc.Id
         
        Restore-SCArchiverAgentDatabase $myAgent -BackupFileName 'C:\SecurityCenterBackup\Archiver_ManualBackup_2017-08-17_11h21min14s.bak'

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $ArchiverAgentId,
        [parameter(Mandatory=$true)] [alias("bfn")][string]$BackupFileName
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $ArchiverAgentId
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("File", $BackupFileName)
            $jsonBody = $jsonObject | ConvertTo-Json

            $uri = "Entities/$rid/RestoreDatabaseBackup"
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssaa Set-SCArchiverAgent
Function Set-SCArchiverAgent() {
    <#
    .Synopsis
        Used to update the properties of an archiver agent in Security Center.
    .DESCRIPTION
        This method is used to update the properties of an archiver agent to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter ArchiverAgent represents and contains the properties that will be updated to security Center.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        $agents = Get-SCArchiverAgents $myArc.Id
        $myAgent = Get-SCEntity $agents[0].Id
        $myAgent.RtspPort = 556
        Set-SCArchiverAgent $myAgent

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("aa")] $ArchiverAgent
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $ArchiverAgent
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssaadg Set-SCArchiverAgentDiskGroup
Function Set-SCArchiverAgentDiskGroup {
    <#
    .Synopsis
        This method will set the disk group config for an archiver agent.
    .DESCRIPTION
        This method is used to set the disk group config for an archiver agent.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The ArchiverAgentId parameter represents the Id of the archiver agent to set the disk group configuration on(The guid representing the agent in the Security Center System).
        You can also pass any archiving agent object that contains an ID as a parameter.

        The DiskGroup parameter is the object containing the disk group configuration.  It can be retreived using the Get-SCArchivingAgentDiskGroup method.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver
        $myAgent = Get-SCArchiverAgents $myArc.Id
        $dg = Get-SCArchiverAgentDiskGroup $myAgent.Id
        $dg[0].Drives[0].Enabled = $true

        Set-SCArchiverAgentDiskGroup $myArc.Id -DiskGroup $dg[0]

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ArchiverAgentId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("dg")] $DiskGroup
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $id = GetIdFromObject $ArchiverAgentId
            $dgid = GetIdFromObject $DiskGroup
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Name", $DiskGroup.Name)

            $drives=@()

            foreach ($diskInfo in $DiskGroup.Drives) 
            {
                $drive=@{}
                $drive.add("MinDriveSpace", $diskInfo.MinDriveSpace)
                $drive.add("Enabled", $diskInfo.Enabled)
                $drive.add("Local", $diskInfo.Local)
                $drive.add("FilePath", $diskInfo.FilePath)
                $drive.add("DestinationDirectoryName", $diskInfo.DestinationDirectoryName)

                $drives += $drive
            }
    
            $jsonObject.Add("Drives", $drives) 

            $jbody = ConvertTo-Json $jsonObject -Depth 3 -Compress
    
            InvokeSCRestMethod -Method 'PUT' -UriSuffix "Entities/$id/DriveGroups/$dgid" -Body $jbody
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias ssarm Set-SCArchiverRecordingModes
Function Set-SCArchiverRecordingModes {
    <#
    .Synopsis
        This method will set the archiver recording modes that the archiver has configured.
    .DESCRIPTION
        This method is used to set the archiver recording modes of the archiver.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The RoleId parameter represents the Id of the archiver to set the recording modes to(The guid representing the archiver in the Security Center System).
        You can also pass any archiver role object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        Set-SCArchiverRecordingModes $myArc.Id -RecModeId "0" -RecordingMode OnMotionOrManual

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("rmId")] $RecModeId
    )

    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'RecordingMode'
        $ParameterAlias = 'rm'
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $false
        $ParameterAttribute.Position = 2

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        # Generate and set the ValidateSet 
            
        $arrSet = 'Continuous', 'Manual', 'Off', 'OnMotionOrManual'
        $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

        # Add the ValidateSet to the attributes collection
        $AttributeCollection.Add($ValidateSetAttribute)

         #add the alias to the attributes collection
        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)
        return $RuntimeParameterDictionary
    }
 
    Begin {
    }

    Process {
        $RecordingMode = $PsBoundParameters[$ParameterName]

        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/RecordingModes/$RecModeId"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Mode", $RecordingMode)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Put" -Body $jsonBody 
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias ssatp Set-SCArchiverTelnetPassword
Function Set-SCArchiverTelnetPassword() {
    <#
    .Synopsis
        Used to update the telnet password of an archiver in Security Center.
    .DESCRIPTION
        This method is used to update the telnet password of an archiver to Security Center (for administrators only).
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter RoleId represents the archiver id of the archiver where the telnet password will be updated in security Center.

        The parameter TelnetPassword is used to specify the new telnet password to update.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newa = Get-SCRoles -Type Archiver

        Set-SCArchiverTelnetPassword -ArchiverId $newa -TelnetPassword "pass"

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("tp")] [string]$TelnetPassword
    )

    SCCmdletImplementation $MyInvocation.InvocationName { 
        #special case for user password, it will be filtered out of the generic Set-User method
        $aid = GetIdFromObject $RoleId
        $uri = "Entities/$aid/" 

        $jsonObject = [ordered]@{} 
        $jsonObject.Add("TelnetPassword", $TelnetPassword)
        $jsonBody = $jsonObject | ConvertTo-Json

        InvokeSCRestMethod -UriSuffix $uri -Method 'PUT' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssap Show-SCArchiverProperties
Function Show-SCArchiverProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an archiver role.
    .DESCRIPTION
        This method will list the supported properties and relation of an archiver role (the data model, not the actual data).  This method is used
        when you want to know what is available for a given archiver role.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCArchiverProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiArchiverRole" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}

# -----------------------------------------------------------------------------
Set-Alias usaadb Update-SCArchiverAgentDatabase
Function Update-SCArchiverAgentDatabase{ 
    <#
    .Synopsis
        This method will update a database for the given Archiver agent if needed.
    .DESCRIPTION
        This method will update the database for the given archiver agent.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The ArchiverAgentId parameter represents the Id of the archiver agent to update the DB (The guid representing the agent in the Security Center System).
        You can also pass any Archiver Agent object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver
        $myAgent = Get-SCArchiverAgents $myArc.Id

        Update-SCArchiverAgentDatabase $myAgent

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $ArchiverAgentId
    )
    SCCmdletImplementation $MyInvocation.InvocationName {
        $rid = GetIdFromObject $ArchiverAgentId
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Id", "")
        $jsonBody = $jsonObject | ConvertTo-Json

        $uri = "Entities/$rid/UpgradeDatabase"
        InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias nsaadb Set-SCArchiverAgentDatabase
Function Set-SCArchiverAgentDatabase {
    <#
    .Synopsis
        This method will set a new database configuration for the given Archiver Agent.
    .DESCRIPTION
        This method will set a new database configuration for the given Archiver Agent.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete .

        The ArchiverAgentId parameter represents the Id of the Archiver Agent to set the database on (The guid representing the agent in the Security Center System).
        You can also pass any Archiver Agent object that contains an ID as a parameter.

        The DatabaseName parameter will be the name given to the new database.

        The DatabaseServer parameter (optional, will be local machine if not specified) is the server that hosts the database.

        The Username parameter (optional, will use default windows credentials if not specified) is the server username, intended if the role is configured with a Microsoft Azure database.

        The Password parameter (optional, will use default windows credentials if not specified) is the server password, intended if the role is configured with a Microsoft Azure database.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver
        $myAgent = Get-SCArchiverAgents $myArc.Id

        Set-SCArchiverAgentDatabase $myAgent -DatabaseName "testDB"

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $ArchiverAgentId,
        [parameter(Mandatory=$true)] [alias("dbn")][string]$DatabaseName = "",
        [parameter(Mandatory=$false)] [alias("dbs")][string]$DatabaseServer = "",
	    [parameter(Mandatory=$false)] [alias("dbun")][string]$Username = "",
	    [parameter(Mandatory=$false)] [alias("dbp")][string]$Password = ""
    )

    SCCmdletImplementation $MyInvocation.InvocationName {
        $rid = GetIdFromObject $ArchiverAgentId
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Database", $DatabaseName)
        $jsonObject.Add("Server", $DatabaseServer)
        $jsonObject.Add("Username", $Username)
        $jsonObject.Add("Password", $Password)
        $jsonBody = $jsonObject | ConvertTo-Json

        $uri = "Entities/$rid/SetDatabase"
        InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias gsaras Get-SCArchiverRoleExtensionsAdvancedSettings
Function Get-SCArchiverRoleExtensionsAdvancedSettings {
    <#
    .Synopsis
        This method will return the Extensions advanced settings of the archiver
    .DESCRIPTION
        This method is used to return the advanced setting of the archiver
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver role to retrieve the settings from(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter
        The ExtID parameter represents the Id of the extension you want to retrieve the Extensions advanced settings from.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        $ext = gsae b6ace764-1d5f-4dbc-bf1f-c9fff8228a34
        Get-SCArchiverRoleExtensionsAdvancedSettings $myArc.Id $ext[0].Id 

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("RId")] $RoleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("EId")] $ExtId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $eid = GetIdFromObject $ExtId
            $uri = "Entities/$rid/Extensions/$eid/AdvancedSettings"
            InvokeSCRestMethod -UriSuffix $uri -Method "GET" 
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias usaras Update-SCArchiverRoleExtensionsAdvancedSettings
Function Update-SCArchiverRoleExtensionsAdvancedSettings {
    <#
    .Synopsis
        This method will allow to Update a single setting in the Extensions advanced settings of the archiver 
    .DESCRIPTION
        This method is used  to Update a single setting in the Extensions advanced settings of the archiver 
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver role to retrieve the settings from(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter
        The ExtID parameter represents the Id of the extension you want to Update the settings from.
        The name parameter represents the name in the Extensions advanced settings you want to Update the value. This value must exist already
        The value parameter represents the value you want to Update
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        $ext = gsae b6ace764-1d5f-4dbc-bf1f-c9fff8228a34
        Update-SCArchiverRoleExtensionsAdvancedSettings $myArc.Id $ext[0].Id "Name" 42

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("RId")] $RoleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("EId")] $ExtId,
        [parameter(Mandatory=$true,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("nme")] $name,
        [parameter(Mandatory=$true,Position=3,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("val")] $value

    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $eid = GetIdFromObject $ExtId
            $uri = "Entities/$rid/Extensions/$eid/AdvancedSettings"
            if($name)
            {

                $jsonObject = [ordered]@{} 
                $jsonObject.Add("Name", "$name")
                $jsonObject.Add("Value", $value)
                $jsonBody = $jsonObject | ConvertTo-Json
                
                InvokeSCRestMethod -UriSuffix $uri -Method "PUT" -Body $jsonBody
            }
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias asaras Add-SCArchiverRoleExtensionsAdvancedSettings
Function Add-SCArchiverRoleExtensionsAdvancedSettings{
    <#
    .Synopsis
        This method will allow to add a single setting in the Extensions advanced settings of the archiver 
    .DESCRIPTION
        This method is used  to add a single setting in the Extensions advanced settings of the archiver 
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver role to retrieve the settings from(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter
        The ExtID parameter represents the Id of the extension you want to Add the settings to.
        The name parameter represents the name in the Extensions advanced settings you want to Add.
        The value parameter represents the value you want to Add
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        $ext = gsae b6ace764-1d5f-4dbc-bf1f-c9fff8228a34
        Add-SCArchiverRoleAdvancedSetting $myArc.Id $ext[0].Id "Name" 42

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("RId")] $RoleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("EId")] $ExtId,
        [parameter(Mandatory=$true,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("nme")] $name,
        [parameter(Mandatory=$true,Position=3,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("val")] $value

    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $eid = GetIdFromObject $ExtId
            $uri = "Entities/$rid/Extensions/$eid/AdvancedSettings"
            if($name)
            {
                $jsonObject = [ordered]@{} 
                $jsonObject.Add("Name", "$name")
                $jsonObject.Add("Value", $value)
                $jsonBody = $jsonObject | ConvertTo-Json
                
                InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
            }
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias rsaras Remove-SCArchiverRoleExtensionsAdvancedSettings
Function Remove-SCArchiverRoleExtensionsAdvancedSettings {
    <#
    .Synopsis
        This method will allow to Remove a single setting in the Extensions advanced settings of the archiver 
    .DESCRIPTION
        This method is used  to Remove a single setting in the Extensions advanced settings of the archiver 
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver role to retrieve the settings from(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter
        The ExtID parameter represents the Id of the extension you want to Remove the settings from.
        The name parameter represents the name in the Extensions advanced settings you want to Remove the value. This value must exist already
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        $ext = gsae b6ace764-1d5f-4dbc-bf1f-c9fff8228a34
        Remove-SCArchiverRoleExtensionsAdvancedSettings $myArc.Id $ext[0].Id "Name"

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("RId")] $RoleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("EId")] $ExtId,
        [parameter(Mandatory=$true,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("nme")] $name

    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $eid = GetIdFromObject $ExtId
            $uri = "Entities/$rid/Extensions/$eid/AdvancedSettings/$name"
                InvokeSCRestMethod -UriSuffix $uri -Method "DELETE"
        }
    }   
}


# -----------------------------------------------------------------------------
Set-Alias gsaas Get-SCArchiverAdvancedSettings
Function Get-SCArchiverAdvancedSettings {
    <#
    .Synopsis
        This method will return the advanced settings of the archiver
    .DESCRIPTION
        This method is used to return the advanced setting of the archiver
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver role to retrieve the settings from(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver
        Get-SCArchiverRoleAdvancedSettings $myArc.Id  

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("RId")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/AdvancedSettings"
            InvokeSCRestMethod -UriSuffix $uri -Method "GET" 
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias usaas Update-SCArchiverAdvancedSettings
Function Update-SCArchiverAdvancedSettings {
    <#
    .Synopsis
        This method will allow to Update a single setting in the advanced settings of the archiver 
    .DESCRIPTION
        This method is used  to Update a single setting in the advanced settings of the archiver 
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver role to retrieve the settings from(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter
        The name parameter represents the name in the advanced settings you want to Update the value. This value must exist already
        The value parameter represents the value you want to Update
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        $ext = gsae b6ace764-1d5f-4dbc-bf1f-c9fff8228a34
        Update-SCArchiverRoleAdvancedSettings $myArc.Id "Name" 42

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("RId")] $RoleId,
        [parameter(Mandatory=$true,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("nme")] $name,
        [parameter(Mandatory=$true,Position=3,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("val")] $value

    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/advancedsettings/$name"
            if($name)
            {

                $jsonObject = [ordered]@{} 
                $jsonObject.Add("Name", "$name")
                $jsonObject.Add("Value", $value)
                $jsonBody = $jsonObject | ConvertTo-Json
                
                InvokeSCRestMethod -UriSuffix $uri -Method "PUT" -Body $jsonBody
            }
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias asaas Add-SCArchiverAdvancedSettings
Function Add-SCArchiverAdvancedSettings{
    <#
    .Synopsis
        This method will allow to add a single setting in the advanced settings of the archiver 
    .DESCRIPTION
        This method is used  to add a single setting in the advanced settings of the archiver 
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver role to retrieve the settings from(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter
        The name parameter represents the name in the advanced settings you want to Add.
        The value parameter represents the value you want to Add
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        Add-SCArchiverRoleAdvancedSetting $myArc.Id $ext[0].Id "Name" 42

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("RId")] $RoleId,
        [parameter(Mandatory=$true,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("nme")] $name,
        [parameter(Mandatory=$true,Position=3,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("val")] $value

    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/advancedsettings"
            if($name)
            {
                $jsonObject = [ordered]@{} 
                $jsonObject.Add("Name", "$name")
                $jsonObject.Add("Value", $value)
                $jsonBody = $jsonObject | ConvertTo-Json
                
                InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
            }
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias rsaas Remove-SCArchiverAdvancedSettings
Function Remove-SCArchiverAdvancedSettings {
    <#
    .Synopsis
        This method will allow to Remove a single setting in the advanced settings of the archiver 
    .DESCRIPTION
        This method is used  to Remove a single setting in the advanced settings of the archiver 
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the archiver role to retrieve the settings from(The guid representing the role in the Security Center System)
        You can also pass any archiving role object that contains an ID as a parameter
        The name parameter represents the name in the advanced settings you want to Remove the value. This value must exist already
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type Archiver 
        Remove-SCArchiverRoleAdvancedSettings $myArc.Id "Name"

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("RId")] $RoleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("nme")] $name

    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/advancedsettings/$name"
                InvokeSCRestMethod -UriSuffix $uri -Method "DELETE"
        }
    }   
}

Export-ModuleMember -Function '*-*' -Alias '*'
