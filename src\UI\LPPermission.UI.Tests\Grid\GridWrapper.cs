using LPPermission.UI.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Rendering;

namespace LPPermission.UI.Tests.Grid
{
    public class GridWrapper : ComponentBase
    {
        [Parameter] public List<PrivilegeDto> Privileges { get; set; } = new List<PrivilegeDto>();
        [Inject] private IServiceProvider ServiceProvider { get; set; }
        protected override void BuildRenderTree(RenderTreeBuilder builder)
        {
            builder.OpenComponent(0, typeof(UI.Pages.Grid));
            builder.AddAttribute(1, nameof(UI.Pages.Grid.Privileges), Privileges);
            builder.AddAttribute(2, "ServiceProvider", ServiceProvider);
            builder.CloseComponent();
        }
    }
}
