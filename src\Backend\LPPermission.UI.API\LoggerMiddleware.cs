﻿using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.Extensions.Logging;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.Azure.Functions.Worker;

namespace LPPermission.UI.API
{
    /// <summary>
    /// Middleware for logging and tracking Azure Function executions.
    /// </summary>
    
    public class LoggerMiddleware : IFunctionsWorkerMiddleware
    {

        private readonly ILogger<LoggerMiddleware> _logger;
        
        public LoggerMiddleware()
        {

        }

        /// <summary>
        /// Constructor with dependency injection for ILogger.
        /// </summary>
        /// <param name="logger">Logger instance for logging information and errors.</param>
        public LoggerMiddleware(ILogger<LoggerMiddleware> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Middleware execution logic for logging and tracking function execution.
        /// </summary>
        /// <param name="context">The function execution context.</param>
        /// <param name="next">Delegate to invoke the next middleware or function.</param>
        /// <returns>A Task representing the asynchronous operation.</returns>
        public async Task Invoke(FunctionContext context, FunctionExecutionDelegate next)
        {
            var requestId = Guid.NewGuid().ToString();
            var startTime = DateTime.UtcNow;

            // Log the incoming request to Application Insights
            _logger.LogInformation($"Request {requestId}: Function {context.FunctionDefinition.Name} started at {DateTime.UtcNow}");

            // Track the request in Application Insights
            var requestTelemetry = new RequestTelemetry
            {
                Name = context.FunctionDefinition.Name,
                Timestamp = DateTime.UtcNow,
                Id = requestId,

            };
            try
            {
                await next(context); // Call the next middleware
            }
            catch (Exception ex)
            {
                // Log exception in Application Insights

                _logger.LogError(ex, $"Request {requestId}: Function {context.FunctionDefinition.Name} failed. {ex.Message}");
                var exceptionTelemetry = new ExceptionTelemetry(ex)
                {
                    Message = ex.Message
                };
                throw;
            }

            var duration = DateTime.UtcNow - startTime;
            // Log response status to Application Insights
            _logger.LogInformation($"Request {requestId}: Function {context.FunctionDefinition.Name} completed at {DateTime.UtcNow}");

            // Track the response in Application Insights
            requestTelemetry.Duration = duration;
            
        }
    }
}