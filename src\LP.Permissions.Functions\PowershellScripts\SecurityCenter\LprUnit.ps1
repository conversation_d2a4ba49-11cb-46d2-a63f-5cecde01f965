﻿# ==========================================================================
# Copyright (C) 1989-2018 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias aslu Add-SCLprUnit
Function Add-SCLprUnit {
    <#
    .Synopsis
        Method used to add an LPR Unit to an LPR role in Security Center using the unit assistant
    .DESCRIPTION
        This method is used to add an LPR unit to the provided LPR role in Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RoleId parameter represents the Id of the LPR Manager we want to add the unit to

	    The UnitName parameter represents the name of the LPR Unit we want to add

        The Address parameter represents the ip address of the LPR unit we want to add

        The Port parameter represents the port of the LPR unit we want to add

        The Username parameter reprents the username to use when connecting to the LPR unit

        The Password parameter reprents the password to use when connecting to the LPR unit

    .EXAMPLE
		# Call the following in order to import all the modules. Otherwise, Enter-SCSession will not work.
        # Import-Module ".\SDK\REST\Powershell Scripts\Modules\SecurityCenter\SecurityCenter.psm1" -Force
        # Import-Module .\SecurityCenter.psm1 -Force
        
		# Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

		$result = Add-SCLprUnit -RoleId 6ff73fd4-ddf3-4bba-8da9-ce339f2c230c -UnitName "simulatedSharp10usingRest" -Address "127.0.0.1" -Port 42671 -Username "admin" -Password "Genetec"

		$UnitAssistant = Get-SCRoles -t UnitAssistant -f Base

        Exit-SCSession

    .NOTES
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""
        $result = Add-SCLprUnit -RoleId 6ff73fd4-ddf3-4bba-8da9-ce339f2c230c -UnitName "simulatedSharp10usingRest" -Address "127.0.0.1" -Port 42671 -Username "admin" -Password "Genetec"
        Exit-SCSession
        #Exit the session
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
	[parameter(Mandatory=$true,Position=1)] [alias("Id")] $RoleId,
	[parameter(Mandatory=$true,Position=2)] [alias("name")] [string]$UnitName,
	[parameter(Mandatory=$true,Position=3)] [alias("addr")][string]$Address,
	[parameter(Mandatory=$true,Position=4)] [alias("p")][int]$Port,
	[parameter(Mandatory=$true,Position=5)] [alias("un")] [string]$Username,
	[parameter(Mandatory=$true,Position=6)] [alias("pwd")] [string]$Password
	)
 
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $roleguid = GetIdFromObject $RoleId
            
            Write-Host $roleguid


            $sess = GetSession
            $uri = "Entities/LprUnits/EnrollLprUnit"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Role", $roleguid)
			$jsonObject.Add("UnitName", $UnitName)
            $jsonObject.Add("Address", $Address)
			$jsonObject.Add("Port", $Port)
			$jsonObject.Add("Username", $Username)
            $jsonObject.Add("Password", $Password)
            $jsonBody = $jsonObject | ConvertTo-Json

            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

Set-Alias gslu Get-SCLprUnit
Function Get-SCLprUnit {
    <#
    .Synopsis
        This method will return all the properties of the LPR unit represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the LPR unit represented by the ID. For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The VideoUnitId parameter represents the Id of the LPR unit to retreive (The guid representing the LPR unit in the Security Center System)
        You can also pass any video unit object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Import-Module .\SecurityCenter.psm1 -Force

        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $LprUnits = Get-SCEntities -t LprUnits -f All

        Get-SCLprUnit -LprUnitId $LprUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $LprUnitId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $LprUnitId 
        }
    }         
}

# -----------------------------------------------------------------------------
Set-Alias rslu Remove-SCLprUnit
Function Remove-SCLprUnit {
    <#
    .Synopsis
        Method used to permanently remove the given LPR unit and it's related reads
    .DESCRIPTION
        This method is used to permanently remove the given LPR unit and the associated reads
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VideoUnitId will be used to specify the video unit we want to get the cameras from
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $LprUnits = Get-SCEntities -t LprUnits -f All

        Remove-SCLprUnit -LprUnitId $LprUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $LprUnitId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $LprUnitId
        }
    }
}

Set-Alias sslup Show-SCLprUnitProperties
Function Show-SCLprUnitProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an LPR unit
    .DESCRIPTION
        This method will list the supported properties and relation of an LPR unit (the data model, not the actual data). This method is used
        when you want to know what is available for a given LPR unit
    .EXAMPLE
        #
        Import-Module .\SecurityCenter.psm1 -Force

        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
 
        Show-SCLprUnitProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiLprUnit" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}



Export-ModuleMember -Function '*-*' -Alias '*'
