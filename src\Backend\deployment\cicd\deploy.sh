#!/bin/bash

###
### Get command line args
###
while [[ $# -gt 0 ]]; do
  case $1 in
  --name)
    FUNCTION_APP_NAME="$2"
    shift # past argument
    shift # past value
    ;;
  --zip)
    ZIP_FILE_PATH="$2"
    shift # past argument
    shift # past value
    ;;
  --env)
    ENVIRONMENT="$2"
    shift # past argument
    shift # past value
    ;;
  --geo)
    GEO="$2"
    shift # past argument
    shift # past value
    ;;
  --rg)
    RESOURCE_GROUP="$2"
    shift # past argument
    shift # past value
    ;;
  --conn)
    SERVICE_CONNECTION="$2"
    shift # past argument
    shift # past value
    ;;
  -* | --*)
    echo "Unknown option $1"
    exit 1
    ;;
  esac
done

deploy_functionapp_firewall() {
  local ipRestrictionAction=$1
  local scmRestrictionAction=$2

  # Update IP security restrictions for the function app
  az resource update --resource-group "$RESOURCE_GROUP" --name "$FUNCTION_APP_NAME" --resource-type "Microsoft.Web/sites" \
    --set properties.siteConfig.ipSecurityRestrictionsDefaultAction="$ipRestrictionAction"
  az resource update --resource-group "$RESOURCE_GROUP" --name "$FUNCTION_APP_NAME" --resource-type "Microsoft.Web/sites" \
    --set properties.siteConfig.scmIpSecurityRestrictionsDefaultAction="$ipRestrictionAction"
  az webapp config access-restriction set -g "$RESOURCE_GROUP" -n "$FUNCTION_APP_NAME" --use-same-restrictions-for-scm-site "$scmRestrictionAction"
}

echo "Running deploy script with the following parameters:"
echo "FUNCTION_APP_NAME  = ${FUNCTION_APP_NAME}"
echo "ZIP_FILE_PATH      = ${ZIP_FILE_PATH}"
echo "ENVIRONMENT        = ${ENVIRONMENT}"
echo "GEO                = ${GEO}"
echo "RESOURCE_GROUP     = ${RESOURCE_GROUP}"
echo "SERVICE_CONNECTION = ${SERVICE_CONNECTION}"

# Update IP security restrictions to Allow
deploy_functionapp_firewall "Allow" false

echo "Deploying to $GEO: $FUNCTION_APP_NAME"

set +e # Disable exit on error

# Deploy the function from the zip file provided
az functionapp deployment source config-zip --resource-group "$RESOURCE_GROUP" --name "$FUNCTION_APP_NAME" --src "$ZIP_FILE_PATH"

deploy_functionapp_firewall "Deny" true


# Check the exit status of the last command
if [ $? -ne 0 ]; then
  echo "Deployment failed for $GEO: $FUNCTION_APP_NAME"
  exit 1
else
  echo "Deployment successful for $GEO: $FUNCTION_APP_NAME"
  exit 0
fi

#Update IP security restrictions to Deny
