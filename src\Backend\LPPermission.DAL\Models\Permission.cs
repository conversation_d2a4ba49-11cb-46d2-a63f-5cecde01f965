﻿using System;
using System.Collections.Generic;

namespace LPPermission.DAL.Models;

public partial class Permission
{
    public int PermissionId { get; set; }

    public int PrivilegeId { get; set; }

    public int UserGroupId { get; set; }

    public int? StateId { get; set; }

    public int? ContainerId { get; set; }

    public int? GeoId { get; set; }

    public string? Createby { get; set; }

    public DateTime? Createdate { get; set; }

    public string? Updateby { get; set; }

    public DateTime? Updatedate { get; set; }

    public virtual Container? Container { get; set; }

    public virtual Geo? Geo { get; set; }

    public virtual Privilege Privilege { get; set; } = null!;

    public virtual State? State { get; set; }

    public virtual UserGroup UserGroup { get; set; } = null!;
}
