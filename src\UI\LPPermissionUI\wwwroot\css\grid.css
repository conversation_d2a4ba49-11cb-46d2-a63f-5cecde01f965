﻿body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f7fb;
    color: #333;
    margin: 0;
    padding: 0;
}

.grid-page {
    background-color: #85151e;
    color: white;
    height: 100vh;
    padding: 20px;
}

.grid-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-top: 20px;
    color: #333;
}

.grid-header {
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: white;
    font-family: 'Arial', sans-serif;
}

.k-grid-header {
    background-color: #85151e;
    color: white;
    font-size: 12px;
}

.k-button {
    background-color: #85151e;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
}

    .k-button:hover {
        background-color: #6a0c12;
    }

.k-grid-content {
    font-size: 12px;
}
.dropdown-row {
    display: flex;
    gap: 24px;
    align-items: center;
    margin-bottom: 16px;
}

.dropdown-item {
    display: flex;
    flex-direction: column;
    min-width: 220px;
    width:60% !important

}

    .dropdown-item label {
        margin-bottom: 4px;
    }


.dropdown-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.k-form-label {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #2d3a4a;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #bfc9d1;
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    background: #f8fafc;
    transition: border-color 0.2s;
}

    .form-control:focus {
        border-color: #1976d2;
        outline: none;
    }
k-dropdownlist {
    width:50%
}
.k-dropdown .k-select,
.k-dropdownlist .k-select {
    display: flex;
    align-items: center;
    height: 100%;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.k-dropdown .k-icon,
.k-dropdownlist .k-icon {
    vertical-align: middle !important;
    align-self: center !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}