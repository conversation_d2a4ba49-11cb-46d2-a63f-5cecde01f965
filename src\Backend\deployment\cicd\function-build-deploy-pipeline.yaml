###
### This pipeline builds and deploys the LP PosLog Appriss Feeder function app
###

resources:
  repositories:
    - repository: cee-ado-core
      type: github
      endpoint: tjxinc-LPDevOps
      name: tjxinc/cee-ado-core
      # refs can be used to target a specific branch, tag, or commit
      # useful when making changes to cee-ado-core that have not been merged yet
      # ref: refs/heads/kylegibbons-CEETEAM-6379-lp-enable-scaling

variables:
  # User
  buildConfiguration : "Release"
  solution           : "src/Backend/LPPermission.sln"
  vmImageName        : "ubuntu-latest"
  vmWinImageName     : "windows-latest"
  dotnetVersion      : "8.x"

  # Computed
  ceewizclidecorator.skip: true # this pipeline does not deploy infrastructure, so Wiz is not needed
  outputDirectory        : "$(Build.ArtifactStagingDirectory)/" # Artifact staging
  csProject              : "src/Backend/LPPermission.UI.API/LPPermission.UI.API.csproj" # C# project file path
  NUGET_PACKAGES         : $(Pipeline.Workspace)/.nuget/packages

  # we need to reference the parameter file for the function app infrastructure so we can make changes
  # to the associated storage account
  ${{ if eq(parameters.environmentName, 'dev') }}:
    resourcegroupName      : "innovate-nprod-iac-devtest-eastus2-lp-perms-rg"
    networkResourceGroupName: "innovate-nprod-iac-devtest-eastus2-network-rg"
    serviceConnection      : "lp_perms_iac_connection-LPDevOps"
    SubscriptionID: "********-5e7b-4cd3-97a9-c72f31fa21e8"
    #functionappParameters: "$(Pipeline.Workspace)/source/deployment/env/parameters/nprod/eastus2/dev/vars.lp-functionapp-us.json"
    functionAppName      : "lppnpeus2iac01fa01"
    subnetName: "innovate-nprod-iac-devtest-eastus2-lp-perms-frontend-backend-subnet"
    vnetName: "innovate-nprod-iac-devtest-eastus2-vnet01"

  ${{ if eq(parameters.environmentName, 'qa') }}:
    resourcegroupName      : "stores-nprod-qa01-eastus2-lp-tf-rg"
    networkResourceGroupName: "stores-nprod-qa01-eastus2-lpnetwork-rg"
    serviceConnection      : "lp-tf-qa-connection"
    SubscriptionID: "********-30c2-4f86-8935-11a5b5bafad5"
    #functionappParameters: "$(Pipeline.Workspace)/source/deployment/env/parameters/nprod/eastus2/dev/vars.lp-functionapp-us.json"
    functionAppName      : "tfnpeus2qa01fa01edr"
    subnetName: "stores-nprod-qa01-eastus2-tf-edr-subnet01"
    vnetName: "stores-nprod-qa01-eastus2-vnet01"

  ${{ if eq(parameters.environmentName, 'prod') }}:
    resourcegroupName      : "stores-prod-prod01-eastus2-lp-tf-rg"
    networkResourceGroupName: "stores-prod-prod01-eastus2-lpnetwork-rg"
    serviceConnection      : "lp_tf_prod_connection"
    SubscriptionID: "47b0b253-353d-4a14-a80b-b4259dbb0f54"
    #functionappParameters: "$(Pipeline.Workspace)/source/deployment/env/parameters/nprod/eastus2/dev/vars.lp-functionapp-us.json"
    functionAppName      : "tfpeus2prod01fa01edr"
    subnetName: "stores-prod-prod01-eastus2-tf-edr-subnet"
    vnetName: "stores-prod-prod01-eastus2-vnet01"
    
# Trigger builds and deploys whenever these branches have new commits
#trigger:
#  - main
#  - qa

# PR builds whenever these branches have a PR opened or updated, but does not deploy
pr:
  autoCancel: true # if a PR is updated, cancel the build and build the new code instead
  drafts: false # do not run for draft PRs

parameters:
  # Azure environment to deploy into
  - name: environmentName
    displayName: Environment
    type: string
    default: dev
    values:
      - dev
      #- qa
      #- prod

  # Should we deploy the US FA?
  - name: usRegion
    displayName: Deploy Function App US
    type: boolean
    default: true

stages:
  - stage: build
    displayName: Build
    jobs:
      - job: Build
        displayName: Build Function App
        pool:
          vmImage: $(vmImageName)
        continueOnError: false
        steps:
          - checkout: self
            displayName: Check out source code
          - task: Cache@2
            displayName: Cache NuGet packages
            inputs:
              key: 'nuget | "$(Agent.OS)" | $(Build.SourcesDirectory)/**/packages.lock.json'
              restoreKeys: |
                nuget | "$(Agent.OS)"
                nuget
              path: $(NUGET_PACKAGES)
              cacheHitVar: "CACHE_RESTORED"

          # - task: DotNetCoreCLI@2
          #   displayName: Restore project dependencies
          #   condition: ne(variables.CACHE_RESTORED, true)
          #   inputs:
          #     version: "${{ variables.dotnetVersion }}"
          #     command: "restore"
          #     projects: "$(solution)"
          #     verbosityRestore: "Normal"
          #     restoreDirectory: $(NUGET_PACKAGES)

          - task: DotNetCoreCLI@2
            displayName: "Build the project - $(buildConfiguration)"
            inputs:
              version: "${{ variables.dotnetVersion }}"
              command: "build"
              arguments: "--configuration $(buildConfiguration)"
              projects: "$(solution)"
              restoreDirectory: $(NUGET_PACKAGES)

          - task: DotNetCoreCLI@2
            displayName: "Publish the project - $(buildConfiguration)"
            inputs:
              version: "${{ variables.dotnetVersion }}"
              command: "publish"
              projects: "$(csProject)"
              publishWebProjects: false
              arguments: "-o $(outputDirectory)/$(buildConfiguration) --no-restore"
              zipAfterPublish: true
              modifyOutputPath: true

          - task: PublishPipelineArtifact@1
            displayName: "Publish Build Artifact"
            inputs:
              targetPath: "$(Pipeline.Workspace)/a/${{ variables.buildConfiguration }}"
              publishLocation: "pipeline"
              # PathtoPublish: "$(build.artifactstagingdirectory)"
              ArtifactName: "app"

            # Publishing the source artifact allows us to keep a snapshot of the source that
            # was used in this build. It also allows following steps to get the artifact
            #  instead of having to checkout the source again.
          - task: PublishPipelineArtifact@1
            displayName: "Publish Source Artifact"
            inputs:
              targetPath: "$(Pipeline.Workspace)/s"
              publishLocation: "pipeline"
              # PathtoPublish: "$(build.artifactstagingdirectory)"
              ArtifactName: "source"

  

  ###
  ### STAGE: Check
  ###

  # - stage: check
  #   displayName: Check
  #   dependsOn: build
  #   condition: succeeded()
  #   jobs:
  #     - job: SonarQube
  #       displayName: Run SonarQube and Veracode Scan
  #       pool:
  #         vmImage: $(vmImageName)
  #       continueOnError: false
  #       steps:
  #         - download: current
  #           artifact: app
  #           displayName: Download source artifact

        # - task: AzureCLI@2
        #   displayName: Skipping Scans
        #   inputs:
        #     azureSubscription: ${{ variables.serviceConnection }}
        #     scriptType: 'bash' 
        #     scriptLocation: 'inlineScript' 
        #     inlineScript: |
        #       echo "Skipping Scans"

         # Uncomment below to enable scans 
          # - task: DownloadPipelineArtifact@2
          #   displayName: Download Build Artifact
          #   inputs:
          #     artifactName: "app"
          #     path: "$(System.ArtifactsDirectory)"

          # - task: DownloadPipelineArtifact@2
          #   displayName: Download source code
          #   inputs:
          #     artifactName: "source"
          #     path: "$(System.DefaultWorkingDirectory)"

          # - task: SonarQubePrepare@5
          #   displayName: SonarQube Preparation
          #   inputs:
          #     SonarQube: "lp_poslog_sq_connection"
          #     scannerMode: "MSBuild"
          #     projectKey: "LP-TF-DIS"
          #     projectName: "LP-TF-DIS"

          # - task: DotNetCoreCLI@2
          #   displayName: "Build the project - $(buildConfiguration)"
          #   inputs:
          #     version: "${{ variables.dotnetVersion }}"
          #     command: "build"
          #     arguments: "--configuration $(buildConfiguration)"
          #     projects: "$(solution)"
          #     restoreDirectory: $(NUGET_PACKAGES)

          # - task: SonarQubeAnalyze@5
          #   displayName: SonarQube Analysis
          #   inputs:
          #     jdkversion: "JAVA_HOME_17_X64"

          # - task: SonarQubePublish@5
          #   displayName: SonarQube Publish Report
          #   inputs:
          #     pollingTimeoutSec: "300"

          # - task: Veracode@3
          #   displayName: "Veracode Scan"
          #   inputs:
          #     ConnectionDetailsSelection: 'Service Connection'
          #     AnalysisService: 'lp_poslog_vc_connection'
          #     veracodeAppProfile: 'POSLog Transaction Finder'
          #     version: 'POSLog_TR_$(build.buildNumber)'
          #     filepath: '$(System.ArtifactsDirectory)'
          #     sandboxName: 'POSLog Transaction Finder Development Sandbox'
          #     createSandBox: false
          #     createProfile: false
          #     failBuildIfUploadAndScanBuildStepFails: true
          #     importResults: false
          #     failBuildOnPolicyFail: false
          #     maximumWaitTime: '360'
  
  ##
  ## STAGE: Prep
  ##

  - stage: prep
    displayName: Prep
    dependsOn: build
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest')) # For PRs, don't deploy
    jobs:
      - job: prep
        displayName: "Prepare to deploy"
        pool:
          vmImage: $(vmImageName)
        continueOnError: false
        steps:
          - checkout: cee-ado-core
            displayName: Checkout cee-ado-core
          - download: current
            artifact: source
            displayName: Download source artifact
          # - template: /src/cicd/templates/cee-azure-cli.yaml@cee-ado-core
          #   parameters:
          #     displayName: "Update Storage Network Settings"
          #     azureServiceConnection: ${{ variables.serviceConnection }}
          #     subscriptionId: ${{ variables.SubscriptionID }}
          #     scriptLocation: inlineScript
          #     inlineScript: |
          #       echo "Input file: $1"
          #       python ./functionapp_elastic_storage_network.py --parameters "$1" --allow
          #     #arguments: "${{ variables.functionappParameters }}"
          #     workingDirectory: "./src/services/scripts/appserviceplan"
          - task: AzureCLI@2
            displayName: Azure CLI - Add Microsoft Web Service Endopoint to subnets
            inputs:
              azureSubscription: ${{ variables.serviceConnection }}
              scriptType: "pscore"
              scriptLocation: inlineScript
              FailOnStandardError: true
              inlineScript: |
                az account set --subscription ${{ variables.SubscriptionID }}
                Write-Output "CLI set to scope: ${{ variables.SubscriptionID }}"
                az network vnet subnet update `
                  --name ${{ variables.subnetName }} `
                  --resource-group ${{ variables.networkResourceGroupName }} `
                  --vnet-name ${{ variables.vnetName }} `
                  --service-endpoints Microsoft.Web Microsoft.Storage Microsoft.KeyVault

  ###
  ### STAGE: Deploy
  ###

  - stage: deploy
    displayName: Deploy
    dependsOn: 
      # By putting both stages here, this will run only if the prep and check pass
      #- check
      - prep
    #condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest')) # For PRs, don't deploy
    jobs:
      - template: deploy-template.yaml
        parameters:
          name: ${{ variables.functionAppName }}
          environmentName: ${{ parameters.environmentName }}
          geo: "US"
          resourcegroupName: ${{ variables.resourcegroupName }}
          condition: ${{ parameters.usRegion }}
          outputDir: ${{ variables.outputDirectory }}
          buildConfiguration: ${{ variables.buildConfiguration }}
          serviceConnection: ${{ variables.serviceConnection }}

  - stage: post
    displayName: Cleanup
    dependsOn:
      # By putting both stages here, this will run even if the build fails
      # which ensures that the storage account firewall is set correctly.
      - prep
      - deploy
    condition: ne(variables['Build.Reason'], 'PullRequest') # For PRs, don't deploy
    jobs:
      - job: prep
        displayName: Cleanup after deployment
        pool:
          vmImage: $(vmImageName)
        continueOnError: false
        steps:
          - checkout: cee-ado-core
            displayName: Checkout cee-ado-core
          - download: current
            artifact: source
            displayName: Download Source Artifact
          # - template: src/cicd/templates/cee-azure-cli.yaml@cee-ado-core
          #   parameters:
          #     displayName: "Update Storage Network Settings"
          #     azureServiceConnection: ${{ variables.serviceConnection }}
          #     subscriptionId: ${{ variables.SubscriptionID }}
          #     scriptLocation: inlineScript
          #     inlineScript: |
          #       echo "Input file: $1"
          #       python ./functionapp_elastic_storage_network.py --parameters "$1"
          #     #arguments: "${{ variables.functionappParameters }}"
          #     workingDirectory: "./src/services/scripts/appserviceplan"
          - task: AzureCLI@2
            displayName: Azure CLI - Remove Microsoft Web Service Endopoint to subnets
            inputs:
              azureSubscription: ${{ variables.serviceConnection }}
              scriptType: "pscore"
              scriptLocation: inlineScript
              FailOnStandardError: true
              inlineScript: |
                az account set --subscription ${{ variables.SubscriptionID }}
                Write-Output "CLI set to scope: ${{ variables.SubscriptionID }}"
                az network vnet subnet update `
                  --name ${{ variables.subnetName }} `
                  --resource-group ${{ variables.networkResourceGroupName }} `
                  --vnet-name ${{ variables.vnetName }} `
                  --service-endpoints Microsoft.Storage Microsoft.KeyVault
               
