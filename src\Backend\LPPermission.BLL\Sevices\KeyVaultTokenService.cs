﻿//using Azure.Identity;
//using Azure.Security.KeyVault.Secrets;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.Logging;
//using Microsoft.Azure.Functions.Worker;
//using System;
//using System.Threading.Tasks;
//using LPPermission.BLL.Interfaces;

//public class KeyVaultTokenService : IKeyVaultTokenService
//{
//    private readonly IConfiguration _configuration;
//    private readonly SecretClient _secretClient;
//    private readonly ILogger<KeyVaultTokenService> _logger;

//    public KeyVaultTokenService(IConfiguration configuration, ILogger<KeyVaultTokenService> logger)
//    {
//        _configuration = configuration;
//        _logger = logger;

//        // Get Key Vault URI and Secret Name from configuration
//        var keyVaultUri = _configuration["AzureKeyVault:VaultUri"];
//        var secretName = _configuration["AzureKeyVault:SecretName"];

//        // Create SecretClient with DefaultAzureCredential (Managed Identity)
//        _secretClient = new SecretClient(new Uri(keyVaultUri), new DefaultAzureCredential());
//    }

//    public async Task<string> GetSecret()
//    {
//        // Read the secret name from configuration
//        string secretName = _configuration["AzureKeyVault:SecretName"]; // Now using configuration

//        try
//        {
//            // Access the secret from Azure Key Vault
//            KeyVaultSecret secret = await _secretClient.GetSecretAsync(secretName);

//            _logger.LogInformation($"Secret '{secretName}' retrieved successfully.");

//            // Return the secret value as part of the response
//            return secret.Value;
//        }
//        catch (Exception ex)
//        {
//            _logger.LogError($"Error retrieving secret: {ex.Message}");
//            return "There is no token";
//        }
//    }
//}
