﻿# ==========================================================================
# Copyright (C) 1989-2020 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias aacu Add-SCAccessControlUnit
Function Add-SCAccessControlUnit {
    <#
    .Synopsis
        This method will enroll an access control unit to the specified access manager.
    .DESCRIPTION
        This method will enroll an access control unit to the specified access manager.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The AccessManagerId parameter represents the Id of the access manager that should enroll the access control unit.

        The ExtensionId parameter represent the Id of the extension used for the enrollment.
        
        The Address parameter is the IP address\dns name of the unit to enroll. 

        The Username parameter is the username needed to login to the unit to enroll.

        The Password parameter is the password needed to login to the unit to enroll.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $synergisExtension = (get-scenum extensionIds) | where-object { $_.Name -eq "Synergis" }
        $accessManager = get-SCRoles -Type AccessManager

        Add-SCAccessControlUnit -AccessManagerId $($accessManager.Id) -ExtensionId $($synergisExtension.Id) -Address "vm646" -Username "admin" -Password "softwire"
        
        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0)] [alias("amId")] $AccessManagerId,
        [parameter(Mandatory=$true,Position=1)] [alias("extId")] $ExtensionId,
        [parameter(Mandatory=$true,Position=2)] [alias("add")] $Address,
        [parameter(Mandatory=$true,Position=3)] [alias("user")] $Username,
        [parameter(Mandatory=$true,Position=4)] [alias("psw")] $Password
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $amId = GetIdFromObject $AccessManagerId
            $extId = GetIdFromObject $ExtensionId

            $uri = "Entities/Units/EnrollAccessControlUnit"
         
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Role", "$amId")
            $jsonObject.Add("ExtensionId", "$extId")
            $jsonObject.Add("Address", "$Address")
            $jsonObject.Add("Username", "$Username")
            $jsonObject.Add("Password", "$Password")
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }       
}

# -----------------------------------------------------------------------------
Set-Alias gsacu Get-SCAccessControlUnit
Function Get-SCAccessControlUnit {
    <#
    .Synopsis
        This method will return all the properties of the access control unit represented by the ID.
    .DESCRIPTION
        This method will return all the properties of the access control unit represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The UnitId parameter represents the Id of the access control unit to retrieve (The guid representing the access control unit in the Security Center System)
        You can also pass any access control unit object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ACUnits = Get-SCEntities -t Units -f All

        Get-SCAccessControlUnit -UnitId $ACUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UnitId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $UnitId 
        }
    }       
}

# -----------------------------------------------------------------------------
Set-Alias gsacudv Get-SCAccessControlUnitDevices
Function Get-SCAccessControlUnitDevices {
    <#
    .Synopsis
        Method used to get all the devices of the given access control unit.
    .DESCRIPTION
        This Method will allow the user to get all the devices of the given access control unit.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter UnitId will be used to specify the access control unit we want to get the devices from.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ACUnits = Get-SCEntities -t Units -f All

        Get-SCAccessControlUnitDevices -UnitId $ACUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UnitId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $UnitId -RelationName Devices
        }
    }    
}

# -----------------------------------------------------------------------------
Set-Alias gsacud Get-SCAccessControlUnitDoors
Function Get-SCAccessControlUnitDoors {
    <#
    .Synopsis
        Method used to get all doors controlled by the given access control unit.
    .DESCRIPTION
        This Method will allow the user to get all door of the given access control unit.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter UnitId will be used to specify the access control unit we want to get the doors from.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ACUnits = Get-SCEntities -t Units -f All

        Get-SCAccessControlUnitDoors -AccessControlUnitId $ACUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AccessControlUnitId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AccessControlUnitId -RelationName doors
        }
    }    
}

# -----------------------------------------------------------------------------
Set-Alias gsacui Get-SCAccessControlUnitInterfaceModules
Function Get-SCAccessControlUnitInterfaceModules {
    <#
    .Synopsis
        Method used to get all the interface modules of the given access control unit.
    .DESCRIPTION
        This Method will allow the user to get all the interface modules of the given access control unit.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter UnitId will be used to specify the access control unit we want to get the interface modules from
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ACUnits = Get-SCEntities -t Units -f All

        Get-SCAccessControlUnitInterfaceModules -UnitId $ACUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UnitId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $UnitId -RelationName InterfaceModules
        }
    }    
}

# -----------------------------------------------------------------------------
Set-Alias ssacu Set-SCAccessControlUnit
Function Set-SCAccessControlUnit() {
    <#
    .Synopsis
        Used to update the properties of a access control unit in Security Center.
    .DESCRIPTION
        This method is used to update the properties of a access control unit to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods.
        
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Unit represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $ACUnits = Get-SCEntities -t Units -f All
        $ACUnits[0].Description = "test"

        Set-SCAccessControlUnit $ACUnits[0]

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("acu")] $Unit
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Set-SCEntity -EntityToSet $Unit
        }
    }
}


# -----------------------------------------------------------------------------
Set-Alias ssacup Set-SCAccessControlUnitPassword
Function Set-SCAccessControlUnitPassword() {
    <#
    .Synopsis
        Used to update the password of an access control unit in Security Center
    .DESCRIPTION
        This method is used to update the password of an access control unit to Security Center (for administrators only).
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter UnitId represents the access control unit and contains the properties that will be updated to security Center

        The parameter Password is used to specify the new password to update
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $Units = Get-SCEntities -t Units -f All

        Set-SCAccessControlUnitPassword -UnitId $Units[0].Id -Password "pass"

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $UnitId,
        [parameter(Mandatory=$true)] [alias("p")] [string]$Password
    )

    SCCmdletImplementation $MyInvocation.InvocationName { 
        #special case for user password, it will be filtered out of the generic Set-User method
        $uid = GetIdFromObject $UnitId
        $uri = "Entities/$uid/" 

        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Password", $Password)
        $jsonBody = $jsonObject | ConvertTo-Json

        InvokeSCRestMethod -UriSuffix $uri -Method 'PUT' -Body $jsonBody
    }
}
# -----------------------------------------------------------------------------
Set-Alias ssacup Show-SCAccessControlUnitProperties
Function Show-SCAccessControlUnitProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an access control unit
    .DESCRIPTION
        This method will list the supported properties and relation of an access control unit (the data model, not the actual data).  This method is used
        when you want to know what is available for a given access control unit
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCAccessControlUnitProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiUnit" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}
# -----------------------------------------------------------------------------
Set-Alias ascim Add-SCInterfaceModule
Function Add-SCInterfaceModule{
    <#
    .Synopsis
        Method used to add an access control interface module to an access control unit in Security Center
    .DESCRIPTION
        This method is used to add an access control interface module in Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
		# Call the following in order to import all the modules. Otherwise, Enter-SCSession will not work.
        # Import-Module ".\SDK\REST\Powershell Scripts\Modules\SecurityCenter\SecurityCenter.psm1" -Force
        # Import-Module .\SecurityCenter.psm1 -Force
        
		# Must enter a valid Security Center session before calling any method.
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $InterfaceModuleXml = Get-SCInterfaceModuleXml -InterfaceModuleType "EP1501"
        $ACUnits = Get-SCEntities -t Units -f All
        $unitId = $ACUnits[0].Id
        Add-SCInterfaceModule -UnitId $unitId -InterfaceType "EP1501" -InterfaceName "EP1501 from powershell" -InterfaceModuleXml $InterfaceModuleXml.XmlInfo

        Exit-SCSession

    .NOTES        
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory = $true, Position = 0)] [alias("id")] $UnitId,
        [parameter(Mandatory = $true, Position = 1)] [alias("type")] $InterfaceType,
        [parameter(Mandatory = $true, Position = 2)] [alias("name")] $InterfaceName,
        [parameter(Mandatory = $true, Position = 3)] [alias("xmlInfo")] $InterfaceModuleXml,
        [parameter(Mandatory = $false, Position = 4)] [alias("parentId")] $ParentInterfaceModuleId,
        [parameter(Mandatory = $false, Position = 5)] [alias("parentXml")] $ParentInterfaceModuleXml
    )
 
    process {

        $uri = "Entities/" + $UnitId + "/InterfaceModules"
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("InterfaceType",$InterfaceType)
        $jsonObject.Add("XmlConfiguration", $InterfaceModuleXml)
        $jsonObject.Add("Name", $InterfaceName)
        if ($null -ne $ParentInterfaceModuleId){
            $jsonObject.Add("ParentInterfaceModule", $ParentInterfaceModuleId)
        }
        if ($null -ne $ParentInterfaceModuleXml){
            $jsonObject.Add("ParentXmlConfiguration", $ParentInterfaceModuleXml)
        }
        $jsonBody = $jsonObject | ConvertTo-Json
        
        SCCmdletImplementation $MyInvocation.InvocationName {
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'