﻿//using System;
//using System.Collections.Generic;

//namespace LP.Permissions.StoreSetup.Models;

//public partial class Permission
//{
//    public int PermissionId { get; set; }

//    public int PrivilegeId { get; set; }

//    public int UserGroupId { get; set; }

//    public int? StateId { get; set; }

//    public virtual Privilege Privilege { get; set; } = null!;

//    public virtual State? State { get; set; }

//    public virtual UserGroup UserGroup { get; set; } = null!;
//}
