# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias asarc Add-SCAccessRuleCardholder
Function Add-SCAccessRuleCardholder {
    <#
    .Synopsis
        Method used to add a cardholder to the given access rule.
    .DESCRIPTION
        This Method will allow the user to add a security center cardholder to an access rule.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter AccessRuleId will be used to specify the access rule we want to add the cardholder to.

        The parameter CardholderId represents the Id of the cardholder we want to add to the access rule.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $always = Get-SCEntities -type schedules | Where-Object {$_.Name -eq "Always"} | Get-SCEntity
        $newar = New-SCAccessRule -Name "Myar" -Schedule $always | get-SCAccessRule
        
        $ch = New-SCCardholder -Name "MyNewCardholder"

        Add-SCAccessRuleCardholder -AccessRuleId $newar -CardholderId $ch
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AccessRuleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cid")] $CardholderId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CardholderId
            Set-SCEntityRelation -EntityId $AccessRuleId -RelationName "cardholders" -RelationId $cid   
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias gscar Get-SCAccessRule
Function Get-SCAccessRule {
    <#
    .Synopsis
        This method will return all the properties of the access rule represented by the ID.
    .DESCRIPTION
        This method will return all the basic properties of the access rule represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The AccessRuleId parameter represents the Id of the access rule to retrieve (The guid representing the access rule in the Security Center System)
        You can also pass any access rule object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ar = New-SCAccessRule -n "MyNewAccessRule"
        Get-SCAccessRule -AccessRuleId $ar.Id

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ar = New-SCAccessRule -n "MyNewAccessRule2" | Get-SCAccessRule

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AccessRuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AccessRuleId            
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gscara Get-SCAccessRuleAccessPoints
Function Get-SCAccessRuleAccessPoints {
    <#
    .Synopsis
        Method used to get all access points of the given access rule.
    .DESCRIPTION
        This Method will allow the user to get all access points of the given access rule.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter AccessRuleId will be used to specify the access rule we want to get the access points from.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $always = Get-SCEntities -type schedules | Where-Object {$_.Name -eq "Always"} | Get-SCEntity
        $newar = New-SCAccessRule -Name "Myar" -Schedule $always | get-SCAccessRule

        Get-SCAccessRuleAccessPoints -AccessRuleId $newar
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AccessRuleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $AccessRuleId -RelationName "accesspoints"
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias gscarc Get-SCAccessRuleCardholders
Function Get-SCAccessRuleCardholders {
    <#
    .Synopsis
        Method used to get all the cardholders of the given access rule.
    .DESCRIPTION
        This Method will allow the user to get all the cardholders of the given access rule.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter AccessRuleId will be used to specify the access rule we want to get the cardholders from.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $always = Get-SCEntities -type schedules | Where-Object {$_.Name -eq "Always"} | Get-SCEntity
        $newar = New-SCAccessRule -Name "Myar" -Schedule $always | get-SCAccessRule
        
        $ch = New-SCCardholder -Name "MyNewCardholder"

        Add-SCAccessRuleCardholder -AccessRuleId $newar -CardholderId $ch

        Get-SCAccessRuleCardholders -AccessRuleId $newar
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AccessRuleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $AccessRuleId -RelationName "cardholders"
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias nscar New-SCAccessRule
Function New-SCAccessRule {
    <#
    .Synopsis
        Method used to create a new access rule with the provided name and schedule.
    .DESCRIPTION
        This Method will allow the user to create a new access rule with the provided name.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete..

        The name parameter will be given to new access rule upon creation.
        
        The schedule parameter will be used to specify when the access rule is in place. 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $always = Get-SCEntities -type schedules | Where-Object {$_.Name -eq "Always"} | Get-SCEntity

        $ar = New-SCAccessRule -Name "MyNewAccessRule" -Schedule $always

        Exit-SCSession 

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name,
        [parameter(Mandatory=$true)] [alias("s")]$schedule
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uriSuffix = "Entities/" 

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("EntityType", "AccessRules")
            $jsonObject.Add("Name", $Name)
            $jsonObject.Add("Schedule", $schedule.Id)

            $jsonBody = $jsonObject | ConvertTo-Json
        
            InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'POST' -Body $jsonBody
        }
    } 
}

# -----------------------------------------------------------------------------
Function New-SCTemporaryAccessRule {
     <#
    .Synopsis
        Method used to create a new access rule with its type set to Temporary
    .DESCRIPTION
        This Method will allow the user to create a new Temporary access rule with the provided name, schedule, start time, and end time.
        
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The name, schedule, start time, and endtime will be given to the new Temporary access rule upon creation. 
        The start time and end time are read in as UTC.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $always = Get-SCEntities -type schedules | Where-Object {$_.Name -eq "Always"} | Get-SCEntity

        $ar = New-SCTemporaryAccessRule -Name "MyNewAccessRule" -Schedule $always -StartTime "2017-10-24T12:30:00Z" -EndTime "2017-10-26T12:30:00Z"

        Exit-SCSession 

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name,
        [parameter(Mandatory=$true)] [alias("s")]$Schedule,
        [parameter(Mandatory=$true)] [alias("st")]$StartTime,
        [parameter(Mandatory=$true)] [alias("et")]$EndTime
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uriSuffix = "Entities/" 

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("EntityType", "AccessRules")
            $jsonObject.Add("Name", $Name)
            $jsonObject.Add("Schedule", $Schedule.Id)
            $jsonObject.Add("StartTime", $StartTime)
            $jsonObject.Add("EndTime", $EndTime)
            $jsonObject.Add("AccessPointRuleType", "Temporary")

            $jsonBody = $jsonObject | ConvertTo-Json
        
            InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'POST' -Body $jsonBody
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias rscar Remove-SCAccessRule
Function Remove-SCAccessRule {
    <#
    .Synopsis
        Will remove the access rule represented by the provided AccessruleId from Security Center
    .DESCRIPTION
        This method will permanently remove the specified access rule from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The AccessRuleId parameter represents the entity to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $always = Get-SCEntities -type schedules | Where-Object {$_.Name -eq "Always"} | Get-SCEntity

        $ar = New-SCAccessRule -Name "MyNewAccessRule" -Schedule $always

        Remove-SCAccessRule -AccessRuleId $ar.Id

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AccessRuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $AccessRuleId
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias rscarc Remove-SCAccessRuleCardholder
Function Remove-SCAccessRuleCardholder {
    <#
    .Synopsis
        Method used to remove a cardholder from the given access rule
    .DESCRIPTION
        This Method will allow the user to remove a security center cardholder from a specified access rule
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AccessRuleId will be used to specify the access rule we want to remove the cardholder from

        The parameter CardholderId represents the Id of the cardholder we want to remove from the access rule
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $always = Get-SCEntities -type schedules | Where-Object {$_.Name -eq "Always"} | Get-SCEntity
        $newar = New-SCAccessRule -Name "Myar" -Schedule $always | get-SCAccessRule
        
        $ch = New-SCCardholder -Name "MyNewCardholder"

        Add-SCAccessRuleCardholder -AccessRuleId $newar -CardholderId $ch

        Remove-SCAccessRuleCardholder -AccessRuleId $ar -CardholderId $ch
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AccessRuleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cid")] $CardholderId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CardholderId
            Remove-SCEntityRelation -EntityId $AccessRuleId -RelationName "cardholders" -RelationId $cid  
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias sscar Set-SCAccessRule
Function Set-SCAccessRule() {
    <#
    .Synopsis
        Used to update the properties of an access rule in Security Center
    .DESCRIPTION
        This method is used to update the properties of an access rule in Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AccessRule represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new access rule
        $always = Get-SCEntities -type schedules | Where-Object {$_.Name -eq "Always"} | Get-SCEntity
        $newar = New-SCAccessRule -Name "Myar" -Schedule $always | get-SCAccessRule
        $newar.Positive = $false
        $newar.Description = "test"
        
        Set-SCAccessRule -AccessRule $newar

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("ar")] $AccessRule
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $AccessRule
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias sscarp Show-SCAccessRuleProperties
Function Show-SCAccessRuleProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an access rule
    .DESCRIPTION
        This method will list the supported properties and relation of an access rule (the data model, not the actual data).  This method is used
        when you want to know what is available for a given access rule
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCAccessRuleProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiAccessPointRule" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'