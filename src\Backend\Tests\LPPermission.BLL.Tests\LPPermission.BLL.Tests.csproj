<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.6.0" />
	  <PackageReference Include="MockQueryable.Moq" Version="7.0.3" />
	  <PackageReference Include="Moq" Version="4.20.72" />	  
	  <PackageReference Include="NUnit" Version="3.13.3" />
	  <PackageReference Include="NUnit3TestAdapter" Version="4.2.1" />
	  <PackageReference Include="NUnit.Analyzers" Version="3.6.1" />
	  <PackageReference Include="coverlet.collector" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\LPPermission.BLL\LPPermission.BLL.csproj" />
  </ItemGroup>

</Project>
