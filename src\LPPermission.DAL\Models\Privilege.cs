﻿using System;
using System.Collections.Generic;

namespace LPPermission.DAL.Models;

public partial class Privilege
{
    public int PrivilegeId { get; set; }

    public int MenuId { get; set; }

    public string? Name { get; set; }

    public string? Description { get; set; }

    public string GenetecRef { get; set; } = null!;

    public virtual Menu Menu { get; set; } = null!;

    public virtual ICollection<Permission> Permissions { get; set; } = new List<Permission>();
}
