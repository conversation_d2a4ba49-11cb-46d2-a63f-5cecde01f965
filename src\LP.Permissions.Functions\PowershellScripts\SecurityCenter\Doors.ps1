﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCDoorDoorSideAccessRule {
    <#
    .Synopsis
        Method used to add an access rule to the given door side
    .DESCRIPTION
        This Method will allow the user to add a security center access rule to a door side
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorSideId will be used to specify the door side we want to add the access rule to

        The parameter AccessRuleId represents the Id of the access rule we want to add to the door side
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $always = Get-SCEntities -type schedules | Where-Object {$_.Name -eq "Always"} | Get-SCEntity
        $newar = New-SCAccessRule -Name "MyAccesRule" -Schedule $always | get-SCAccessRule
        
        $door = New-SCDoor -Name "MyNewDoor"
        $doorSide = (Get-SCDoorDoorsides -DoorId $door.Id) | Where-Object { $_.Name -eq "A" }

        Add-SCDoorDoorSideAccessRule -DoorSideId $doorSide -AccessRuleId $newar
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorSideId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("rid")] $AccessRuleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $AccessRuleId
            Set-SCEntityRelation -EntityId $DoorSideId -RelationName "rules" -RelationId $rid   
        }
    } 
}

# -----------------------------------------------------------------------------
Function Add-SCDoorDoorSideCamera {
    <#
    .Synopsis
        Method used to add a camera to the given door side
    .DESCRIPTION
        This Method will allow the user to add a camera to a door side
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorSideId will be used to specify the door side we want to add the access rule to

        The parameter CameraId represents the Id of the camera we want to add to the door side
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $cameras = Get-SCEntities -type Cameras
        
        $door = New-SCDoor -Name "MyNewDoor"
        $doorSideA = (Get-SCDoorDoorsides -DoorId $door.Id) | Where-Object { $_.Name -eq "In" }

        Add-SCDoorDoorSideCamera -DoorSideId $doorSideA -CameraId $cameras[0].Id
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorSideId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cid")] $CameraId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CameraId
            Set-SCEntityRelation -EntityId $DoorSideId -RelationName "Cameras" -RelationId $cid   
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias gsd Get-SCDoor
Function Get-SCDoor {
    <#
    .Synopsis
        This method will return all the properties of the door represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the door represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The DoorId parameter represents the Id of the door to retrieve (The guid representing the door in the Security Center System)
        You can also pass any door object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $door = New-SCDoor -n "MyNewDoor"

        Get-SCDoor -DoorId $door.Id

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $door = New-SCDoor -n "MyNewDoor" | Get-SCDoor

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $door = nsd -n "MyNewDoor" | gsd

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $DoorId   
        }
    } 
}

# -----------------------------------------------------------------------------
Function Get-SCDoorDoorSideAccessRules {
    <#
    .Synopsis
        Method used to get all access rules of the given door side
    .DESCRIPTION
        This Method will allow the user to get all access rules of the given door side
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorSideId will be used to specify the door side we want to get the access rules from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $door = New-SCDoor -Name "MyNewDoor"
        $doorSide = (Get-SCDoorDoorsides -DoorId $door.Id) | Where-Object { $_.Name -eq "A" }

        Get-SCDoorDoorSideAccessRules -DoorSideId $doorSide
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorSideId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $doorSideId -RelationName "rules"
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCDoorDoorSideCameras {
    <#
    .Synopsis
        Method used to get all the cameras of the given door side
    .DESCRIPTION
        This Method will allow the user to get all the cameras of the given door side
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorSideId will be used to specify the door side we want to get the cameras from
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $cameras = Get-SCEntities -type Cameras
        
        $door = New-SCDoor -Name "MyNewDoor"
        $doorSideA = (Get-SCDoorDoorsides -DoorId $door.Id) | Where-Object { $_.Name -eq "In" }

        Add-SCDoorDoorSideCamera -DoorSideId $doorSideA -CameraId $cameras[0].Id

        Get-SCDoorDoorSideCameras -DoorSideId $doorSideA
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorSideId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $doorSideId -RelationName "Cameras"
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCDoorDoorsides {
    <#
    .Synopsis
        Method used to get all door sides of the given door
    .DESCRIPTION
        This method will allow the user to get all door sides of the given door
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorId will be used to specify the door we want to get the credentials from
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $door = New-SCDoor -n "MyNewDoor"

        Get-SCDoorDoorsides -DoorId $door.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $DoorId -RelationName "doorsides"
        }
    }  
}

# -----------------------------------------------------------------------------
Function Get-SCDoorAccessPoint {
    <#
    .Synopsis
        This method will return all the properties of the access point represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the access point represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The APId parameter represents the Id of the access point to retrieve (The guid representing the door in the Security Center System)
        You can also pass any door object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $door = New-SCDoor -n "MyNewDoor"
        $ap = Get-SCDoorAccessPoints $door.Id

        Get-SCDoorAccessPoint -APId $ap[0]

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $APId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $APId   
        }
    } 
}

# -----------------------------------------------------------------------------
Function Get-SCDoorAccessPoints {
    <#
    .Synopsis
        Method used to get all access points of the given door
    .DESCRIPTION
        This method will allow the user to get all the access points of the given door
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorId will be used to specify the door we want to get the credentials from
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $door = New-SCDoor -n "MyNewDoor"

        Get-SCDoorAccessPoints -Id $door.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $DoorId -RelationName "AccessPoints"
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias isdu Invoke-SCDoorUnlock
Function Invoke-SCDoorUnlock {
    <#
    .Synopsis
        Method used to unlock a door
    .DESCRIPTION
        This Method will allow the user to unlock a security center door.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorId is used to specify which door to unlock

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $door = New-SCDoor -n "MyNewDoor"

        Invoke-SCDoorUnlock -Id $door.Id

        #Exit the session
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorId
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $did = GetIdFromObject $DoorId
            $uri = "Entities/$did/unlock"
         
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", "")
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias nsd New-SCDoor
Function New-SCDoor {
    <#
    .Synopsis
        Method used to create a new door with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new door with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new door upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $door = New-SCDoor -n "MyNewDoor"

        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $door = nsd "MyNewDoor"

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation "New-SCDoor"{
            New-SCEntity -n $Name -t Doors
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias rsd Remove-SCDoor
Function Remove-SCDoor {
    <#
    .Synopsis
        Will remove the door represented by the provided DoorId parameter from Security Center
    .DESCRIPTION
        This method will permanently remove the specified door from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The DoorId parameter represents the door to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $door = New-SCDoor -n "MyNewDoor"

        Remove-SCDoor -Id $door.Id

        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $door = nsd "MyNewDoor"

        rsd $door

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $DoorId
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCDoorDoorSideAccessRule {
    <#
    .Synopsis
        Method used to remove an access rule from the given door side
    .DESCRIPTION
        This Method will allow the user to remove a security center access rule from a specified door side
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorSideId will be used to specify the door side we want to remove the access rule from

        The parameter AccessRuleId represents the Id of the access rule we want to remove from the door side
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $always = Get-SCEntities -type schedules | Where-Object {$_.Name -eq "Always"} | Get-SCEntity
        $newar = New-SCAccessRule -Name "MyAccesRule" -Schedule $always | get-SCAccessRule
        
        $door = New-SCDoor -Name "MyNewDoor"
        $doorSide = (Get-SCDoorDoorsides -DoorId $door.Id) | Where-Object { $_.Name -eq "A" }

        Add-SCDoorDoorSideAccessRule -DoorSideId $doorSide -AccessRuleId $newar

        Remove-SCDoorDoorSideAccessRule -DoorSideId $doorSide -AccessRuleId $newAr
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorSideId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("rid")] $AccessRuleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $AccessRuleId
            Remove-SCEntityRelation -EntityId $DoorSideId -RelationName "rules" -RelationId $rid  
        }
    }   
}

# -----------------------------------------------------------------------------
Function Remove-SCDoorDoorSideCamera {
    <#
    .Synopsis
        Method used to remove a camera from the given door side
    .DESCRIPTION
        This Method will allow the user to remove a camera from a specified door side
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorSideId will be used to specify the door side we want to remove the access rule from

        The parameter CameraId represents the Id of the camera we want to remove from the door side
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $cameras = Get-SCEntities -type Cameras
        
        $door = New-SCDoor -Name "MyNewDoor"
        $doorSideA = (Get-SCDoorDoorsides -DoorId $door.Id) | Where-Object { $_.Name -eq "In" }

        Add-SCDoorDoorSideCameras -DoorSideId $doorSideA -CameraId $cameras[0].Id

        Remove-SCDoorDoorSideCamera -DoorSideId $doorSide -CameraId $cameras[0].Id
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorSideId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cid")] $CameraId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $CameraId
            Remove-SCEntityRelation -EntityId $DoorSideId -RelationName "Cameras" -RelationId $cid  
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias ssd Set-SCDoor
Function Set-SCDoor() {
    <#
    .Synopsis
        Used to update the properties of a door in Security Center
    .DESCRIPTION
        This method is used to update the properties of a door to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Door represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $door = New-SCDoor -n "MyNewDoor" | gsd
        $door.Description = "test"

        Set-SCDoor $door

        Exit-SCSession 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $door = New-SCDoor -n "MyNewDoor" | gsd
        $door.Description = "test"

        ssd $door

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("u")] $Door
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Set-SCEntity -EntityToSet $Door
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssdap Set-SCDoorAccessPoint
Function Set-SCDoorAccessPoint() {
    <#
    .Synopsis
        Used to update the properties of an access point in Security Center
    .DESCRIPTION
        This method is used to update the properties of an access point in Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter AccessPoint represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorIn = (Get-SCDoorDoorsides -DoorId $door.Id) | Get-SCEntity | Where-Object { $_.AccessPointSide -eq "Alpha" }
        $doorIn.Description = "test"

        Set-SCDoorAccessPoint $doorSide

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("ds")] $AccessPoint
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Set-SCEntity -EntityToSet $AccessPoint
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssda Set-SCDoorAccessPoint
Function Set-SCDoorDoorSide() {
    <#
    .Synopsis
        Used to update the properties of a door side in Security Center
    .DESCRIPTION
        This method is used to update the properties of a door side in Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorSide represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorIn = (Get-SCDoorDoorsides -DoorId $door.Id) | Get-SCEntity | Where-Object { $_.AccessPointSide -eq "Alpha" }
        $doorIn.Description = "test"

        Set-SCDoorDoorSide $doorSide

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("ds")] $DoorSide
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Set-SCEntity -EntityToSet $DoorSide
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCDoorActivityReport {
    <#
    .Synopsis
        Used to retrieve a door activity report
    .DESCRIPTION
        This method will return a door activity report for all the activities of the specified door  The report will contain a Results property
        that contains 2 arrays.  The first array will be the column definition.  This will contain the information this will be in each rows.
        The second array is the actual data of the report matching the column definition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The door parameter represents the Id of the door to retrieve (The guid representing the door in the Security Center System)
        You can also pass any door object that contains an ID as a parameter
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $doors = Get-SCEntities -Type Doors

        Show-SCDoorActivityReport $doors[0]

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $Door
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $did = GetIdFromObject $Door   
            $uri = "Reports/DoorActivities?Doors="  + $did
            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        }
    }  
}

# -----------------------------------------------------------------------------
Function Show-SCDoorProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a door
    .DESCRIPTION
        This method will list the supported properties and relation of an door (the data model, not the actual data).  This method is used
        when you want to know what is available for a given door
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCDoorProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -Method 'Get' -UriBuilder { $args[0] + "Help/Entities/ApiDoor" }

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCAccessPointProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an access point
    .DESCRIPTION
        This method will list the supported properties and relation of an access point (the data model, not the actual data).  This method is used
        when you want to know what is available for a given access point
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCDoorProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -Method 'Get' -UriBuilder { $args[0] + "Help/Entities/ApiAccessPoint" }

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCDoorDoorSideProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a door side
    .DESCRIPTION
        This method will list the supported properties and relation of an door side (the data model, not the actual data).  This method is used
        when you want to know what is available for a given door side
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCDoorProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -Method 'Get' -UriBuilder { $args[0] + "Help/Entities/ApiDoorAccessPoint" }

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'