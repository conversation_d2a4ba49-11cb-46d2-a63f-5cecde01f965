﻿@page "/orchestrator-log-entries"
@using LPPermission.UI.Models
@inject LPPermission.UI.Services.LogService LogService
@inject LPPermission.UI.Services.OrchestratorService OrchestratorService

<h3>Orchestrator and Log Entries</h3>

<button class="btn btn-primary" @onclick="StartOrchestrator">Start Orchestrator</button>

@if (isLoading)
{
    <p><em>Loading...</em></p>
}
else if (logEntries != null)
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Banner</th>
                <th>Start Time</th>
                <th>End Time</th>
                <th>Duration</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var logEntry in logEntries)
            {
                <tr>
                    <td>@logEntry.Banner</td>
                    <td>@logEntry.StartTime</td>
                    <td>@logEntry.EndTime</td>
                    <td>@logEntry.Duration</td>
                </tr>
            }
        </tbody>
    </table>
}

@code {
    private List<LogEntry> logEntries;
    private bool isLoading = false;

    private async Task StartOrchestrator()
    {
        isLoading = true;
        await OrchestratorService.StartOrchestratorAsync();
        logEntries = await LogService.GetLogEntriesAsync();
        isLoading = false;
    }
}
