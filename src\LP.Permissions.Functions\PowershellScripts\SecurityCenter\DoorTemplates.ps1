﻿# -----------------------------------------------------------------------------
Function New-SCDoorTemplate{
    <#
    .Synopsis
        Method used to create a new door template with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new door template with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new door template upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $door = New-SCDoorTemplate -n "MyNewDoorTemplate"

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation "New-SCDoorTemplate"{
            New-SCEntity -n $Name -t DoorTemplates
        }
    } 
}

# -----------------------------------------------------------------------------
Function Get-SCDoorTemplate{
    <#
    .Synopsis
        This method will return all the properties of the door template represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the door template represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The DoorTemplateId parameter represents the Id of the door template to retrieve (The guid representing the door template in the Security Center System)
        You can also pass any door template object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $doorTemplate = New-SCDoorTemplate -n "MyNewDoorTemplate"

        Get-SCDoorTemplate -DoorTemplateId $doorTemplate.id

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $doorTemplate = New-SCDoorTemplate -n "MyNewDoorTemplate" | Get-SCDoorTemplate

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorTemplateId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $DoorTemplateId   
        }
    } 
}

# -----------------------------------------------------------------------------
Function Remove-SCDoorTemplate{
    <#
    .Synopsis
        Will remove the door template represented by the provided DoorTemplateId parameter from Security Center
    .DESCRIPTION
        This method will permanently remove the specified door template from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The DoorTemplateId parameter represents the door to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoorTemplate -n "MyNewDoorTemplate"

        Remove-SCDoorTemplate -DoorTemplateId $doorTemplate.Id

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorTemplateId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $DoorTemplateId
        }
    }
}

# -----------------------------------------------------------------------------
Function Set-SCDoorTemplate(){
    <#
    .Synopsis
        Used to update the properties of a door template in Security Center
    .DESCRIPTION
        This method is used to update the properties of a door template to Security Center. All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Door represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoorTemplate -n "MyNewDoorTemplate"
        $doorTemplate.Description = "test"

        Set-SCDoorTemplate $doorTemplate

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("u")] $DoorTemplate
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Set-SCEntity -EntityToSet $DoorTemplate
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCDoorTemplateAccessPoints{
    <#
    .Synopsis
        Method used to get all access points of the given door template
    .DESCRIPTION
        This Method will allow the user to get all access points of the given door template
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoortemplate -Name "MyNewDoorTemplate"

        Get-SCDoorTemplateAccessPoints -DoorTemplateId $doorTemplate.id
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorTemplateId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $DoorTemplateId -RelationName "AccessPoints"
        }
    }  
}

# -----------------------------------------------------------------------------
Function Set-SCDoorTemplateAccessPoints {
    <#
    .Synopsis
        Method used to set access points to the given door template
    .DESCRIPTION
        This Method will allow the user to set security center access points to a door template
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorTemplateId will be used to specify the door template we want to set the access points to

        The parameter AccessPoints represents the access points we want to set to the door template. An access point is structured as such:
		{DeviceId :<string>, Side : <ApiAccessPointSide>, Type : <ApiAccessPointType>}
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoorTemplate -Name "MyNewDoorTemplate"
		$accessPoints = @(
			@{DeviceId = $devideIdGuid1; Side = $side1; Type = $type1;},
			@{DeviceId = $devideIdGuid2; Side = $side2; Type = $type2;}
		)

        Set-SCDoorTemplateAccessPoints -DoorTemplateId $doorTemplate.id -AccessPoints $accessPoints
        
        Exit-SCSession 

	.EXAMPLE

        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoorTemplate -Name "MyNewDoorTemplate"
		$accessPoints = @(
			@{DeviceId = $devideIdGuid1; Side = $side1; Type = $type1;},
			@{DeviceId = $devideIdGuid2; Side = $side2; Type = $type2;}
		)

        Set-SCDoorTemplateAccessPoints -DoorTemplateId $doorTemplate.id -AccessPoints $accessPoints
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorTemplateId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("aps")] $AccessPoints
    )
 
    Begin {
    }

	Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $did = GetIdFromObject $DoorTemplateId
			$jsonObjectList = New-Object System.Collections.ArrayList
			foreach ($ap in $AccessPoints )
			{
				$jsonObject = [ordered]@{} 
				$jsonObject.Add("DeviceId", $ap.DeviceId)
				$jsonObject.Add("Side", $ap.Side)
				$jsonObject.Add("Type", $ap.Type)
				$jsonObjectList.Add($jsonObject);
			}
            $jsonBody = ConvertTo-Json $jsonObjectList
            $uri = "Entities/$did/AccessPoints"
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCDoorTemplateAccessRules{
    <#
    .Synopsis
        Method used to get all access rules of the given door template
    .DESCRIPTION
        This Method will allow the user to get all access rules of the given door template
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoortemplate -Name "MyNewDoorTemplate"

        Get-SCDoorTemplateAccessRules -DoorTemplateId $doorTemplate.id
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorTemplateId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $DoorTemplateId -RelationName "AccessRules"
        }
    }  
}

# -----------------------------------------------------------------------------
Function Set-SCDoorTemplateCardAndPinSettings{
    <#
    .Synopsis
        Method used to set card and pin settings to the given door template
    .DESCRIPTION
        This Method will allow the user to set card and pin settings to a door template
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorTemplateId will be used to specify the door template we want to set the card and pin settings to

        The parameter CardAndPinSettings represents the card and pin coverage we want to set to the door template. A card and pin coverage is structured as such:
		{CardAndPinCoverage :<Guid>, CardAndPinTimeout : <int>, Side : <ApiAccessPointSide>, ReaderMode : <ApiDeviceReaderMode>}
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoorTemplate -Name "MyNewDoorTemplate"
		$cardAndPinSettings = @(
			@{CardAndPinCoverage = $coverage1Guid; CardAndPinTimeout = $Timeout2Int; Side = $side1ApiAccessPointSide; ReaderMode = $reader1ApiDeviceReaderMode; },
			@{CardAndPinCoverage = $coverage2Guid; CardAndPinTimeout = $Timeout2Int; Side = $side2ApiAccessPointSide; ReaderMode = $reader2ApiDeviceReaderMode; }
		)

        Set-SCDoorTemplateCardAndPinSettings -DoorTemplate $doorTemplate.id -CardAndPinSettings $cardAndPinSettings
        
        Exit-SCSession 
	.EXAMPLE
        #This exemple removes the card and pin settings of the given door template
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoorTemplate -Name "MyNewDoorTemplate"
		$cardAndPinSettings = @()

        Set-SCDoorTemplateCardAndPinSettings -DoorTemplate $doorTemplate.id -CardAndPinSettings $cardAndPinSettings
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorTemplateId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("aps")] $CardAndPinSettings
    )
 
    Begin {
    }

	Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $did = GetIdFromObject $DoorTemplateId
			$jsonObjectList = New-Object System.Collections.ArrayList
			foreach ($cps in $CardAndPinSettings )
			{
				$jsonObject = [ordered]@{} 
				$jsonObject.Add("CardAndPinCoverage", $cps.CardAndPinCoverage)
				$jsonObject.Add("CardAndPinTimeout", $cps.CardAndPinTimeout)
				$jsonObject.Add("Side", $cps.Side)
				$jsonObject.Add("ReaderMode", $cps.ReaderMode)
				$jsonObjectList.Add($jsonObject);
			}
            $jsonBody = ConvertTo-Json $jsonObjectList
            $uri = "Entities/$did/CardAndPinSettings"
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCDoorTemplateCardAndPinSettings{
    <#
    .Synopsis
        Method used to get all card and pin settings of the given door template
    .DESCRIPTION
        This Method will allow the user to get all card and pin settings of the given door template
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoortemplate -Name "MyNewDoorTemplate"

        Get-SCDoorTemplateCardAndPinSettings -DoorTemplateId $doorTemplateId
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorTemplateId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $DoorTemplateId -RelationName "CardAndPinSettings"
        }
    }  
}

# -----------------------------------------------------------------------------
Function Set-SCDoorTemplateAccessRules{
    <#
    .Synopsis
        Method used to set access rules to the given door template
    .DESCRIPTION
        This Method will allow the user to set security center access rules to a door template
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorTemplateId will be used to specify the door template we want to set the access rules to

        The parameter AccessRules represents the access rules we want to set to the door template. An access rule is structured as such:
		{AccessRule :<Guid>, Side : <ApiAccessPointSide>}
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoorTemplate -Name "MyNewDoorTemplate"
		$accessRules = @(
			@{AccessRule = $arGuid1; Side = $side1;},
			@{AccessRule = $arGuid2; Side = $side2;}
		)

        Set-SCDoorTemplateAccessRules -DoorTemplate $doorTemplate.id -AccessRules $accessRules
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorTemplateId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("aps")] $AccessRules
    )
 
    Begin {
    }

	Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $did = GetIdFromObject $DoorTemplateId
			$jsonObjectList = New-Object System.Collections.ArrayList
			foreach ($ar in $AccessRules )
			{
				$jsonObject = [ordered]@{} 
				$jsonObject.Add("AccessRule", $ar.AccessRule)
				$jsonObject.Add("Side", $ar.Side)
				$jsonObjectList.Add($jsonObject);
			}
            $jsonBody = ConvertTo-Json $jsonObjectList
            $uri = "Entities/$did/AccessRules"
            InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCDoorTemplateAppliedDoors{
    <#
    .Synopsis
        Method used to get all applied doors of the given door template
    .DESCRIPTION
        This Method will allow the user to get all applied doors of the given door template
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $doorTemplate = New-SCDoortemplate -Name "MyNewDoorTemplate"

        Get-SCDoorTemplateAppliedDoors -DoorTemplateId $doorTemplateId
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorTemplateId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $DoorTemplateId -RelationName "AppliedDoors"
		}
    }  
}

# -----------------------------------------------------------------------------
Function Invoke-SCDoorTemplateApply{
    <#
    .Synopsis
        Method used to apply the door template to a door using provided unit and interface
    .DESCRIPTION
        This Method will allow the user to apply the door template to a door using provided unit and interface.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter DoorId is used to specify which door to apply the template to

		The parameter UnitId is used to specify which unit to set as prefered unit

		The parameter InterfaceId is used to specify which interface module to select devices from

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $doorTemplate = New-SCDoorTemplate -n "MyNewDoorTemplate"
		
        Invoke-SCDoorTemplateApply -DoorTemplateId $doorTemplate.id -DoorId $doorId -UnitId $unitId -InterfaceId $interfaceId

        #Exit the session
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $DoorTemplateId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("dId")] $DoorId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("uId")] $UnitId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("iId")] $InterfaceId
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $did = GetIdFromObject $DoorTemplateId
            $uri = "Entities/$did/Apply"
         
            $jsonObject = [ordered]@{} 
            $jsonObject.Add("doorGuid", $DoorId)
            $jsonObject.Add("unitGuid", $UnitId)
            $jsonObject.Add("interfaceGuid", $InterfaceId)
            $jsonBody = ConvertTo-Json $jsonObject
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
		}
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'