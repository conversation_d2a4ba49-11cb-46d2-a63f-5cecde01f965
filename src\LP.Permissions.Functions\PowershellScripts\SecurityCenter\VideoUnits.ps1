﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

Set-Alias asvu Add-SCVideoUnit
# -----------------------------------------------------------------------------
Function Add-SCVideoUnit {
    <#
    .Synopsis
        Method used to add a video unit to an archiver role in Security Center
    .DESCRIPTION
        This method is used to add a video unit to the provided archiver role in security center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ExtensionName parameter represents the extension (Genetec, Axis, bosch, sony, etc) to use when adding a video unit

        The ArchiverId parameter represents the Id of the archiver we want to add the unit to

        The IpAdress parameter represents the ip address of the video unit we want to add

        The Port parameter represents the discovery port of the video unit we want to add

        The User parameter reprents the username to use when connecting to the video unit

        The Password parameter reprents the password to use when connecting to the video unit

        The UseDefaultConnection parameter (optionnal, false by default) used to specify the usage or not of default connection values

        The SpecificConfiguration parameter (optionnal, false by default) used to specify values based on extension

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $Archivers = Get-SCRoles -t Archiver -f Base
        $Archivers | foreach {Write-Host $_.Name "  Guid : " $_.Id}

        Exit-SCSession

    .NOTES
        $result = Add-SCVideoUnit -ExtensionName Axis -ArchiverId $Archivers[0].Id -IpAdress ********** -User root -Password pass -Port 80

        #Exit the session
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=1)] [alias("Id")] $ArchiverId,
        [parameter(Mandatory=$true,Position=2)] [alias("ip")][string]$IpAdress,
        [parameter(Mandatory=$true,Position=3)] [alias("p")][int]$Port,
        [parameter(Mandatory=$false,Position=4)] [alias("u")] [string]$User,
        [parameter(Mandatory=$false,Position=5)] [alias("pwd")] [string]$Password,
        [parameter(Mandatory=$false,Position=6)] [alias("udc")][bool]$UseDefaultConnection = $false,
        [parameter(Mandatory=$false,Position=7)] [alias("sc")][string]$SpecificConfiguration = "",
        [parameter(Mandatory=$false,Position=8)] [alias("dp")][int]$DiscoveryPort

    )
 
    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'ExtensionName'
        $ParameterAlias = "xid"    
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]

        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $true
        $ParameterAttribute.Position = 0

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        $sess = GetSession -Quiet $true
        if ($sess -ne $null)
        {
            $arrSet = $sess.SCExtensionIds
            $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection.Add($ValidateSetAttribute)
        }

        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $ExtensionName = $PsBoundParameters[$ParameterName]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $aid = GetIdFromObject $ArchiverId

            $sess = GetSession
            $sess.ScExtensionMap | foreach{ if($_.Name -eq $ExtensionName) {$ExtensionId = $_.Id} }

            $uri = "Entities/VideoUnits/EnrollVideoUnit"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Role", $aid)
            $jsonObject.Add("ExtensionId", $ExtensionId)
            $jsonObject.Add("UseDefaultConnection", $UseDefaultConnection)
            $jsonObject.Add("Username", $User)
            $jsonObject.Add("Password", $Password)
            $jsonObject.Add("Address", $IpAdress)
            $jsonObject.Add("Port", $Port)
            $jsonObject.Add("SpecificConfiguration", $SpecificConfiguration)
            $jsonObject.Add("DiscoveryPort", $DiscoveryPort)
            $jsonBody = $jsonObject | ConvertTo-Json

            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

Function Add-SCVideoUnitV2 {
    <#
    .Synopsis
        Method used to add a video unit to an archiver role in Security Center using the unit assitant
    .DESCRIPTION
        This method is used to add a video unit to the provided archiver role in security center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ArchiverId parameter represents the Id of the archiver we want to add the unit to

	    The UnitAssistantId parameter represents the Id of the Unit assistant role we want to use

        The IpAdress parameter represents the ip address of the video unit we want to add

        The Port parameter represents the discovery port of the video unit we want to add

        The User parameter reprents the username to use when connecting to the video unit

        The Password parameter reprents the password to use when connecting to the video unit

        The UseDefaultConnection parameter (optionnal, false by default) used to specify the usage or not of default connection values

        The Manufacturer parameter reprents the manufacturer of the camera to enroll

		The ProductType parameter reprents the product type of the camera to enroll

		The AreaId parameter (optionnal) used to specify a default area to add the new camera to

		The SpecialFeatures parameter (optionnal) specific parameters to enroll certain type of cameras with

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $Archivers = Get-SCRoles -t Archiver -f Base
        $Archivers | foreach {Write-Host $_.Name "  Guid : " $_.Id}

		$UnitAssistant = Get-SCRoles -t UnitAssistant -f Base

        Exit-SCSession

    .NOTES
        $result = Add-SCVideoUnitV2 -Manufacturer Axis -ArchiverId $Archivers[0].Id -UnitAssistantId $UnitAssistant[0].Id -IpAdress ********** -User root -Password pass -Port 80

        #Exit the session
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=1)] [alias("Id")] $ArchiverId,
		[parameter(Mandatory=$true,Position=2)] [alias("uaId")] $UnitAssistantId,
        [parameter(Mandatory=$true,Position=3)] [alias("ip")][string]$IpAdress,
        [parameter(Mandatory=$false,Position=4)] [alias("p")][int]$Port,
		[parameter(Mandatory=$true,Position=5)] [alias("m")][string]$Manufacturer,
		[parameter(Mandatory=$false,Position=6)] [alias("pt")][string]$ProductType,
        [parameter(Mandatory=$false,Position=7)] [alias("u")] [string]$User,
        [parameter(Mandatory=$false,Position=8)] [alias("pwd")] [string]$Password,
        [parameter(Mandatory=$false,Position=9)] [alias("udc")][bool]$UseDefaultConnection = $false,
        [parameter(Mandatory=$false,Position=10)] [alias("sc")][string]$SpecialFeatures = "",
		[parameter(Mandatory=$false,Position=11)] [alias("aid")]$AreaId
    )
 
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $archiverid = GetIdFromObject $ArchiverId
			$uaid = GetIdFromObject $UnitAssistantId
			$areaid = GetIdFromObject $AreaId

            $sess = GetSession
            $uri = "Entities/VideoUnits/EnrollVideoUnit"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Role", $archiverid)
			$jsonObject.Add("UnitAssistantId", $uaid)
            $jsonObject.Add("Manufacturer", $Manufacturer)
			$jsonObject.Add("ProductType", $ProductType)
			$jsonObject.Add("AreaId", $AreaId)
            $jsonObject.Add("UseDefaultConnection", $UseDefaultConnection)
            $jsonObject.Add("Username", $User)
            $jsonObject.Add("Password", $Password)
            $jsonObject.Add("Address", $IpAdress)
            $jsonObject.Add("Port", $Port)
            $jsonObject.Add("SpecialFeatures", $SpecialFeatures)
            $jsonBody = $jsonObject | ConvertTo-Json

            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias gsvu Get-SCVideoUnit
Function Get-SCVideoUnit {
    <#
    .Synopsis
        This method will return all the properties of the video unit represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the video unit represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The VideoUnitId parameter represents the Id of the video unit to retreive (The guid representing the video unit in the Security Center System)
        You can also pass any video unit object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $VideoUnits = Get-SCEntities -t VideoUnits -f All

        Get-SCVideoUnit -VideoUnitId $VideoUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VideoUnitId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $VideoUnitId 
        }
    }       
}

# -----------------------------------------------------------------------------
Function Get-SCVideoUnitCameras {
    <#
    .Synopsis
        Method used to get all cameras of the given video unit
    .DESCRIPTION
        This Method will allow the user to get all cameras of the given video unit
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VideoUnitId will be used to specify the video unit we want to get the cameras from
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $VideoUnits = Get-SCEntities -t VideoUnits -f All

        Get-SCVideoUnitCameras -VideoUnitId $VideoUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VideoUnitId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $VideoUnitId -RelationName cameras
        }
    }    
}

# -----------------------------------------------------------------------------
Set-Alias rsvu Remove-SCVideoUnit
Function Remove-SCVideoUnit {
    <#
    .Synopsis
        Method used to permanently remove the given video unit and it's related archived video
    .DESCRIPTION
        This method is used to permanently remove the given video unit, it's cameras and the associated recorded video
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VideoUnitId will be used to specify the video unit we want to get the cameras from
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $VideoUnits = Get-SCEntities -t VideoUnits -f All

        Remove-SCVideoUnit -VideoUnitId $VideoUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VideoUnitId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $VideoUnitId
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssvu Set-SCVideoUnit
Function Set-SCVideoUnit() {
    <#
    .Synopsis
        Used to update the properties of a video unit in Security Center
    .DESCRIPTION
        This method is used to update the properties of a video unit to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated seperatly by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VideoUnit represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $VideoUnits = Get-SCEntities -t VideoUnits -f All
        $VideoUnits[0].Description = "test"

        Set-SCVideoUnit $VideoUnits[0]

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("vu")] $VideoUnit
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Set-SCEntity -EntityToSet $VideoUnit
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssvup Set-SCVideoUnitPassword
Function Set-SCVideoUnitPassword() {
    <#
    .Synopsis
        Used to update the password of a video unit in Security Center
    .DESCRIPTION
        This method is used to update the password of a video unit to Security Center (for administrators only).
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter VideoUnitId represents the video unit and contains the properties that will be updated to security Center

        The parameter Password is used to specify the new password to update
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $VideoUnits = Get-SCEntities -t VideoUnits -f All

        Set-SCVideoUnitPassword -VideoUnitId $VideoUnits[0].Id -Password "pass"

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $VideoUnitId,
        [parameter(Mandatory=$true)] [alias("p")] [string]$Password
    )

    SCCmdletImplementation $MyInvocation.InvocationName { 
        #special case for user password, it will be filtered out of the generic Set-User method
        $vuid = GetIdFromObject $VideoUnitId
        $uri = "Entities/$vuid/" 

        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Password", $Password)
        $jsonBody = $jsonObject | ConvertTo-Json

        InvokeSCRestMethod -UriSuffix $uri -Method 'PUT' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Function Show-SCVideoUnitProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a video unit
    .DESCRIPTION
        This method will list the supported properties and relation of a video unit (the data model, not the actual data).  This method is used
        when you want to know what is available for a given video unit
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCVideoUnitProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiVideoUnit" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}

#------------------------------------------------------------------------------
Function Invoke-SCVideoUnitMove {
	<#
	.Synopsis
		This method will move a video unit to a destination archiver.
	.DESCRIPTION
		This method is called when the user wants to move a video unit to a different archiver.
		Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.
		
		The VideoUnitId parameter represents the video unit to move.
		The DestinationId parameter represents the id of the archiver to move the video unit to.
	.EXAMPLE
		# Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""
		
        $units = Get-SCEntities -Type VideoUnits -Filter All
		Invoke-MoveVideoUnits -VideoUnitId $units[0].Id -DestinationId "16794100-669c-4aa7-8b06-15b1b5278ae1"

	    #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
	#>
	[CmdletBinding()]
	param(
		[parameter(Mandatory=$true,Position=0)] [alias("Id")] $VideoUnitId,
		[parameter(Mandatory=$true,Position=1)] [alias("dest") ] $DestinationId
	)
	Begin {
	}

	Process{
		SCCmdletImplementation $MyInvocation.InvocationName {
            $vuid = GetIdFromObject $VideoUnitId
			$uri = "entities/$vuid/MoveUnit"

			$jsonObject = [ordered]@{}
			$jsonObject.Add("Id", $DestinationId)
			$jsonBody = $jsonObject | ConvertTo-Json

			InvokeSCRestMethod -Method 'POST' -UriSuffix $uri -Body $jsonBody	
		}
	}
}

#------------------------------------------------------------------------------
Function Invoke-SCVideoUnitReconnect {
	<#
	.Synopsis
		This method will send a reconnect action for a video unit.
	.DESCRIPTION
		This method is called when the user wants to reconnect a video unit to an archiver.
		Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.
		
		The VideoUnitId parameter represents the video unit to move.

		The DestinationId parameter represents the id of the archiver to move the video unit to.
	.EXAMPLE
		# Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""
		
        $units = Get-SCEntities -Type VideoUnits -Filter All
		Invoke-SCVideoUnitReconnect -VideoUnitId $units[0].Id

	    #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
	#>
	[CmdletBinding()]
	param(
		[parameter(Mandatory=$true,Position=0)] [alias("id")] $VideoUnitId
	)
	Begin {
	}

	Process{
		SCCmdletImplementation $MyInvocation.InvocationName {
			$uri = "entities/$VideoUnitId/Reconnect"

			$jsonObject = [ordered]@{}
			$jsonObject.Add("Id", "")
			$jsonBody = $jsonObject | ConvertTo-Json

			InvokeSCRestMethod -Method 'POST' -UriSuffix $uri -Body $jsonBody	
		}
	}
}

#------------------------------------------------------------------------------
Function Invoke-SCVideoUnitIdentify {
	<#
	.Synopsis
		This method will send a Identify action for a video unit.
	.DESCRIPTION
		This method is called when the user wants to Identify a video unit.
		Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.
		
		The VideoUnitId parameter represents the video unit to move.

		The DestinationId parameter represents the id of the archiver to move the video unit to.
	.EXAMPLE
		# Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""
		
        $units = Get-SCEntities -Type VideoUnits -Filter All
		Invoke-SCVideoUnitIdentify -VideoUnitId $units[0].Id

	    #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
	#>
	[CmdletBinding()]
	param(
		[parameter(Mandatory=$true,Position=0)] [alias("id")] $VideoUnitId
	)
	Begin {
	}

	Process{
		SCCmdletImplementation $MyInvocation.InvocationName {
			$uri = "entities/$VideoUnitId/Identify"

			$jsonObject = [ordered]@{}
			$jsonObject.Add("Id", "")
			$jsonBody = $jsonObject | ConvertTo-Json

			InvokeSCRestMethod -Method 'POST' -UriSuffix $uri -Body $jsonBody	
		}
	}
}

#------------------------------------------------------------------------------
Function Invoke-SCVideoUnitPing {
	<#
	.Synopsis
		This method will send a ping action for a video unit.
	.DESCRIPTION
		This method is called when the user wants to ping a video unit.
		Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.
		
		The VideoUnitId parameter represents the video unit to move.

		The DestinationId parameter represents the id of the archiver to move the video unit to.
	.EXAMPLE
		# Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""
		
        $units = Get-SCEntities -Type VideoUnits -Filter All
		Invoke-SCVideoUnitPing -VideoUnitId $units[0].Id

	    #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
	#>
	[CmdletBinding()]
	param(
		[parameter(Mandatory=$true,Position=0)] [alias("id")] $VideoUnitId
	)
	Begin {
	}

	Process{
		SCCmdletImplementation $MyInvocation.InvocationName {
			$uri = "entities/$VideoUnitId/Ping"

			$jsonObject = [ordered]@{}
			$jsonObject.Add("Id", "")
			$jsonBody = $jsonObject | ConvertTo-Json

			InvokeSCRestMethod -Method 'POST' -UriSuffix $uri -Body $jsonBody	
		}
	}
}


Export-ModuleMember -Function '*-*' -Alias '*'
