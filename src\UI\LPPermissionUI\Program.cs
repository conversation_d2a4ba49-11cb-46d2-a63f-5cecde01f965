﻿
using LPPermission.UI.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Azure.Identity;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using LPPermission.UI.Services.Interface;
using LPPermission.UI.Models;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.ApplicationInsights;
using Microsoft.IdentityModel.Abstractions;

var builder = WebApplication.CreateBuilder(args);

var keyVaultEndpoint = new Uri(Environment.GetEnvironmentVariable("VaultUri")!);
builder.Configuration.AddAzureKeyVault(keyVaultEndpoint, new DefaultAzureCredential());
// Retrieve all secrets from Key Vault and register them as a dictionary
var keyVaultSecrets = builder.Configuration.AsEnumerable()
    .Where(kvp => !string.IsNullOrEmpty(kvp.Value)) // Filter out null or empty values
    .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

builder.Services.AddSingleton(keyVaultSecrets);

// Register the KeyVaultService
builder.Services.AddSingleton<KeyVaultService>();

//var lpPermissionFunctionUrl = builder.Configuration["LPPermissionFunctionURL"];
builder.Services.Configure<ApiSettings>(builder.Configuration.GetSection("ApiSettings"));
// Add Application Insights telemetry
builder.Services.AddApplicationInsightsTelemetry(builder.Configuration["ApplicationInsights:ConnectionString"]);

// Register TelemetryClient explicitly
builder.Services.AddSingleton<TelemetryClient>(provider =>
{
    var telemetryConfiguration = provider.GetRequiredService<TelemetryConfiguration>();
    return new TelemetryClient(telemetryConfiguration);
});



// Replace the obsolete line:  
// builder.Services.AddApplicationInsightsTelemetry(builder.Configuration["ApplicationInsights:ConnectionString"]);  

// With the following updated code:  
builder.Services.AddApplicationInsightsTelemetry(options =>
{
    options.ConnectionString = builder.Configuration["ApplicationInsights:ConnectionString"];
});
// Add services to the container.
builder.Services.AddRazorPages();
builder.Services.AddHealthChecks();
builder.Services.AddServerSideBlazor();
builder.Services.AddTelerikBlazor();
builder.Services.AddHttpClient();
builder.Services.AddScoped<ILoginService, LoginService>();
builder.Services.AddScoped<JwtTokenStorageService>();
builder.Services.AddScoped<IApiService, ApiService>();
builder.Services.AddScoped<AuthService>();
builder.Services.AddScoped<ProtectedSessionStorage>();
builder.Services.AddHttpClient<LogService>();
builder.Services.AddHttpClient<OrchestratorService>();
builder.Services.AddHttpClient<IGeoContainerService, GeoContainerService>(client =>
{
    client.BaseAddress = new Uri(builder.Configuration["ApiBaseUri"]);
});

//builder.Services.AddSingleton<TelemetryClient>(new TelemetryClient());
builder.Services.AddSingleton<LPPermission.UI.Services.Interface.ITelemetryClient>(provider =>
{
    var telemetryConfiguration = provider.GetRequiredService<TelemetryConfiguration>();
    var telemetryClient = new TelemetryClient(telemetryConfiguration);
    return new TelemetryClientWrapper(telemetryClient);
});
    // Wrap TelemetryClient in a custom implementation });</telemetryconfiguration></itelemetryclient>


var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();

app.UseRouting();
app.MapHealthChecks("/health");

app.MapBlazorHub();
app.MapFallbackToPage("/_Host");

app.Run();
