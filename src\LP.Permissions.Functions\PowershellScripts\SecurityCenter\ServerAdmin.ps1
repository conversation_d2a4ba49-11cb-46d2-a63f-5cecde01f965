# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCServerWatchDogMailRecepients {
    <#
    .Synopsis
        Method used to add an e-mail recipient to the watchdog notification
    .DESCRIPTION
        This Method will allow the user to add an e-mail recipient to the watchdog notification
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter EmailAddress will be used to specify the new e-mail address we want to add to the watchdog

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Add-SCServerWatchDogMailRecepients "<EMAIL>"

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("email")] [string]$EmailAddress
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Set-SCServerAdminRelation -RelationName "GenetecServer_Watchdog_EmailRecipients" -RelationId $EmailAddress  
        }
    }  
}

# -----------------------------------------------------------------------------
Function Backup-SCDirectoryDatabase {
    <#
    .Synopsis
        Method used to add to start the backup of the directory database
    .DESCRIPTION
        This Method used to start the backup of the directory database in the configured backup folder
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Backup-SCDirectoryDatabase

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    
    $jsonObject = [ordered]@{} 
    $jsonObject.Add("File", "")
    $jsonBody = $jsonObject | ConvertTo-Json

    $uri = "ServerAdmin/directory_database_backups"

    SCCmdletImplementation $MyInvocation.InvocationName {
        InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Function Disable-SCServerNetworkCard {
    <#
    .Synopsis
        Method used to disable the use of a specific network card (will cause the server to reboot)
    .DESCRIPTION
        This Method is used to disable the use of a specific network card on the server (will cause the server to reboot)
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter NetworkCardInterface is used to select the card to disable
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Backup-SCDirectoryDatabase -NetworkCardInterface '********* - Local Area Connection 3'

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param()
 
    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'NetworkCardInterface'
        $ParameterAlias = 'nic'
       
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $true
        $ParameterAttribute.Position = 1

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        # Generate and set the ValidateSet 
        $cards = Get-SCServerAdmin -RelationName genetecserver_network_networkcards
        $cardNames = New-Object System.Collections.ArrayList
        $cards | foreach{ $index = $cardNames.Add($_.Name)}
        $arrSet = $cardNames
        $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

        # Add the ValidateSet to the attributes collection
        $AttributeCollection.Add($ValidateSetAttribute)

        #add the alias to the attributes collection
        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)
        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $NetworkCardInterface = $PsBoundParameters[$ParameterName]
    }

    process {
        $cards | foreach{ if($_.Name -eq $NetworkCardInterface){ $cardId = $_.Id }}
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Enabled", $false)
        $jsonBody = $jsonObject | ConvertTo-Json

        $uri = "ServerAdmin/GenetecServer_Network_NetworkCards/$cardId" 

        SCCmdletImplementation $MyInvocation.InvocationName {
            InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'PUT' -Body $jsonBody
        }
    } 
}

# -----------------------------------------------------------------------------
Function Enable-SCServerNetworkCard {
    <#
    .Synopsis
        Method used to enable the use of a specific network card (will cause the server to reboot)
    .DESCRIPTION
        This Method is used to enable the use of a specific network card on the server (will cause the server to reboot)
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter NetworkCardInterface is used to select the card to disable
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Enable-SCDirectoryDatabase -NetworkCardInterface '********* - Local Area Connection 3'

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param()
 
    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'NetworkCardInterface'
        $ParameterAlias = 'nic'
       
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $true
        $ParameterAttribute.Position = 1

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        # Generate and set the ValidateSet 
        $cards = Get-SCServerAdmin -RelationName genetecserver_network_networkcards
        $cardNames = New-Object System.Collections.ArrayList
        $cards | foreach{ $index = $cardNames.Add($_.Name)}
        $arrSet = $cardNames
        $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

        # Add the ValidateSet to the attributes collection
        $AttributeCollection.Add($ValidateSetAttribute)

        #add the alias to the attributes collection
        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)
        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $NetworkCardInterface = $PsBoundParameters[$ParameterName]
    }

    process {
        $cards | foreach{ if($_.Name -eq $NetworkCardInterface){ $cardId = $_.Id }}
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Enabled", $true)
        $jsonBody = $jsonObject | ConvertTo-Json

        $uri = "ServerAdmin/GenetecServer_Network_NetworkCards/$cardId" 

        SCCmdletImplementation $MyInvocation.InvocationName {
            InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'PUT' -Body $jsonBody
        }
    } 
}

# -----------------------------------------------------------------------------
Function Get-SCDirectoryDatabaseInformation {
    <#
    .Synopsis
        Method used to retreive the directory database information
    .DESCRIPTION
        Method used to retreive the directory database information
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Get-SCDirectoryDatabaseInformation

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "ServerAdmin/directory_database_status"

    SCCmdletImplementation $MyInvocation.InvocationName {
        InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'Get'
    }
}

# -----------------------------------------------------------------------------
Function Get-SCDirectoryBackups {
    <#
    .Synopsis
        Method used to retreive the available directory backups in the configured backup folder
    .DESCRIPTION
        Method used to retreive the available directory backups in the configured backup folder
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Get-SCDirectoryBackups

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "ServerAdmin/directory_database_backups"
    SCCmdletImplementation $MyInvocation.InvocationName {
        InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'Get'
    }
}

# -----------------------------------------------------------------------------
Function Get-SCDirectoryLicenseDetails {
    <#
    .Synopsis
        Method used to get the license details of the directory
    .DESCRIPTION
        Method used to get the license details of the directory
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Get-SCDirectoryLicenseDetails

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
 
    SCCmdletImplementation $MyInvocation.InvocationName {
        Get-SCServerAdmin -RelationName "Directory_License_Details"
    }
}

# -----------------------------------------------------------------------------
Set-Alias gssa Get-SCServerAdmin
Function Get-SCServerAdmin {
    <#
    .Synopsis
        This method will return all the properties available in the server admin
    .DESCRIPTION
        This method will return all the properties available in the server admin
        Security Center entities contain two types of properties.  The first type are the base properties.  The second type are referred as relations.  
        A relation is a link between the current entity an one or more other entities in the system.  
        To get those relations, you must explicitly request them by providing the relation name.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RelationName parameter represents the name of the relation we want to retreive.  
        Calling Show-SCServerAdminProperties will list all basic properties and all relations.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        #this will get the admin users properties
        Get-SCServerAdmin

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$false)] [alias("rn")][string]$RelationName
    )

    if ( $RelationName ) {
        $uri = "ServerAdmin/$RelationName"
    }
    else {
        $uri = "ServerAdmin/"
    }

    SCCmdletImplementation $MyInvocation.InvocationName {
        InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'Get'
    }
}

# -----------------------------------------------------------------------------
Function Get-SCServerAvailableCertificates {
    <#
    .Synopsis
        Method used to get the available certificate of the currently logged on server admin
    .DESCRIPTION
        Method used to get the available certificate of a server
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Get-SCServerAvailableCertificates

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
 
    SCCmdletImplementation $MyInvocation.InvocationName {
        Get-SCServerAdmin -RelationName "GenetecServer_General_AvailableCertificates"
    }
}

# -----------------------------------------------------------------------------
Function Get-SCServerWatchDogMailRecepients {
    <#
    .Synopsis
        Method used to get the e-mail recipients of the watchdog notification
    .DESCRIPTION
        Method used to get the e-mail recipients of the watchdog notification
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Get-SCServerWatchDogMailRecepients

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
 
    SCCmdletImplementation $MyInvocation.InvocationName {
        Get-SCServerAdmin -RelationName "GenetecServer_Watchdog_EmailRecipients"
    }
}

# -----------------------------------------------------------------------------
Function Get-SCServerNetworkCard {
    <#
    .Synopsis
        Method used to get the available network cards on a server
    .DESCRIPTION
        Method used to get the available network cards on a server
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Get-SCServerNetworkCard

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    SCCmdletImplementation $MyInvocation.InvocationName {
        Get-SCServerAdmin GenetecServer_Network_NetworkCards
    }
}

# -----------------------------------------------------------------------------
Function New-SCServerCertificate {
    <#
    .Synopsis
        Method used to create a new self-signed certificate on the currently logged on server admin
    .DESCRIPTION
        
Method used to to create a new self-signed certificate on the currently logged on server admin

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        New-SCServerCertificate

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
 
    Process {
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("cert", "")
        $jsonBody = $jsonObject | ConvertTo-Json

        $uri = "ServerAdmin/GenetecServer_General_CreateCertificate"

        SCCmdletImplementation $MyInvocation.InvocationName {
            InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias rssar Remove-SCServerAdminRelation
Function Remove-SCServerAdminRelation() {
    <#
    .Synopsis
        This method will remove the relation between two entity
    .DESCRIPTION
        This method will remove the relation between two entity that is represented by the relation name
        Security Center entities contain two types of properties.  The first type are the base properties.  The second type are referred as relations.  
        A relation is a link between the current Entity an one or more other entities in the system.  To get those relations, 
        you must explicitly request them by providing the relation name.
        You can also get the linked entity by providing the relation name and by providing the id of the linked entity in the method.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The EntityId parameter represents the Id of the entity to retreive (The guid representing the entity in the Security Center System)
        You can also pass any entity object that contains an ID as a parameter

        The RelationName parameter represents the name of the relation we want to remove the entity from.  
        Calling Show-SCServerAdminProperties will list all basic properties and all relations.

        The RelationId parameter will represent a the specific entity in the relation we want to remove
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $EmailAddress = Add-SCServerWatchDogMailRecepients "<EMAIL>"

        Remove-SCServerAdminRelation -RelationName "GenetecServer_Watchdog_EmailRecipients" -RelationId $EmailAddress

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param ( [parameter(Mandatory=$true)] [alias("pn")] [string]$RelationName,
            [parameter(Mandatory=$true)] [alias("p")] [string]$RelationId
        )

    $jsonObject = [ordered]@{} 
    $jsonObject.Add("Id", $RelationId)
    $jsonBody = $jsonObject | ConvertTo-Json

    $uri = "ServerAdmin/$RelationName/$RelationId"

    SCCmdletImplementation $MyInvocation.InvocationName {
        InvokeSCServerAdminRestMethod -UriSuffix $uri -Method "DELETE" -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCServerWatchDogMailRecepients {
    <#
    .Synopsis
        Method used to remove an e-mail recipient from the watchdog notification
    .DESCRIPTION
        This Method will allow the user to remove an e-mail recipient to the watchdog notification
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter EmailAddress will be used to specify the e-mail address we want to remove from the watchdog

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $EmailAddress = Add-SCServerWatchDogMailRecepients "<EMAIL>"

        Remove-SCServerWatchDogMailRecepients -EmailAddress $EmailAddress

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("email")] [string]$EmailAddress
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCServerAdminRelation -RelationName "GenetecServer_Watchdog_EmailRecipients" -RelationId $EmailAddress  
        }
    }   
}

# -----------------------------------------------------------------------------
Function Restore-SCDirectoryDatabase{ 
    <#
    .Synopsis
        Method used to start the restore of the directory database
    .DESCRIPTION
        This Method used to start the restore of the directory database
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        the BackupFileName parameter is the full path and file name of the backup file to restore

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Restore-SCDirectoryDatabase -BackupFileName 'C:\SecurityCenterBackup\Directory2_ManualBackup_2015-03-26_15h27min03s.bak'

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] [string]$BackupFileName
    )
    $jsonObject = [ordered]@{} 
    $jsonObject.Add("file", $BackupFileName)
    $jsonBody = $jsonObject | ConvertTo-Json

    $uri = "ServerAdmin/directory_database_restorebackup"

    SCCmdletImplementation $MyInvocation.InvocationName {
        InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias sssa Set-SCServerAdmin
Function Set-SCServerAdmin { 
    <#
    .Synopsis
        Used to update the properties of the Security Center server admin
    .DESCRIPTION
        This method is used to update the properties of the Security Center server admin.  All properties that are not read-only will be updated.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated seperatly by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter ServerAdmin represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myServerAdmin = Get-SCServerAdmin
        $myServerAdmin.GenetecServer_Authentication_ServerAdminLocalOnly = $true
        Set-SCServerAdmin $myServerAdmin

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("sa")] $ServerAdmin
    )

    $jsonBody= BuildSAJasonBody($ServerAdmin)

    #this is to get around the behavior of powershell return variables
    if($jsonBody.count -gt 1) {
        $jsonBody = $jsonBody[($jsonBody.count - 1)]
    }

    $uri = "ServerAdmin/"
         
    if (!$jsonBody) {
        Write-Error "Set-SCServerAdmin : Could not build the property string to update"
    }

    SCCmdletImplementation $MyInvocation.InvocationName { 
        InvokeSCServerAdminRestMethod -UriSuffix $uri -Method "Put" -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias sssar Set-SCServerAdminRelation
Function Set-SCServerAdminRelation() {
    <#
    .Synopsis
        Used to update a relation between the given entity for a specified relation name 
    .DESCRIPTION
        This method is used to update a relation between the given entity for a specified relation name in Security Center.  
        Example.  A user entity contains the relation usergroups that represents which usergroups this user belongs to.
        Calling this method on a user for the usergroup relation and with a usergroup relationId will add the user to this usergroup

        The parameter RelationName represents the name of the relation we want to update

        The parameter RelationId represents the relation we want to link the entity with
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $EmailAddress = Set-SCServerAdminRelation -RelationName "GenetecServer_Watchdog_EmailRecipients" -RelationId "<EMAIL>"

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0)] [alias("pn")] [string]$RelationName,
        [parameter(Mandatory=$true,Position=1)] [alias("p")] [string]$RelationId
        )

    $uri = "ServerAdmin/$RelationName"

    $jsonObject = [ordered]@{} 
    $jsonObject.Add("Id", $RelationId)
    $jsonBody = $jsonObject | ConvertTo-Json

    SCCmdletImplementation $MyInvocation.InvocationName {
        InvokeSCServerAdminRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Set-Alias shssa Show-SCServerAdminConfiguration
Function Show-SCServerAdminProperties {
    <#
    .Synopsis
        This method will show all properties and possible relation of the server admin
    .DESCRIPTION
        This method will show all properties and possible relation of the server admin (the data model, not the actual data).  This method is used
        when you want to know what is available for the server admin
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCServerAdminProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "ServerAdmin/?help=true"

    SCCmdletImplementation $MyInvocation.InvocationName {
    $result = InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'Get'

    if($result.Fields -and $result.Relations)
    {
        $result.Fields
        $result.Relations
    }
    else
    {
        $result
    }
    }
}

# -----------------------------------------------------------------------------
Function Submit-SCLicenseWebActivation {
    <#
    .Synopsis
        This method is used to update the security center license using the web activation
    .DESCRIPTION
        This method is used to update the security center license using the web activation
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        Submit-SCLicenseWebActivation -SystemId DEM-150227-632432 -Password 2ESi8qDs
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("si")] [string]$SystemId,
        [parameter(Mandatory=$true)] [alias("p")] [string]$Password
    )
    $jsonObject = [ordered]@{} 
    $jsonObject.Add("SystemId", $SystemId)
    $jsonObject.Add("Password", $Password)
    $jsonBody = $jsonObject | ConvertTo-Json

    $uri = "ServerAdmin/Directory_License_WebActivation"

    SCCmdletImplementation $MyInvocation.InvocationName {
        InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Function Update-SCDirectoryDatabase{ 
    <#
    .Synopsis
        Method used to upgrade the directory database
    .DESCRIPTION
        This Method is used to upgrade the directory database to the latest version
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Update-SCDirectoryDatabase

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $jsonObject = [ordered]@{} 
    $jsonObject.Add("File", "")
    $jsonBody = $jsonObject | ConvertTo-Json

    $uri = "ServerAdmin/directory_database_upgrade"

    SCCmdletImplementation $MyInvocation.InvocationName {
        InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
    }
}

# -----------------------------------------------------------------------------
Function Test-SCServerCertificate {
    <#
    .Synopsis
        Method used to test a certificate on the currently logged on server admin
    .DESCRIPTION
        Method used to test a certificate on the currently logged on server admin

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CertificatThumbprint parameter represents the thumbprint of the certificat to test on the server
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $certs = Get-SCServerAvailableCertificates

        Test-SCServerCertificate $certs[0].Thumbprint

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
 
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("ct")] [string]$CertificatThumbprint
    )
 
    Begin {
    }

    Process {
        $jsonObject = [ordered]@{} 
        $jsonObject.Add("thumbprint", $CertificatThumbprint)
        $jsonBody = $jsonObject | ConvertTo-Json

        $uri = "ServerAdmin/GenetecServer_General_TestCertificate"

        SCCmdletImplementation $MyInvocation.InvocationName {
            InvokeSCServerAdminRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
        }
    } 
}

Export-ModuleMember -Function '*-*' -Alias '*'