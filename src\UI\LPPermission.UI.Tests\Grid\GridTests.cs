﻿using Bunit;
using LPPermission.UI.Models;
using LPPermission.UI.Services.Interface;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Moq;
//using Telerik.Blazor.BUnit.JustMock.Common;
using RichardSzalay.MockHttp;
using Telerik.Blazor.Components;

namespace LPPermission.UI.Tests.Grid
{
    public class GridTest : TestContext
    {
        private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
        private readonly HttpClient _httpClient;
        private readonly Mock<IOptions<ApiSettings>> _apiSettingsMock;
        private  string GetPermissionUrl;
        private readonly IConfiguration _configuration;
        

        public GridTest()
        {
            _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
            _httpClient = new HttpClient(_httpMessageHandlerMock.Object);
            _apiSettingsMock = new Mock<IOptions<ApiSettings>>();
            _apiSettingsMock.Setup(x => x.Value).Returns(new ApiSettings { GetPermissionUrl = GetPermissionUrl });

            Services.AddSingleton(_httpClient);
            Services.AddSingleton(_apiSettingsMock.Object);
            Services.AddTelerikBlazor();

            // Initialize _configuration in the constructor to avoid CS0191 error  
            var configurationBuilder = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            _configuration = configurationBuilder.Build();

            // Retrieve the URL from configuration  
            GetPermissionUrl = _configuration["ApiSettings:GetPermissionUrl"];
        }
        [Fact]
        public void Grid_should_render_inside_telerikrootcomponent_without_privileges()
        {

            // Arrange
            var mockHttpClient = new MockHttpMessageHandler();
            var httpClient = new HttpClient(mockHttpClient);

            var mockApiSettings = new Mock<IOptions<ApiSettings>>();
            mockApiSettings.Setup(x => x.Value).Returns(new ApiSettings
            {
                GetPermissionUrl = GetPermissionUrl
            });


            RegisterTelemetryClientMock();

            Services.AddSingleton(httpClient); // Inject HttpClient
            Services.AddSingleton(mockApiSettings.Object); // Inject properly configured IOptions<ApiSettings>

            // Act
            JSInterop.SetupVoid("TelerikBlazor.initFilterMenu", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initComponentLoaderContainer", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initColumnReorderable", _ => true);
            var rendered = RenderComponent<TelerikRootComponent>(parameter =>
                                parameter.AddChildContent<GridWrapper>());

            // Assert
            var grid = rendered.FindComponent<TelerikGrid<PrivilegeDto>>();
            Assert.NotNull(grid);
        }
        
        [Fact]
        public void Grid_should_render_with_privileges()
        {
            // Arrange
            var privileges = new List<PrivilegeDto>
        {
            new PrivilegeDto { GeoName = "Geo1", UserGroupName = "Group1", MenuName = "Menu1", PrivilegeName = "Privilege1", StateName = "State1" },
            new PrivilegeDto { GeoName = "Geo2", UserGroupName = "Group2", MenuName = "Menu2", PrivilegeName = "Privilege2", StateName = "State2" }
        };

            var mockHttpClient = new MockHttpMessageHandler();
            mockHttpClient.When(GetPermissionUrl)
                .Respond("application/json", System.Text.Json.JsonSerializer.Serialize(privileges));

            var httpClient = new HttpClient(mockHttpClient);

            Services.AddSingleton(httpClient); // Inject HttpClient
            Services.AddSingleton(_apiSettingsMock.Object); // Inject IOptions<ApiSettings>
            RegisterTelemetryClientMock();

            // Act
            JSInterop.SetupVoid("TelerikBlazor.initFilterMenu", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initComponentLoaderContainer", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initColumnReorderable", _ => true);

            var rendered = RenderComponent<TelerikRootComponent>(parameter =>
                                parameter.AddChildContent<GridWrapper>(gridParams =>
                                    gridParams.Add(p => p.Privileges, privileges)));

            // Assert
            var grid = rendered.FindComponent<TelerikGrid<PrivilegeDto>>();
            Assert.NotNull(grid);
            Assert.Equal(2, grid.Instance.Data.Count());
        }

        [Fact]
        public void Grid_should_render_correct_header()
        {
            // Arrange
            var privileges = new List<PrivilegeDto>
        {
            new PrivilegeDto { GeoName = "Geo1", UserGroupName = "Group1", MenuName = "Menu1", PrivilegeName = "Privilege1", StateName = "State1" }
        };

            var mockHttpClient = new MockHttpMessageHandler();
            mockHttpClient.When(GetPermissionUrl)
                .Respond("application/json", System.Text.Json.JsonSerializer.Serialize(privileges));

            var httpClient = new HttpClient(mockHttpClient);

            Services.AddSingleton(httpClient); // Inject HttpClient
            Services.AddSingleton(_apiSettingsMock.Object); // Inject IOptions<ApiSettings>
            RegisterTelemetryClientMock();


            // Act
            JSInterop.SetupVoid("TelerikBlazor.initFilterMenu", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initComponentLoaderContainer", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initColumnReorderable", _ => true);

            var rendered = RenderComponent<TelerikRootComponent>(parameter =>
                                parameter.AddChildContent<GridWrapper>(gridParams =>
                                    gridParams.Add(p => p.Privileges, privileges)));

            // Assert
            var markup = rendered.Markup;
            Assert.Contains("grid-header", markup); // Optional: Check if the class exists in the markup

        }
        [Fact]
        public void Grid_should_render_with_empty_privileges()
        {
            // Arrange
            var privileges = new List<PrivilegeDto>();

            var mockHttpClient = new MockHttpMessageHandler();
            mockHttpClient.When(GetPermissionUrl)
                .Respond("application/json", System.Text.Json.JsonSerializer.Serialize(privileges));

            var httpClient = new HttpClient(mockHttpClient);
            RegisterTelemetryClientMock();

            Services.AddSingleton(httpClient);
            Services.AddSingleton(_apiSettingsMock.Object);

            // Act
            // Act
            JSInterop.SetupVoid("TelerikBlazor.initFilterMenu", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initComponentLoaderContainer", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initColumnReorderable", _ => true);

            var rendered = RenderComponent<TelerikRootComponent>(parameter =>
                                parameter.AddChildContent<GridWrapper>(gridParams =>
                                    gridParams.Add(p => p.Privileges, privileges)));

            // Assert
            var grid = rendered.FindComponent<TelerikGrid<PrivilegeDto>>();
            Assert.NotNull(grid);
            Assert.Empty(grid.Instance.Data);
        }
        [Fact]
        public void Grid_should_handle_pagination_correctly()
        {
            // Arrange
            var privileges = new List<PrivilegeDto>();
            for (int i = 1; i <= 50; i++)
            {
                privileges.Add(new PrivilegeDto { GeoName = $"Geo{i}", UserGroupName = $"Group{i}", MenuName = $"Menu{i}", PrivilegeName = $"Privilege{i}", StateName = $"State{i}" });
            }

            var mockHttpClient = new MockHttpMessageHandler();
            mockHttpClient.When(GetPermissionUrl)
                .Respond("application/json", System.Text.Json.JsonSerializer.Serialize(privileges));

            var httpClient = new HttpClient(mockHttpClient);

            Services.AddSingleton(httpClient);
            Services.AddSingleton(_apiSettingsMock.Object);
            RegisterTelemetryClientMock();

            // Act
            JSInterop.SetupVoid("TelerikBlazor.initFilterMenu", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initComponentLoaderContainer", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initColumnReorderable", _ => true);

            var rendered = RenderComponent<TelerikRootComponent>(parameter =>
                                parameter.AddChildContent<GridWrapper>(gridParams =>
                                    gridParams.Add(p => p.Privileges, privileges)));

            // Assert
            var grid = rendered.FindComponent<TelerikGrid<PrivilegeDto>>();
            Assert.NotNull(grid);
            Assert.Equal(50, grid.Instance.Data.Count());
        }
        [Fact]
        public void Grid_should_render_with_special_characters_in_privileges()
        { 
            // Retrieve the URL from configuration
            // Arrange
            var privileges = new List<PrivilegeDto>
        {
            new PrivilegeDto { GeoName = "Geo@1", UserGroupName = "Group#1", MenuName = "Menu$1", PrivilegeName = "Privilege%1", StateName = "State&1" }
        };

            var mockHttpClient = new MockHttpMessageHandler();
            mockHttpClient.When(GetPermissionUrl)
                .Respond("application/json", System.Text.Json.JsonSerializer.Serialize(privileges));

            var httpClient = new HttpClient(mockHttpClient);

            Services.AddSingleton(httpClient);
            Services.AddSingleton(_apiSettingsMock.Object);
            RegisterTelemetryClientMock();

            // Act
            JSInterop.SetupVoid("TelerikBlazor.initFilterMenu", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initComponentLoaderContainer", _ => true);
            JSInterop.SetupVoid("TelerikBlazor.initColumnReorderable", _ => true);
            var rendered = RenderComponent<TelerikRootComponent>(parameter =>
                                parameter.AddChildContent<GridWrapper>(gridParams =>
                                    gridParams.Add(p => p.Privileges, privileges)));

            // Assert
            var grid = rendered.FindComponent<TelerikGrid<PrivilegeDto>>();
            Assert.NotNull(grid);
            Assert.Single(grid.Instance.Data);
            Assert.Contains("Geo@1", rendered.Markup);
        }
        private void RegisterTelemetryClientMock()
        {
            var telemetryClientMock = new Mock<ITelemetryClient>();
            Services.AddSingleton(telemetryClientMock.Object);
        }
    }
}
