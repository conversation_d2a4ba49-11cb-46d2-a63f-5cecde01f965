# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Get-SCPermit {
    <#
    .Synopsis
        This method will return all the properties of the permit represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the permit represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The permitId parameter represents the Id of the permit to retrieve (The guid representing the permit in the Security Center System)
        You can also pass any permit object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $permit = Get-SCPermit -PermitId $permitId

        #Exit the session
        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $PermitId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $PermitId           
        }
    }
}
# -----------------------------------------------------------------------------
Function New-SCPermit {
    <#
    .Synopsis
        Method used to create a new permit with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new permit with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new permit upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $permitid = New-SCPermit -n "MyNewPermit"

        Exit-SCSession     
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Permits
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCPermit {
    <#
    .Synopsis
        Will remove the permit represented by the provided permitId parameter from Security Center
    .DESCRIPTION
        This method will permanently remove the specified permit from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The permitId parameter represents the permit to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Remove-SCPermit -PermitId $cred.Id

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $PermitId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $PermitId
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCPermit() {
    <#
    .Synopsis
        Used to update the properties of a permit in Security Center
    .DESCRIPTION
        This method is used to update the properties of a permit to Security Center.  All properties that are not read-only will be updated.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separatelytly by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter permit represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new permit.
        $myPermit = New-SCPermit -Name "MyPermit" | Get-SCPermit
        # Set the permit file path.
        $myPermit.Path = "D:\permit\test.txt"
        # Update the entity.
        Set-SCPermit -Permit $myPermit 

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("p")] $Permit
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Permit
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCPermitProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a permit
    .DESCRIPTION
        This method will list the supported properties and relation of an permit (the data model, not the actual data).  This method is used
        when you want to know what is available for a given permit
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCPermitProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiPermit" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'