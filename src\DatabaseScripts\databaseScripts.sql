
/****** Object:  Table [dbo].[__EFMigrationsHistory]    Script Date: 4/8/2025 2:52:54 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sysobjects WHERE name = '[dbo].[__EFMigrationsHistory]' AND xtype = 'U')
BEGIN
    DROP TABLE [dbo].[__EFMigrationsHistory];
END;
GO
CREATE TABLE [dbo].[__EFMigrationsHistory](
	[MigrationId] [nvarchar](150) NOT NULL,
	[ProductVersion] [nvarchar](32) NOT NULL,
 CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY CLUSTERED 
(
	[MigrationId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UserLogin]    Script Date: 4/8/2025 2:52:54 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sysobjects WHERE name = 'UserLogin' AND xtype = 'U')
BEGIN
    DROP TABLE [dbo].[UserLogin];
END;
GO
CREATE TABLE [dbo].[UserLogin](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Username] [nvarchar](50) NOT NULL,
	[Password] [nvarchar](255) NOT NULL,
	[IsActive] [bit] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Containers]    Script Date: 4/8/2025 2:52:54 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sysobjects WHERE name = 'Containers' AND xtype = 'U')
BEGIN
    DROP TABLE [dbo].[Containers];
END;
GO
CREATE TABLE [dbo].[Containers](
	[container_id] [int] NOT NULL,
	[name] [varchar](50) NULL,
	[geo_id] [int] NOT NULL,
	[description] [varchar](50) NULL,
	[identifier] [varchar](10) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[container_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Geos]    Script Date: 4/8/2025 2:52:54 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sysobjects WHERE name = 'Geos' AND xtype = 'U')
BEGIN
    DROP TABLE [dbo].[Geos];
END;
GO
CREATE TABLE [dbo].[Geos](
	[geo_id] [int] NOT NULL,
	[name] [varchar](50) NULL,
	[description] [varchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[geo_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[States]    Script Date: 4/8/2025 2:52:54 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sysobjects WHERE name = 'States' AND xtype = 'U')
BEGIN
    DROP TABLE [dbo].[States];
END;
GO
CREATE TABLE [dbo].[States](
	[state_id] [int] IDENTITY(1,1) NOT NULL,
	[name] [varchar](50) NOT NULL,
	[description] [varchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[state_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Stores]    Script Date: 4/8/2025 2:52:54 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sysobjects WHERE name = 'Stores' AND xtype = 'U')
BEGIN
    DROP TABLE [dbo].[Stores];
END;
GO
CREATE TABLE [dbo].[Stores](
	[store_id] [int] NOT NULL,
	[container_id] [int] NULL,
	[server_name] [varchar](50) NULL,
	[name] [varchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[store_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StoreUserGroups]    Script Date: 4/8/2025 2:52:54 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sysobjects WHERE name = 'StoreUserGroups' AND xtype = 'U')
BEGIN
    DROP TABLE [dbo].[StoreUserGroups];
END;
GO
CREATE TABLE [dbo].[StoreUserGroups](
	[store_user_group_id] [int] NOT NULL,
	[store_id] [int] NOT NULL,
	[user_group_id] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[store_user_group_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UserGroups]    Script Date: 4/8/2025 2:52:54 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sysobjects WHERE name = 'UserGroups' AND xtype = 'U')
BEGIN
    DROP TABLE [dbo].[UserGroups];
END;
GO
CREATE TABLE [dbo].[UserGroups](
	[user_group_id] [int] NOT NULL,
	[description] [varchar](50) NULL,
	[parent_user_group_id] [int] NULL,
	[name] [varchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[user_group_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[UserLogin] ADD  DEFAULT ((1)) FOR [IsActive]
GO
ALTER TABLE [dbo].[Containers]  WITH CHECK ADD FOREIGN KEY([geo_id])
REFERENCES [dbo].[Geos] ([geo_id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[Stores]  WITH CHECK ADD FOREIGN KEY([container_id])
REFERENCES [dbo].[Containers] ([container_id])
ON DELETE SET NULL
GO
ALTER TABLE [dbo].[StoreUserGroups]  WITH CHECK ADD FOREIGN KEY([store_id])
REFERENCES [dbo].[Stores] ([store_id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[StoreUserGroups]  WITH CHECK ADD FOREIGN KEY([user_group_id])
REFERENCES [dbo].[UserGroups] ([user_group_id])
GO
ALTER TABLE [dbo].[UserGroups]  WITH CHECK ADD FOREIGN KEY([parent_user_group_id])
REFERENCES [dbo].[UserGroups] ([user_group_id])
GO
/****** Object:  StoredProcedure [CORP\gir00075].[GetAllPermissions]    Script Date: 4/8/2025 2:52:56 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sysobjects WHERE name = 'GetAllPermissions' AND xtype = 'P')
BEGIN
    DROP TABLE [dbo].[GetAllPermissions];
END;
GO
CREATE PROCEDURE [dbo].[GetAllPermissions]
AS
BEGIN
    SELECT 
    u.Name AS UserGroupName,
    m.name AS MenuName,
    pr.Name AS PrivilegeName,
    st.Name AS StateName,
    pr.genetec_ref AS GenetecReference
FROM Permissions p
JOIN Privileges pr ON p.permission_id = pr.privilege_id
JOIN Menu m ON pr.menu_id = m.menu_id
JOIN UserGroups u ON p.user_group_id = u.user_group_id
JOIN States st ON p.state_id = st.state_id
END
GO
