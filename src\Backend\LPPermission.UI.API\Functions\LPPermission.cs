using CsvHelper;
using CsvHelper.Configuration;
using LPPermission.BLL.Interfaces;
using LPPermission.BLL.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Globalization;
using System.Text;
using LPPermission.Models;
using LPPermission.UI.API.Helper;
using LPPermission.BLL.Model;

namespace LPPermission.UI.API.Functions
{
    public class LPPermission
    {
        private readonly ILogger<LPPermission> _logger;
        private readonly IUpload _upload; 
        private readonly IUser _user;
        private readonly IJwtTokenGenerateService _jwtTokengenerate;

        public LPPermission(ILogger<LPPermission> logger, IUpload upload, IUser user,IJwtTokenGenerateService jwtTokenGenerateService)
        {
            _logger = logger;
            _upload = upload;
            _user = user;
            _jwtTokengenerate = jwtTokenGenerateService;
        }
       
       /// <summary>
       /// This function handles the login request for LP Permission UI application.
       /// </summary>
       /// <param name="req"></param>
       /// <returns></returns>
        [Function("Login")]
        public async Task<IActionResult> Login1([HttpTrigger(AuthorizationLevel.Function, "post")] HttpRequest req)
        {
            _logger.LogInformation("Processing LOGIN request.");

            // Read the request body (username and password)
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var loginRequest = JsonConvert.DeserializeObject<LoginRequest>(requestBody);

            if (loginRequest == null || string.IsNullOrEmpty(loginRequest.Username) || string.IsNullOrEmpty(loginRequest.Password))
            {
                return new BadRequestObjectResult("Invalid login request.");
            }

            // Validate the user credentials (username and password)
            bool isUserValid = await _user.IsUserValidAsync(loginRequest.Username, loginRequest.Password);

            if (isUserValid)
            {
                var logindata = new LoginModel
                {
                    UserName = loginRequest.Username,
                    Password = loginRequest.Password
                };
                var token = _jwtTokengenerate.GenerateAccessToken(logindata);
                return new OkObjectResult(token); // Return the result if successful
            }
            else
            {
                return new UnauthorizedObjectResult("Invalid username, password, or account is inactive.");
            }
        }

        /// <summary>
        /// This function handles the refresh token request for LP Permission UI application. 
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [Function("RefreshToken")]
        private async Task<IActionResult> RefreshToken([HttpTrigger(AuthorizationLevel.Function, "post")] HttpRequest req)
        {
            // Read the body of the incoming request as a string
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            //var refresh = JsonConvert.DeserializeObject<LoginModel>(requestBody);
            var token = _jwtTokengenerate.RefreshAccessToken(requestBody);
            return new OkObjectResult(token);
        }

        /// <summary>
        /// This function handles the request to get all permissions for the LP Permission UI application.
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns> <summary>
        [Function("GetPermission")]
        public async Task<IActionResult> GetPermission([HttpTrigger(AuthorizationLevel.Function, "get")] HttpRequest req)
        {
            _logger.LogInformation("Processing GET PERMISSION request.");

            try
            {
                // Retrieve all permissions
                var permissions = await _upload.GetAllPermissions();

                if (permissions == null || !permissions.Any())
                {
                    return new NotFoundObjectResult("No permissions found.");
                }

                return new OkObjectResult(permissions);
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while retrieving permissions: {ex.Message}");
                return new ObjectResult($"An error occurred: {ex.Message}") { StatusCode = StatusCodes.Status500InternalServerError };
            }
        }

        /// <summary>
        ///  <summary>
        /// This function handles the request to get all geos for the LP Permission UI application.
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>//  
        [Function("GetGeos")]
        public async Task<IActionResult> GetGeos([HttpTrigger(AuthorizationLevel.Function, "get", Route = "geos")] HttpRequest req)
        {
            var geos = await _upload.GetAllGeosAsync();
            return new OkObjectResult(geos.Select(g => new { g.GeoId, g.Name, g.Description }));
        }

        /// <summary>
        /// This function will return the containers based on the Geoid 
        /// </summary>
        /// <returns></returns>
        [Function("GetContainers")]
        public async Task<IActionResult> GetContainers([HttpTrigger(AuthorizationLevel.Function, "get", Route = "containers/{geoId:int}")] HttpRequest req, int geoId)
        {
            var containers = await _upload.GetContainersByGeoIdAsync(geoId);
            return new OkObjectResult(containers.Select(c => new { c.ContainerId, c.ContainerName, c.Description }));
        }

        /// <summary>
        /// This function will process the CSV file and store the data in the database
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns> <summary>
        
        [Function("Save")]
        public async Task<IActionResult> Save([HttpTrigger(AuthorizationLevel.Function, "post")] HttpRequest req)
        {

            var file = req.Form.Files["files"];
            var geoName = req.Form["geoName"].ToString();
            var containerName = req.Form["containerName"].ToString();
            if (file != null)
            {
                try
                {
                    if (file == null)
                    {
                        _logger.LogError("No file uploaded.");
                        return new BadRequestObjectResult("No file uploaded.");
                    }

                    var fileExtension = Path.GetExtension(file.FileName).ToLower();
                    if (fileExtension != ".csv")
                    {
                        _logger.LogError("Uploaded file is not a CSV.");
                        return new BadRequestObjectResult("The uploaded file is not a CSV.");
                    }

                    // Process the CSV file
                    var records = new List<CsvRecord>();

                    using (var reader = new StreamReader(file.OpenReadStream(), Encoding.UTF8))
                    {
                        var fileContent = await reader.ReadToEndAsync();
                        var sections = fileContent.Split(new[] { "SectionDelimiter" }, StringSplitOptions.RemoveEmptyEntries);

                        foreach (var section in sections)
                        {
                            using (var sectionReader = new StringReader(section))
                            using (var csv = new CsvReader(sectionReader, new CsvConfiguration(CultureInfo.InvariantCulture)
                            {
                                HasHeaderRecord = true,
                                IgnoreBlankLines = true,
                                TrimOptions = CsvHelper.Configuration.TrimOptions.Trim,
                                HeaderValidated = null, // Ignore header validation errors
                                MissingFieldFound = null, // Ignore missing field errors
                            }))
                            {
                                // Register the custom ClassMap to handle the "Inherited from" header
                                csv.Context.RegisterClassMap<CsvRecordMap>();

                                // Try to read the CSV into records
                                records.AddRange(csv.GetRecords<CsvRecord>().ToList());
                            }
                        }

                        await _upload.ProcessCsvRecordsAsync(records, geoName, containerName); // Pass the geoName and containerName as needed
                    }

                    return new StatusCodeResult(StatusCodes.Status201Created);
                }
                catch (Exception ex)
                {
                    return new ObjectResult($"Upload failed. Exception: {ex.Message}") { StatusCode = StatusCodes.Status500InternalServerError };
                }
            }

            return new EmptyResult();
        }
    }
}
