using Microsoft.AspNetCore.Components;
using Telerik.Blazor.Components;
using Microsoft.AspNetCore.Components.Rendering; // Add this using directive

namespace LPPermission.UI.Tests.Pages
{
    public class MockMainLayout : LayoutComponentBase
    {
        protected override void BuildRenderTree(RenderTreeBuilder builder)
        {
            builder.OpenComponent<TelerikRootComponent>(0); // Add TelerikRootComponent
            builder.CloseComponent();
            builder.OpenRegion(1); // Use OpenRegion to render child content
            builder.AddContent(2, Body); // Render child content using the Body property
            builder.CloseRegion();
        }
    }
}
