﻿using System;
using System.Threading.Tasks;

namespace SetLPPermissions.Utilities
{
    public class CircuitBreaker:ICircuitBreaker
    {
        private readonly int _failureThreshold;
        private readonly TimeSpan _resetTime;
        private int _failureCount;
        private DateTime _lastFailureTime;
        private bool _isOpen;

        public CircuitBreaker(int failureThreshold, TimeSpan resetTime)
        {
            _failureThreshold = failureThreshold;
            _resetTime = resetTime;
            _failureCount = 0;
            _isOpen = false;
        }

        public async Task<T> ExecuteAsync<T>(Func<Task<T>> action)
        {
            if (_isOpen && DateTime.UtcNow - _lastFailureTime < _resetTime)
            {
                throw new CircuitBreakerException("Circuit breaker is open. Try again later");
            }

            try
            {
                var result = await action();
                _failureCount = 0;
                _isOpen = false;
                return result;
            }
            catch (Exception)
            {
                _failureCount++;
                _lastFailureTime = DateTime.UtcNow;
                if (_failureCount >= _failureThreshold)
                {
                    _isOpen = true;
                }
                throw;
            }
        }
    }

    public class CircuitBreakerException : Exception
    {
        public CircuitBreakerException(string message) : base(message) { }
    }
}
