﻿@page "/"

@using LPPermission.UI.Services
@using LPPermission.UI.Services.Interface
@using TelerikUI_App.Models
@inject NavigationManager Navigation
@inject ILogger<Index> Logger
@inject IApiService ApiService
@inject ILoginService LoginService
@inject JwtTokenStorageService JwtTokenStorageService
@inject HttpClient Http
@inject IJSRuntime JSRuntime
@inject AuthService authser

<!-- Link to external CSS file -->
<head>
    <link href="css/Styles.css" rel="stylesheet" />
</head>


<div class="form-container">
    <!-- Logo with reduced size -->

    <div class="logo">
        <img src="TJX_Logo.svg.png" alt="Logo" />
    </div>

    <EditForm Model="@person" OnValidSubmit="@HandleValidSubmit" class="k-form">
        <DataAnnotationsValidator />
        <fieldset>
            <legend style="font-size: 16px; font-weight: bold; text-align: center; color: #85151e;">Login</legend>

            <div class="form-group">
                <label for="FNameTb">
                    <span>User Name <span class="k-required">*</span></span>
                </label>
                <TelerikTextBox @bind-Value="@person.UserName" Id="FNameTb" />
            </div>

            <div class="form-group">
                <label for="LNameTb">
                    <span>Password <span class="k-required">*</span></span>
                </label>
                <TelerikTextBox Placeholder="Enter Password"
                                Password="HidePassword"
                                @bind-Value="@person.Password" />
            </div>
        </fieldset>
           

        <!-- Validation Summary to show all validation messages globally -->
        <ValidationSummary />

        <div class="text-right">
            <TelerikButton ButtonType="@ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" class="k-button">Login</TelerikButton>
        </div>
    </EditForm>
    </div>


