﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Get-SCFederationEvents {
    <#
    .Synopsis
        This method will return all the supported events of the given federation role
    .DESCRIPTION
        This method will return all the supported events of the given federation role
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RoleId parameter represents the Id of the federation role to retrieve the events from (The guid representing the federation in the Security Center System)
        You can also pass any role object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myFed = Get-SCRoles -Type Federation 
        Get-SCFederationEvents $myFed

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "events"  
        }
    } 
}

# -----------------------------------------------------------------------------
Function Get-SCFederationRoles {
    <#
    .Synopsis
        This method will get all the federation roles
    .DESCRIPTION
        This method is used when the user wants to get all the federation roles.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $federations = Get-SCFederationRoles
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    Begin {
    }
    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCRoles -t Federation -Filter All
        }
    }   
}

# -----------------------------------------------------------------------------
Function Set-SCFederationEvent {
    <#
    .Synopsis
        This method will configure one of the supported events of the given federation role
    .DESCRIPTION
        This method will configure one of the supported events of the given federation role
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RoleId parameter represents the Id of the federation role to configure the events (The guid representing the federation in the Security Center System)
        You can also pass any role object that contains an ID as a parameter

        The parameter EventName is there to select which event we want to enable\disable

        The parameter EvenEnabled is there to enable\disable the selected event
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myFed = Get-SCRoles -Type Federation 
        Set-SCFederationEvent $myFed[0] -EventName LprOffloadFailed -EvenEnabled $true

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("en")] [string]$EventName = "",
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("ee")] [bool]$EventEnabled = $false
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/Events/$EventName"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Enabled", $EventEnabled)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Put" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias goefr Get-SCOEFRedirectors
Function Get-SCOEFRedirectors() {
    <#
    .Synopsis
        This method will get the OEF redirectors that are associated with an OEF role
    .DESCRIPTION
        This method allows you to get an array of the redirector records associated with the OEF role. It only works for Omnicast Enhanced Federation.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myOEFRoles = Get-SCRoles -Type Omnicast
        foreach ($OEF in $myOEFroles)
        {
            get-SCOEFRedirectors -RoleId $OEF.Id
        }

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

       [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {

            if(!$RoleId){
                throw "Invalid parameter Id" 
            }

            if(((Get-SCEnum -Enum RoleTypeIds) | where {$_.id -eq ((Get-SCEntity -EntityId $RoleId).RoleTypeId)}).RoleTypeId -eq 'Omnicast')
            {
               return (Get-SCEntity -EntityId $RoleId -RelationName "redirectors")
            }
            else
            {
                throw "Attempted to get redirectors of non-OEF role."
            }
        }
    }
}  

# -----------------------------------------------------------------------------
Set-Alias aoefr Add-SCOEFRedirector
Function Add-SCOEFRedirector() {
    <#
    .Synopsis
        This method will add new the OEF redirectors to an OEF role
    .DESCRIPTION
        This method allows you to add redirector records OEF role. It only works for Omnicast Enhanced Federation. It requires the id of the role and an object with all of the mandatory properties.

        ServerId                     : b12c09a1-8b7d-4329-bec6-16c797014709
        OverrideGatewayAddress       : True
        GatewayAddress               : test
        GatewayUsername              : ac1
        GatewayPassword              : ****
        IncomingUdpPortRange         : @{Min=12000; Max=15000}
        MaximumLiveStreams           : 0
        MaximumPlaybackStreams       : 0
        MulticastInterface           : 005056990FF8
        RtspPort                     : 556
        IsAdvanceRedirectionStrategy : False
        RedirectionStrategyList      : []

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

           $newredir = '' | Select-Object ServerId, OverrideGatewayAddress, GatewayAddress, GatewayUsername, GatewayPassword, IncomingUdpPortRange, MaximumLiveStreams, MaximumPlaybackStreams, MulticastInterface, RtspPort, IsAdvanceRedirectionStrategy, RedirectionStrategyList
           $newredir.ServerId = 'b12c09a1-8b7d-4329-bec6-16c797014709'
           $newredir.OverrideGatewayAddress       = $True
           $newredir.GatewayAddress               = 'test'
           $newredir.GatewayUsername              = 'ac1'
           $newredir.GatewayPassword              = '1234'
           $newredir.IncomingUdpPortRange         = @{Min=12000; Max=15000}
           $newredir.MaximumLiveStreams           = 0
           $newredir.MaximumPlaybackStreams       = 0
           $newredir.MulticastInterface           = '005056990FF8'
           $newredir.RtspPort                     = '556'
           $newredir.IsAdvanceRedirectionStrategy = $false
           $newredir.RedirectionStrategyList      = @()
           Add-SCOEFRedirector -RoleId $OEF.Id -Object $redir -forcePassword

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

       [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("Redirector")] $Object,
        [alias("Force")] [switch]$forcePassword = $false
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {

            if(!$RoleId){
                throw "Invalid parameter Id" 
            }
            if (!$ForcePassword -and $Object.GatewayPassword -eq '****')
            {
                $Object.PSObject.Properties.Remove('GatewayPassword')
                write-warning "Password property was set to '****' and so will not be updated"
            }

            if(((Get-SCEnum -Enum RoleTypeIds) | where {$_.id -eq ((Get-SCEntity -EntityId $RoleId).RoleTypeId)}).RoleTypeId -eq 'Omnicast')
            {
                $jsonBody = $Object | ConvertTo-Json
                write-debug $jsonBody
                $uri = "entities/$RoleId/redirectors"
                InvokeSCRestMethod -UriSuffix $uri -Method 'POST' -Body $jsonBody
            }
            else
            {
                throw "Attempted to add redirectors to a non-OEF role"
            }

        }
    }
}  


# -----------------------------------------------------------------------------
Set-Alias soefr Set-SCOEFRedirector
Function Set-SCOEFRedirector() {
<#
    .Synopsis
        This method will modify existing OEF redirectors that are associated with an OEF role
    .DESCRIPTION
        This method allows you to change the settings of the redirectors associated with the OEF role. It only works for Omnicast Enhanced Federation. It expects the new settings to be provided
        as an object, similarly to the add-SCOEFRedirector cmdlet

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myOEFRoles = Get-SCRoles -Type Omnicast
        foreach ($OEF in $myOEFroles)
        {
            $redir = get-SCOEFRedirectors -RoleId $OEF.Id
            $redir.GatewayUsername = 'test2'
            Set-SCOEFRedirector -RoleId $OEF.Id -Object $redir
        }

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>   

       [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("Redirector")] $Object,
        [alias("Force")] [switch]$forcePassword = $false
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {

            if(!$RoleId){
                throw "Invalid parameter Id" 
            }

            if (!$ForcePassword -and $Object.GatewayPassword -eq '****')
            {
                $Object.PSObject.Properties.Remove('GatewayPassword')
                write-warning "Password property was set to '****' and so will not be updated"
            }

            if(((Get-SCEnum -Enum RoleTypeIds) | where {$_.id -eq ((Get-SCEntity -EntityId $RoleId).RoleTypeId)}).RoleTypeId -eq 'Omnicast')
            {
                $jsonBody = $Object | ConvertTo-Json
                write-debug $jsonBody
                $ServerId = $Object.ServerId
                $uri = "entities/$RoleId/redirectors/$ServerId"
                InvokeSCRestMethod -UriSuffix $uri -Method 'PUT' -Body $jsonBody
            }
            else
            {
                throw "Attempted to update redirectors but role is not OEF role"
            }
        }
    }
}  

# -----------------------------------------------------------------------------
Set-Alias roefr Remove-SCOEFRedirector
Function Remove-SCOEFRedirector() {
<#
    .Synopsis
        This method will remove a redirector from an OEF role.
    .DESCRIPTION
        This method allows you to remove the redirectors associated with the OEF role based on the server id of the redirector. It only works for Omnicast Enhanced Federation. 

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myOEFRoles = Get-SCRoles -Type Omnicast
        foreach ($OEF in $myOEFroles)
        {
            remove-SCOEFRedirector -RoleId $OEF.Id -ServerId 'b12c09a1-8b7d-4329-bec6-16c797014709'
        }

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>    

       [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true)] [alias("RedirectorServerId")] $ServerId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {

            if(!$RoleId){
                throw "Invalid parameter Role Id" 
            }

            if(!$ServerId){
                throw "Invalid parameter Server Id" 
            }
            if(((Get-SCEnum -Enum RoleTypeIds) | where {$_.id -eq ((Get-SCEntity -EntityId $RoleId).RoleTypeId)}).RoleTypeId -eq 'Omnicast')
            {
                $uri = "entities/$RoleId/redirectors/$ServerId"
                Write-Debug $uri
                InvokeSCRestMethod -UriSuffix $uri -Method 'DELETE'
            }
            else
            {
                throw "Attempted to remove redirectors but role is not OEF role"
            }
        }
    }
} 



Export-ModuleMember -Function '*-*' -Alias '*'
