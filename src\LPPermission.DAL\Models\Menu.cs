﻿using System;
using System.Collections.Generic;

namespace LPPermission.DAL.Models;

public partial class Menu
{
    public int MenuId { get; set; }

    public int? ParentId { get; set; }

    public string? Name { get; set; }

    public string? Description { get; set; }

    public int MenuLevel { get; set; }

    public virtual ICollection<Menu> InverseParent { get; set; } = new List<Menu>();

    public virtual Menu? Parent { get; set; }

    public virtual ICollection<Privilege> Privileges { get; set; } = new List<Privilege>();
}
