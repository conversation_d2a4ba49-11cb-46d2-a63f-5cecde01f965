﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCFailOverDirectoryServer {
    <#
    .Synopsis
        This method will add a server to the Directory servers list of the Directory Manager role
    .DESCRIPTION
        This method is used when the user wants to add a server to the directory servers list of the Directory Manager role in order to configure
        the directory fail-over. Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter ServerId is used to specify the server to add

        The parameter Port is optional (5500)

        The parameter IsGatewayOnly is optional (false by default) and is used to specify is the server acts as a gateway only

        The parameter IsDisasterRecovery is optional (false by default) and is used to specify is the server acts as a disaster recovery only
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Add-SCFailOverDirectoryServer -ServerId $serverToAdd
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [int] $Port=5500, [bool] $IsGatewayOnly=$false, [bool] $IsDisasterRecovery=$false,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ServerId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $ServerId
            $FailOverDirectory = Get-SCRoles -t DirectoryFailover
            $FailOverDirectoryId = $FailOverDirectory.Id

            $uri = "Entities/$FailOverDirectoryId/Servers/"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", $sid)
            $jsonObject.Add("Port", $Port)
            $jsonObject.Add("IsGatewayOnly", $IsGatewayOnly)
            $jsonObject.Add("IsDisasterRecovery", $IsDisasterRecovery)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCDirectoryManagerRole {
    <#
    .Synopsis
        This method will get the Directory Manager role
    .DESCRIPTION
        This method is used when the user wants to get Directory Manager role
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $FailOverRole = Get-SCDirectoryManagerRole
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    Begin {
    }
    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCRoles -t DirectoryFailover -Filter All
        }
    }   
}

# -----------------------------------------------------------------------------
Function Get-SCDirectoryManagerValidationKey {

    <#
    .Synopsis
		Invoke the command to get the Validation Key from multiple servers.
        
    .DESCRIPTION
		This method is used to generate a validation Key from multiple servers, since it is not possible to do it from the ServerAdmin.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

		The RoleId parameter represents either the Guid or the object containing the Directory Manager Role.
		The ServersIds parameter represents a System.Collections.ArrayList containing either the Guids of the servers or an object that contains a property (.Id) which returns the Id of the object.

    .EXAMPLE

        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password"

        # Getting the first Role id of type Directory Failover.
        $RoleId = Get-SCRoles -Type DirectoryFailover | Select-Object -first 1 -ExpandProperty Id

        # Getting all the servers id.
        $ServersIds = Get-SCEntities -Type Servers | Select-Object -ExpandProperty Id
        # Just in case there's only 1 server, we convert the string to an array.
        if( $ServersIds.GetType().Name -eq 'String' ) { $ServersIds = @($ServersIds.ToString()) }


        # Calling the function.
        # If you want, you can also copy the result to the clipboard : (Get-ValidationKey -RoleId $RoleId -ServersIds $ServersIds) | clip
		Get-ValidationKey -RoleId $RoleId -ServersIds $ServersIds
        
        # Exiting the Security Center Session
        Exit-SCSession
        
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true)] [String]$RoleId,
        [parameter(Mandatory=$true)] [System.Collections.ArrayList]$ServersIds
    )
    
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {

            # Specifying the method
            $method = 'POST';

            # Building Path
            $rId = GetIdFromObject $RoleId
            $uri = "Entities/$rId/ValidationKey"

            # Building Body
            $jsonObject = [ordered]@{}
            $servers = New-Object System.Collections.ArrayList
            Foreach ($serverId in $ServersIds)
            {
                $id = GetIdFromObject $serverId
                $servers.Add($id)
            }
            $jsonObject.Add("Servers", $servers)
            $jsonBody = $jsonObject | ConvertTo-Json

            # Invoking the rest method
            InvokeSCRestMethod -UriSuffix $uri -Method $method -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCFailOverDirectoryServer {
    <#
    .Synopsis
        This method will get the Directory servers list of the Directory Manager role
    .DESCRIPTION
        This method is used when the user wants to get directory servers list of the Directory Manager role
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Get-SCFailOverDirectoryServer
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    Begin {
    }
    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $FailOverDirectory = Get-SCRoles -t DirectoryFailover
            Get-SCEntity -EntityId $FailOverDirectory -RelationName "Servers"  
        }
    }   
}

# -----------------------------------------------------------------------------
Function Remove-SCFailOverDirectoryServer {
    <#
    .Synopsis
        This method will remove a server from Directory servers list of the Directory Manager role
    .DESCRIPTION
        This method is used when the user wants to remove a server from the directory servers list of the Directory Manager role in order to configure
        the directory fail-over. Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter ServerId is used to specify the server to remove

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Remove-SCFailOverDirectoryServer -ServerId $serverToAdd
        
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param([parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ServerId)

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $ServerId
            $FailOverDirectory = Get-SCRoles -t DirectoryFailover
            $FailOverDirectoryId = $FailOverDirectory.Id
            $uri = "Entities/$FailOverDirectoryId/Servers/$sid"
            InvokeSCRestMethod -UriSuffix $uri -Method "DELETE"
        }
    }
}

# -----------------------------------------------------------------------------
Function Set-SCDirectoryManagerRole() {
    <#
    .Synopsis
        Used to update the properties of a Directory Manager role in Security Center.
    .DESCRIPTION
        This method is used to update the properties of a Directory Manager role to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The parameter DirectoryManagerRole contains all the properties that will be updated to security Center.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $role = Get-SCDirectoryManagerRole
        $role.ForceDirectoryOnMaster = $true
        Set-SCDirectoryManagerRole $role

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("dmr")] $DirectoryManagerRole
    )
    Begin {
    }

    Process {
    
        if ($Role)
        {
            $Role.PSObject.Properties.Remove('Disabled')
        }

        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $DirectoryManagerRole
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssfods Set-SCFailOverDirectoryServer
Function Set-SCFailOverDirectoryServer() {
    <#
    .Synopsis
        Used to update the properties of an failover directory server in Security Center

    .DESCRIPTION
        This method is used to update the properties of an failover directory server to Security Center.  

        The parameter ServerId is used to specify the server to add

        The parameter Port is optional (5500)

        The parameter IsGatewayOnly is optional (false by default) and is used to specify is the server acts as a gateway only

        The parameter IsDisasterRecovery is optional (false by default) and is used to specify is the server acts as a disaster recovery only

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $server = Get-SCFailOverDirectoryServer
        Set-SCFailOverDirectoryServer $server[0].Id -IsGatewayOnly $true

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    Param(
        [int] $Port=5500, [bool] $IsGatewayOnly=$false, [bool] $IsDisasterRecovery=$false,
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ServerId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $sid = GetIdFromObject $ServerId
            $FailOverDirectory = Get-SCRoles -t DirectoryFailover
            $FailOverDirectoryId = $FailOverDirectory.Id

            $uri = "Entities/$FailOverDirectoryId/Servers/$sid"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Port", $Port)
            $jsonObject.Add("IsGatewayOnly", $IsGatewayOnly)
            $jsonObject.Add("IsDisasterRecovery", $IsDisasterRecovery)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "PUT" -Body $jsonBody
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'
