using System;
using System.Diagnostics;
using System.IO;
using System.Collections.Generic;
using Newtonsoft.Json;
using System.Text.Json.Serialization;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Extensibility;
using SetLPPermissions.Utilities;
using FunctionApp.LPPermissions.Services.interfaces;
using Microsoft.Extensions.Logging;
using LPPermissionStoreSetup.Models;
using Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;
using System.Text.Json.Nodes;
using LPPermission.BLL;
using LPPermission.DAL.Models;
using UserGroup = LPPermissionStoreSetup.Models.Group;

namespace FunctionApp.LPPermissions.Services
{
    public class StoreConnectionService : IStoreConnectionService
    {
        private readonly string _storeConnectScriptPath;
        private readonly string _getPermissionDataScriptPath;
        private readonly string _updatePermissionScriptPath;
        private readonly string _logFilePath;
        private readonly string _ipJsonFilePath;
        private readonly string _outputFilePath;
        private readonly string _tempJsonFilePath;
        private readonly string _permissionsJsonFilePath;
        private readonly TelemetryClient telemetryClient;
        private readonly int maxRetries;
        private readonly CircuitBreaker _circuitBreaker;
        private readonly IGetPermission _getPermission;
        private object userGroup;


        public StoreConnectionService(string _storeConnectScriptPath, string _getPermissionDataScriptPath, string _updatePermissionScriptPath, string _logFilePath, string _ipJsonFilePath, string _outputFilePath, TelemetryClient telemetryClient, IGetPermission getPermission, int maxRetries = 3)
        {

            this._storeConnectScriptPath = _storeConnectScriptPath;
            this._getPermissionDataScriptPath = _getPermissionDataScriptPath;
            this._updatePermissionScriptPath = _updatePermissionScriptPath;
            this._logFilePath = _logFilePath;
            this._ipJsonFilePath = _ipJsonFilePath;
            this._outputFilePath = _outputFilePath;
            this._tempJsonFilePath = Path.Combine(Path.GetTempPath(), "permissions.json");
            this.telemetryClient = telemetryClient;
            this.maxRetries = maxRetries;
            this._circuitBreaker = new CircuitBreaker(5, TimeSpan.FromMinutes(5)); // Example threshold and reset time
            _getPermission = getPermission;
        }



        public bool Connect(string ipAddress, string username, string credential)
        {
            // Read IP addresses from JSON file
            List<string> ipAddresses = JsonConvert.DeserializeObject<List<string>>(File.ReadAllText(_ipJsonFilePath));

            foreach (string ip in ipAddresses)
            {
                int retryCount = 0;
                bool success = false;

                while (retryCount < maxRetries && !success)
                {
                    try
                    {
                        _circuitBreaker.ExecuteAsync(async () => { await ConnectToStoresAsync(ip, username, credential); return Task.CompletedTask; }).Wait();
                        success = true;
                    }
                    catch (CircuitBreakerException ex)
                    {
                        LogToFile(_logFilePath, $"Circuit breaker is open for IP: {ip}. {ex.Message}");
                        telemetryClient.TrackEvent("CircuitBreakerOpen", new Dictionary<string, string>
                                        {
                                            { "IPAddress", ip },
                                            { "Attempt", (retryCount + 1).ToString() }
                                        });
                        break;
                    }
                    catch (Exception ex)
                    {
                        LogToFile(_logFilePath, $"Attempt {retryCount + 1} for store IP: {ip} failed: {ex.Message}");
                        telemetryClient.TrackEvent("StoreConnectFailed", new Dictionary<string, string>
                                        {
                                            { "IPAddress", ip },
                                            { "Attempt", (retryCount + 1).ToString() },
                                            { "ErrorMessage", ex.Message }
                                        });
                        retryCount++;
                        if (retryCount < maxRetries)
                        {
                            LogToFile(_logFilePath, $"Retrying store IP: {ip}...");
                            telemetryClient.TrackEvent("RetryingStoreConnect", new Dictionary<string, string>
                                            {
                                                { "IPAddress", ip },
                                                { "Attempt", (retryCount + 1).ToString() }
                                            });
                        }
                    }
                }

                if (!success)
                {
                    LogToFile(_logFilePath, $"Failed to process store IP: {ip} after {maxRetries} attempts.");
                    telemetryClient.TrackEvent("StoreConnectFailure", new Dictionary<string, string>
                                    {
                                        { "IPAddress", ip }
                                    });
                }
            }

            // Delete the store_ips.json file after processing all IP addresses
            try
            {
                if (File.Exists(_ipJsonFilePath))
                {
                    File.Delete(_ipJsonFilePath);
                    LogToFile(_logFilePath, $"Deleted IP JSON file: {_ipJsonFilePath}");
                    telemetryClient.TrackEvent("IPJsonFileDeleted", new Dictionary<string, string>
                                    {
                                        { "FilePath", _ipJsonFilePath }
                                    });
                }
            }
            catch (Exception ex)
            {
                LogToFile(_logFilePath, $"Failed to delete IP JSON file: {ex.Message}");
                telemetryClient.TrackException(ex);
            }

            // Exit the application
            Environment.Exit(0);
            return true;
        }

        /// <summary>
        /// Connects to a store using the provided IP address, username, and credential,
        /// and executes a PowerShell script to update permissions on the target store.
        /// Logs the process and tracks telemetry events for success or failure.
        /// </summary>
        /// <param name="ipAddress">The IP address of the target store.</param>
        /// <param name="username">The username used to connect to the store.</param>
        /// <param name="credential">The credential used for authentication.</param>
        /// <returns>A task representing the asynchronous operation.</returns>

        private async Task ConnectToStoresAsync(string ipAddress, string username, string credential)
        {
            LogToFile(_logFilePath, $"Running Update Permission script for IP: {ipAddress}...");
            // Run StoreConnect.ps1 for each IP address
            string storeConnectCommand = $"powershell.exe -NoProfile -ExecutionPolicy Bypass -File \"{_updatePermissionScriptPath}\" -_permissionsJsonFilePath \"{_permissionsJsonFilePath}\" -IPAddress \"{ipAddress}\" -Username \"{username}\" -Credential \"{credential}\" -_logFilePath \"{_logFilePath}\"";
            ProcessStartInfo storeConnectProcessStartInfo = new ProcessStartInfo("cmd.exe", "/c " + storeConnectCommand)
            {
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using (Process storeConnectProcess = new Process { StartInfo = storeConnectProcessStartInfo })
            {
                try
                {
                    storeConnectProcess.Start();
                    string storeConnectOutput = await storeConnectProcess.StandardOutput.ReadToEndAsync();
                    string storeConnectError = await storeConnectProcess.StandardError.ReadToEndAsync();
                    storeConnectProcess.WaitForExit();

                    // Log when the PowerShell script finishes execution
                    LogToFile(_logFilePath, $"PowerShell script execution completed for IP: {ipAddress}. Exit Code: {storeConnectProcess.ExitCode}");

                    if (storeConnectProcess.ExitCode != 0)
                    {
                        throw new Exception(storeConnectError);
                    }

                    LogToFile(_logFilePath, $"Processed store IP: {ipAddress}");
                    telemetryClient.TrackEvent("StoreConnectSucceeded", new Dictionary<string, string>
                            {
                                { "IPAddress", ipAddress }
                            });
                }
                catch (TaskCanceledException ex)
                {
                    LogToFile(_logFilePath, $"Task was canceled for IP: {ipAddress}. {ex.Message}");
                    telemetryClient.TrackEvent("TaskCanceled", new Dictionary<string, string>
                            {
                                { "IPAddress", ipAddress },
                                { "ErrorMessage", ex.Message }
                            });
                    throw;
                }
            }
        }

        private void LogToFile(string filePath, string message)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(filePath, true))
                {
                    writer.WriteLine($"{DateTime.Now}: {message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to write to log file: {ex.Message}");
                telemetryClient.TrackException(ex);
            }
        }
        /// <summary>
        /// Sets LP permissions for a specified store by retrieving permissions from the database,
        /// exporting them to a temporary JSON file, and invoking an external powershell script to apply the permissions through ConnectToStoresAsync method.
        /// </summary>
        /// <param name="ipAddress">The IP address of the target store.</param>
        /// <param name="username">The username used to connect to the store.</param>
        /// <param name="credential">The credential used for authentication.</param>
        /// <returns>
        /// A list of <see cref="PermissionDto"/> objects representing the database permissions that should be compared to the store and set,
        /// or <c>null</c> if the operation failed.
        /// </returns>
        /// 
        public async Task<List<PermissionDto>> SetLPPermissions(string ipAddress, string username, string credential)
        {
            LogToFile(_logFilePath, "Starting SetLPPermissions...");

            // Create temporary file paths for the JSON inputs
            string _permissionsJsonFilePath = Path.Combine(Path.GetTempPath(), "permissions.json");

            try
            {
                // 1. Get the standard permissions from the database
                LogToFile(_logFilePath, "Fetching permissions from the database...");
                var permissions = (await _getPermission.GetAllPermissions()).ToList();
                LogToFile(_logFilePath, $"{permissions.Count} permissions retrieved from the database.");

                // 2. Serialize and write the permissions to a temporary JSON file
                LogToFile(_logFilePath, $"Exporting permissions to JSON file: {_permissionsJsonFilePath}...");
                string permissionsJson = JsonConvert.SerializeObject(permissions, Formatting.Indented);
                await File.WriteAllTextAsync(_permissionsJsonFilePath, permissionsJson);
                LogToFile(_logFilePath, $"Permissions successfully exported to JSON file: {_permissionsJsonFilePath}.");

                // 3. Verify that the JSON file exists
                if (!File.Exists(_permissionsJsonFilePath))
                {
                    string errorMessage = $"Permissions JSON file not found at: {_permissionsJsonFilePath}. Aborting operation.";
                    LogToFile(_logFilePath, errorMessage);
                    telemetryClient.TrackEvent("PermissionsJsonMissing", new Dictionary<string, string>
            {
                { "FilePath", _permissionsJsonFilePath }
            });
                    return null;
                }

                // 4. Ensure the store is connected
                LogToFile(_logFilePath, $"Connecting to store at IP: {ipAddress}...");
                await ConnectToStoresAsync(ipAddress, username, credential);
                LogToFile(_logFilePath, $"Successfully connected to store at IP: {ipAddress}.");

                // 5. (Optional) Pass the JSON file to an external script or process
                // Example: If you need to pass the JSON file to a PowerShell script
                string updatePermissionsCommand = $"powershell.exe -NoProfile -ExecutionPolicy Bypass -File \"{_updatePermissionScriptPath}\" -IPAddress \"{ipAddress}\" -Username \"{username}\" -Credential \"{credential}\" -_logFilePath \"{_logFilePath}\" -PermissionsJsonFile \"{_permissionsJsonFilePath}\"";

                ProcessStartInfo processStartInfo = new ProcessStartInfo("cmd.exe", "/c " + updatePermissionsCommand)
                {
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using (Process process = new Process { StartInfo = processStartInfo })
                {
                    process.Start();
                    string output = await process.StandardOutput.ReadToEndAsync();
                    string error = await process.StandardError.ReadToEndAsync();
                    process.WaitForExit();

                    if (process.ExitCode != 0)
                    {
                        LogToFile(_logFilePath, $"SetLPPermissions script failed. Error: {error}");
                        telemetryClient.TrackEvent("SetLPPermissionsFailed", new Dictionary<string, string>
                {
                    { "IPAddress", ipAddress },
                    { "ErrorMessage", error }
                });
                        return null;
                    }

                    LogToFile(_logFilePath, $"SetLPPermissions script succeeded. Output: {output}");
                }

                return permissions;
            }
            catch (Exception ex)
            {
                LogToFile(_logFilePath, $"An error occurred in SetLPPermissions: {ex.Message}");
                telemetryClient.TrackException(ex);
                throw;
            }
            finally
            {
                // Clean up the temporary JSON file
                if (File.Exists(_permissionsJsonFilePath))
                {
                    File.Delete(_permissionsJsonFilePath);
                    LogToFile(_logFilePath, $"Temporary JSON file deleted: {_permissionsJsonFilePath}");
                }
            }
        }

    }
}