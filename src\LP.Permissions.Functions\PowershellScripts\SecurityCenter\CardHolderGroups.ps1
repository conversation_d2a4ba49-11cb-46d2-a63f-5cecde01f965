# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Add-SCCardholderGroupMembers {
    <#
    .Synopsis
        Method used to add a member to the given cardholder group

    .DESCRIPTION
        This Method will allow the user to add a security center cardholder under a specified security center cardholder group
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CardholderGroupId will be used to specify the cardholder we want to add under the cardholder group
        The parameter MemberId represents the Id of the cardholder we want to add to the cardholder group

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewCardHolder = New-SCCardHolder -Name "MyNewCardHolder" | Get-SCCardholder
        $myNewRestCardholderGroup = New-SCCardHolderGroup -Name "MyNewRestCardholderGroup" | Get-SCCardholderGroup

        Add-SCCardholderGroupMembers -CardholderGroupId $myNewRestCardholderGroup -MemberId $myNewCardHolder

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderGroupId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("mid")] $MemberId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $mid = GetIdFromObject $MemberId
            Set-SCEntityRelation -EntityId $CardholderGroupId -RelationName "members" -RelationId $mid   
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias gschg Get-SCCardholderGroup
Function Get-SCCardholderGroup {
    <#
    .Synopsis
        This method will return all the properties of the cardholder group represented by the ID
		
    .DESCRIPTION
        This method will return all the basic properties of the cardholder group represented by the ID.
		For object properties that represent a relation with other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CardHolderGroupId parameter represents the Id of the cardholder group to retreive (The guid representing the cardholder group in the Security Center System)
        You can also pass any cardholder group object that contains an ID as a parameter.
		
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewCardholderGroup = New-SCCardHolderGroup -Name "MyNewCardholderGroup"
        Get-SCCardholderGroup $myNewCardholderGroup

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardHolderGroupId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CardHolderGroupId   
        }
    }
}

# -----------------------------------------------------------------------------
Function Get-SCCardholderGroupMembers {
    <#
    .Synopsis
        Method used to get all the members of the given cardholder group

    .DESCRIPTION
        This Method will allow the user to get all members of the given cardholder group
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CardholderGroupId will be used to specify the cardholder group we want to get the members from

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewCardholder = New-SCCardholder -Name "MyNewRestCardholder" | Get-SCCardholder
        $myNewRestCardholderGroup = New-SCCardholderGroup -Name "MyNewRestCardholderGroup" | Get-SCCardholderGroup

        Add-SCCardholderCardholderGroups -CardholderId $myNewCardholder -CardholderGroupId $myNewRestCardholderGroup
        Get-SCCardholderGroupMembers -CardholderGroupId $myNewRestCardholderGroup

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderGroupId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CardholderGroupId -RelationName "members"
        }  
    }  
}

# -----------------------------------------------------------------------------
Set-Alias nschg New-SCCardholderGroup
Function New-SCCardholderGroup {
    <#
    .Synopsis
        Method used to create a new cardholder group with the provided name

    .DESCRIPTION
        This Method will allow the user to create a new security center cardholder group with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new cardholder group upon creation 

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        New-SCCardholderGroup -Name "MyNewCardholderGroup"
        
        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Cardholdergroups
        } 
    }
}

# -----------------------------------------------------------------------------
Set-Alias rschg Remove-SCCardholderGroup
Function Remove-SCCardholderGroup {
    <#
    .Synopsis
        Will remove the cardholder group represented by the provided CardholderGroupId from Security Center

    .DESCRIPTION
        This method will permantly remove the specified cardholder group from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CardholderGroupId parameter represents the cardholder group to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $group = New-SCCardholderGroup -Name "MyNewCardholderGroup"
        Remove-SCCardholderGroup $group

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderGroupId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $CardholderGroupId
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCCardholderGroupMembers {
    <#
    .Synopsis
        Method used to remove a member of the given cardholder group

    .DESCRIPTION
        This Method will allow the user to remove a security center member from a specified security center cardholder group
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CardholderGroupId will be used to specify the cardholder group we want to remove the member from

        The parameter MemberId represents the Id of the member we want to remove from the cardholder group.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewCardholder = New-SCCardholder -Name "MyNewRestCardholder" | Get-SCCardholder
        $myNewRestCardholderGroup = New-SCCardholderGroup -Name "MyNewRestCardholderGroup" | Get-SCCardholderGroup

        Add-SCCardholderGroupMembers -CardholderGroupId $myNewRestCardholderGroup -MemberId $myNewCardholder

        Remove-SCCardholderGroupMembers -CardholderGroupId $myNewRestCardholderGroup -MemberId $myNewCardholder

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CardholderGroupId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("mid")] $MemberId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $mid = GetIdFromObject $MemberId
            Remove-SCEntityRelation -EntityId $CardholderGroupId -RelationName "members" -RelationId $mid   
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias sschg Set-SCCardholderGroup
Function Set-SCCardholderGroup() {
    <#
    .Synopsis
        Used to update the properties of a cardholder group in Security Center.

    .DESCRIPTION
        This method is used to update the properties of a cardholder group to Security Center.  All properties that are not read-only will be updated.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated seperately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter CardholderGroup represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new cardholder group
        $newchg = New-SCCardholderGroup -Name "MyCardholderGroup" | gschg
        $newchg.Description = "test"
        
        Set-SCCardholderGroup -CardholderGroup $newchg

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("chg")] $CardholderGroup
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $CardholderGroup
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCCardholderGroupProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a cardholder group

    .DESCRIPTION
        This method will list the supported properties and relation of a cardholder group (the data model, not the actual data).  This method is used
        when you want to know what is available for a given cardholder group.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCCardholderGroupProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiCardholderGroup" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

