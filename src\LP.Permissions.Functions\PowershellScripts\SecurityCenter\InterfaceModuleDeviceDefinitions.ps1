# -----------------------------------------------------------------------------
Function Get-SCInterfaceModuleDeviceDefinition {
    <#
    .Synopsis
        This method will return all the properties of the interface module device definition represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the interface module device definition represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The InterfaceModuleId parameter represents the Id of the interface module device definition to retrieve (The guid representing the interface module device definition in the Security Center System)
        You can also pass any interface module device definition object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method

		#You can get the entities of type InterfaceModuleDeviceDefinitions by calling Get-SCEntities -Type InterfaceModuleDeviceDefinitions
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        Get-SCInterfaceModuleDeviceDefinition -InterfaceModuleId $interfaceModuleId

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $InterfaceModuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $InterfaceModuleId   
        }
    } 
}

# -----------------------------------------------------------------------------
Function Get-SCInterfaceModuleDeviceDefinitionDevices{
    <#
    .Synopsis
        Method used to get all devices of the given interface module device definition
    .DESCRIPTION
        This Method will allow the user to get all devices of the given interface module device definition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Get-SCInterfaceModuleDeviceDefinitionDevices -InterfaceModuleId $interfaceModuleId
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $InterfaceModuleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {   
            Get-SCEntity -EntityId $InterfaceModuleId -RelationName "Devices"
        }
    }  
}

Export-ModuleMember -Function '*-*' -Alias '*'