﻿# ==========================================================================
# Copyright (C) 1989-2019 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias asiu Add-SCIntrusionUnit
Function Add-SCIntrusionUnit {
    <#
    .Synopsis
        Method used to add an intrusion Unit to an intrusion role in Security Center
    .DESCRIPTION
        This method is used to add an intrusion unit to the provided intrusion role in Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RoleId parameter represents the Id of the intrusion Manager we want to add the unit to.

	    The UnitName parameter represents the name of the intrusion Unit we want to add.

        The Address parameter represents the ip address of the intrusion unit we want to add.

        The Port parameter represents the port of the intrusion unit we want to add.

        The Username parameter reprents the username to use when connecting to the intrusion unit.

        The Password parameter reprents the password to use when connecting to the intrusion unit.

        The Settings represent the xml representation of the panel setting we want to add

        The ExtensionId represent the guid of the panel extention type we want to add (DMP, Honeywell Legacy, generic simulation, etc)

        The CreateUnconnected represent whether the panel is created in the IntrusionManager even if the panel is offline
    .EXAMPLE
		# Call the following in order to import all the modules. Otherwise, Enter-SCSession will not work.
        # Import-Module ".\SDK\REST\Powershell Scripts\Modules\SecurityCenter\SecurityCenter.psm1" -Force
        # Import-Module .\SecurityCenter.psm1 -Force
        
		# Must enter a valid Security Center session before calling any method.
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

         $result = Add-SCIntrusionUnit -RoleId 6ff73fd4-ddf3-4bba-8da9-ce339f2c230c -UnitName "DMP panel name" -Address "127.0.0.1" -Port 42671 
                                        -Settings "<DmpEnrollmentXml xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                                    <AccountNumber>12345</AccountNumber>
                                                    <EventPort>0</EventPort>
                                                    <RemoteKey>542</RemoteKey>
                                                    </DmpEnrollmentXml>" 
                                        -ExtensionId "A8D16923-2081-41A3-986D-6AA94A8E41C0"  #This is DMP extension Guid
                                        -CreateUnconnected $True

        Exit-SCSession

    .NOTES        
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
	[parameter(Mandatory=$true,Position=1)] [alias("Id")] $RoleId,
	[parameter(Mandatory=$true,Position=2)] [alias("name")] [string]$UnitName,
	[parameter(Mandatory=$true,Position=3)] [alias("addr")][string]$Address,
    [parameter(Mandatory=$true,Position=4)] [alias("p")][int]$Port,
    [parameter(Mandatory=$true,Position=5)] [alias("s")][string]$Settings,
    [parameter(Mandatory=$true,Position=6)] [alias("ext")][string]$ExtensionId,
    [parameter(Mandatory=$true,Position=7)] [alias("con")][bool]$CreateUnconnected
	)
 
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            #Intrusion Role 
            # $roleguid = GetIdFromObject $RoleId
            $roleguid = $RoleId
            
            Write-Host $roleguid

            $sess = GetSession
            $uri = "Entities/IntrusionUnits/EnrollIntrusionUnit"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("ExtensionId",$ExtensionId )
            $jsonObject.Add("Role", $roleguid)
			$jsonObject.Add("AlarmPanelName", $UnitName)
            $jsonObject.Add("Address", $Address)
            $jsonObject.Add("Port", $Port)
            $jsonObject.add("Settings",$Settings)
            # This parameter allow to add the intrusionUnit in the intrusionRole even if the unit is not connected/online and available
            $jsonObject.Add("CreateEvenWhenUnconnected","$CreateUnconnected")
            $jsonBody = $jsonObject | ConvertTo-Json -Compress

            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}
# -----------------------------------------------------------------------------
Set-Alias gsiu Get-SCIntrusionUnit
Function Get-SCIntrusionUnit {
    <#
    .Synopsis
        This method will return all the properties of the Intrusion unit represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the Intrusion unit represented by the ID. For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Import-Module .\SecurityCenter.psm1 -Force

        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $IntrusionUnits = Get-SCEntities -t IntrusionUnits -f All

        Get-SCIntrusionUnit -IntrusionUnitId $IntrusionUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $IntrusionUnitId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $IntrusionUnitId 
        }
    }         
}

# -----------------------------------------------------------------------------
Set-Alias ssintu Set-SCIntrusionUnit
Function Set-SCIntrusionUnit {
    <#
    .Synopsis
        Used to update the properties of a Intrusion  unit in Security Center.
    .DESCRIPTION
        This method is used to update the properties of a Intrusion unit to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods.
        
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
        The parameter Unit represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $IntruUnits = Get-SCEntities -t IntrusionUnits -f All
        $IntruUnits[0].Description = "test"
        Set-SCIntrusionUnit $IntruUnits[0]
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("acu")] $Unit
    )
    Begin {
    }
    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Set-SCEntity -EntityToSet $Unit
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias rsiu Remove-SCIntrusionUnit
Function Remove-SCIntrusionUnit {
    <#
    .Synopsis
        Method used to permanently remove the given Intrusion unit
    .DESCRIPTION
        This method is used to permanently remove the given Intrusion unit
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $IntrusionUnits = Get-SCEntities -t IntrusionUnits -f All

        Remove-SCIntrusionUnit -IntrusionUnitId $IntrusionUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $IntrusionUnitId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $IntrusionUnitId
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssiup Show-SCIntrusionUnitProperties
Function Show-SCIntrusionUnitProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an Intrusion unit
    .DESCRIPTION
        This method will list the supported properties and relation of an Intrusion unit (the data model, not the actual data). This method is used
        when you want to know what is available for a given Intrusion unit
    .EXAMPLE
        #
        Import-Module .\SecurityCenter.psm1 -Force

        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword "" 
 
        Show-SCIntrusionUnitProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiIntrusionUnit" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}

# -----------------------------------------------------------------------------
Set-Alias gsiu Get-SCIntrusionUnitArea
Function Get-SCIntrusionUnitArea {
    <#
    .Synopsis
        This method will return all the properties of the Intrusion unit area represented by the ID
    .DESCRIPTION
        This method will return all the areas of the Intrusion unit represented by the ID. 
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Import-Module .\SecurityCenter.psm1 -Force

        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $IntrusionUnits = Get-SCEntities -t IntrusionUnits -f All

        Get-SCIntrusionUnitArea -IntrusionUnitId $IntrusionUnits[0].Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $IntrusionUnitId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $IntrusionUnitId -RelationName IntrusionAreas
        }
    }         
}

Export-ModuleMember -Function '*-*' -Alias '*'