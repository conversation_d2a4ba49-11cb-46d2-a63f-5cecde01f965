﻿@using TelerikUI_App.Models
<style>
    .nav-container {
        display: flex;
        align-items: center;
        padding: 10px;
        background-color: #f4f7fb;
    }

    .nav-logo {
        margin-left: 20px;
    }

    .nav-menu {
        flex-grow: 1;
    }
</style>
<div class="nav-container">
    <div class="nav-menu">
<TelerikMenu Data="@MenuItems">
    <ItemTemplate Context="item">
        @{
            <NavLink href="@item.Url"
                     target="@(IsInternalPage(item.Url) ? "" : "_blank")"
                     class="k-nav k-link k-menu-link"
                     ActiveClass="k-state-active"
                     Match="@(item.Url == "/" ? NavLinkMatch.All : NavLinkMatch.Prefix)">
                @item.Text
            </NavLink>
        }
    </ItemTemplate>
</TelerikMenu>
    </div>
    @* <div class="nav-logo">
        <img src="~/tjxlogo2.png" alt="Logo" width="100" />
    </div> *@
</div>


@code {
    List<MenuItem> MenuItems { get; set; }

    MenuItem SelectedMenuItem { get; set; }

    bool IsInternalPage(string url)
    {
        if (string.IsNullOrEmpty(url)) return false;

        var protocols = new string[] { "//", "http://", "https://" };
        return !protocols.Any(p => url.StartsWith(p.ToLower()));
    }

    protected override void OnInitialized()
    {
        MenuItems = new List<MenuItem>()
    {
           
            new MenuItem()
            {
                Text = "File Upload",
                Url = "/UploadFile"
            },
            new MenuItem()
            {
                Text = "User Group and Permissions",
                Url = "/Grid"
            },
             new MenuItem()
            {
                Text = "Logout",
                Url = "/",
            },
        };
    }
}