using LPPermission.DAL;
using Microsoft.EntityFrameworkCore;
using LPPermission.DAL.Repositories;
using LPPermission.DAL.Models;
using NUnit.Framework;

namespace LPPermission.DAL.Tests
{
    public class Tests
    {
        private DbContextOptions<LppermissionsContext> _dbContextOptions;
        [SetUp]
        public void Setup()
        {
            _dbContextOptions = new DbContextOptionsBuilder<LppermissionsContext>()
                .UseInMemoryDatabase(databaseName: "TestDatabase")
                .Options;
        }

        

        

        [Test]
        public async Task AddAsync_ShouldAddEntityTest()
        {
            // Arrange
            var context = new LppermissionsContext(_dbContextOptions);
            var repository = new GenericRepository<Geo>(context);
            var entity = new Geo { GeoId = 1, Name = "Test Entity" };

            // Act
            await repository.AddAsync(entity);
            var result = await context.Set<Geo>().FindAsync(1); // Check if entity was added

            // Assert
            Assert.IsNotNull(result); // Check if entity exists in the database
            Assert.AreEqual("Test Entity", result.Name); // Verify entity's property
        }
    }
}