﻿using Moq;
using LPPermission.BLL.Services;
using LPPermission.DAL.Interfaces;
using LPPermission.DAL.Models;
using LPPermission.Models;
using AutoMapper;
using NUnit.Framework;
using System.Linq.Expressions;
using System.Threading.Tasks;
using MockQueryable.Moq;
using MockQueryable;
namespace LPPermission.BLL.Tests.Services
{
    /// <summary>
    /// Test class for the UploadService.
    /// </summary>
    [TestFixture]
    public class UploadServiceTests
    {
        private Mock<IGenericRepository<Geo>> geoRepo;
        private Mock<IGenericRepository<Container>> containerRepo;
        private Mock<IGenericRepository<UserGroup>> userGroupRepo;
        private Mock<IGenericRepository<Permission>> permissionRepo;
        private Mock<IGenericRepository<Menu>> menuRepo;
        private Mock<IGenericRepository<Privilege>> privilegeRepo;
        private Mock<IGenericRepository<State>> stateRepo;
        private Mock<IMapper> mapper;
        private UploadService service;

        /// <summary>
        /// Sets up the test environment before each test.
        /// </summary>
        [SetUp]
        public void Setup()
        {
            geoRepo = new Mock<IGenericRepository<Geo>>();
            containerRepo = new Mock<IGenericRepository<Container>>();
            userGroupRepo = new Mock<IGenericRepository<UserGroup>>();
            permissionRepo = new Mock<IGenericRepository<Permission>>();
            menuRepo = new Mock<IGenericRepository<Menu>>();
            privilegeRepo = new Mock<IGenericRepository<Privilege>>();
            stateRepo = new Mock<IGenericRepository<State>>();
            mapper = new Mock<IMapper>();

            service = new UploadService(
                menuRepo.Object,
                privilegeRepo.Object,
                permissionRepo.Object,
                userGroupRepo.Object,
                stateRepo.Object,
                geoRepo.Object,
                containerRepo.Object
            );
        }
        /// <summary>
        /// Tests the ProcessCsvRecordsAsync method with valid CSV records.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        [Test]
        public async Task ProcessCsvRecordsAsync_ValidCsvRecord_CreatesUserGroupAndPermission()
        {
            // Arrange
            var geo = new Geo { GeoId = 1, Name = "US" };
            var container = new Container { ContainerId = 1, Name = "TJMaxx", GeoId = 1 };

            geoRepo.Setup(r => r.GetByConditionAsync(It.IsAny<Expression<Func<Geo, bool>>>()))
                .ReturnsAsync(new Geo { GeoId = 1, Name = "Canada" });

            containerRepo.Setup(r => r.GetByConditionAsync(It.IsAny<Expression<Func<Container, bool>>>()))
                .ReturnsAsync(new Container { Description = "HomeSense", ContainerId = 1 });

            userGroupRepo.Setup(r => r.AddAsync(It.IsAny<UserGroup>())).Returns(Task.CompletedTask);
            userGroupRepo.Setup(r => r.SaveAsync()).Returns(Task.CompletedTask);

            permissionRepo.Setup(r => r.AddAsync(It.IsAny<Permission>())).Returns(Task.CompletedTask);
            permissionRepo.Setup(r => r.SaveAsync()).Returns(Task.CompletedTask);

            // Menu mock
            var menuList = new List<Menu>
        {
            new Menu { MenuLevel = 1, ParentId = null, MenuId = 1, Name = "All privileges" }
        };
            menuRepo.Setup(r => r.GetByConditionAsyncMenu(It.IsAny<Expression<Func<Menu, bool>>>()))
                .Returns((Expression<Func<Menu, bool>> predicate) =>
                    menuList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                );

            // Privilege mock
            var privilegeList = new List<Privilege>();
            privilegeRepo.Setup(r => r.GetByConditionAsyncPrivelege(It.IsAny<Expression<Func<Privilege, bool>>>()))
                .Returns((Expression<Func<Privilege, bool>> predicate) =>
                    privilegeList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                );

            // Permission mock
            var permissionList = new List<Permission>();
            permissionRepo.Setup(r => r.GetByConditionAsyncPermission(It.IsAny<Expression<Func<Permission, bool>>>()))
                .Returns((Expression<Func<Permission, bool>> predicate) =>
                    permissionList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                );

            // UserGroup mock
            var userGroupList = new List<UserGroup>();
            userGroupRepo.Setup(r => r.GetByConditionAsyncUserGroup(It.IsAny<Expression<Func<UserGroup, bool>>>()))
                .Returns((Expression<Func<UserGroup, bool>> predicate) =>
                    userGroupList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                );

            var records = new List<CsvRecord>
        {
            new CsvRecord
            {
                Parent = "All privileges/Application privileges",
                Privilege = "Config Tool",
                State = null,
                InheritedFrom = null,
                Description = "Allows the user to use Config Tool.",
                GroupPermissions = new Dictionary<string, string>
                {
                    { "TJXG_GENETEC_LP_STORE_US", "Deny" },
                    { "TJXG_GENETEC_LP_STORE_Admin_US", "Allowed" },
                    { "TJXG_GENETEC_LP_LV1_Support", "Deny" },
                    { "TJXG_Gtest", "Deny" }
                }
            }
        };

            // Act
            await service.ProcessCsvRecordsAsync(records, "Canada", "HomeSense");

            // Assert
            userGroupRepo.Verify(r => r.AddAsync(It.IsAny<UserGroup>()), Times.AtLeastOnce());
            permissionRepo.Verify(r => r.AddAsync(It.IsAny<Permission>()), Times.AtLeastOnce());
        }

        /// <summary>
        /// Tests the ProcessCsvRecordsAsync method with invalid Geo data.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        [Test]
        public async Task ProcessCsvRecordsAsync_InvalidGeo_ThrowsException()
        {
            // Arrange
            geoRepo.Setup(r => r.GetByConditionAsync(It.IsAny<Expression<Func<Geo, bool>>>()))
                .ReturnsAsync(new Geo { GeoId = 1, Name = "Geo1" });

            containerRepo.Setup(r => r.GetByConditionAsync(It.IsAny<Expression<Func<Container, bool>>>()))
                .ReturnsAsync(new Container { Description = "Test" });

            userGroupRepo.Setup(r => r.AddAsync(It.IsAny<UserGroup>())).Returns(Task.CompletedTask);
            userGroupRepo.Setup(r => r.SaveAsync()).Returns(Task.CompletedTask);

            permissionRepo.Setup(r => r.AddAsync(It.IsAny<Permission>())).Returns(Task.CompletedTask);
            permissionRepo.Setup(r => r.SaveAsync()).Returns(Task.CompletedTask);

            // Menu mock
            var menuList = new List<Menu>
        {
            new Menu { MenuLevel = 1, ParentId = null, MenuId = 1, Name = "All privileges" }
        };
            menuRepo.Setup(r => r.GetByConditionAsyncMenu(It.IsAny<Expression<Func<Menu, bool>>>()))
                .Returns((Expression<Func<Menu, bool>> predicate) =>
                    menuList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                );

            // Privilege mock
            var privilegeList = new List<Privilege>();
            privilegeRepo.Setup(r => r.GetByConditionAsyncPrivelege(It.IsAny<Expression<Func<Privilege, bool>>>()))
                .Returns((Expression<Func<Privilege, bool>> predicate) =>
                    privilegeList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                );

            // Permission mock
            var permissionList = new List<Permission>();
            permissionRepo.Setup(r => r.GetByConditionAsyncPermission(It.IsAny<Expression<Func<Permission, bool>>>()))
                .Returns((Expression<Func<Permission, bool>> predicate) =>
                    permissionList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                );

            // UserGroup mock
            var userGroupList = new List<UserGroup>();
            userGroupRepo.Setup(r => r.GetByConditionAsyncUserGroup(It.IsAny<Expression<Func<UserGroup, bool>>>()))
                .Returns((Expression<Func<UserGroup, bool>> predicate) =>
                    userGroupList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                );

            var records = new List<CsvRecord>
        {
            new CsvRecord
            {
                Parent = "All privileges/Application privileges",
                Privilege = "Config Tool",
                State = "Allowed",
                InheritedFrom = "",
                Description = "Allows the user to use Config Tool.",
                GroupPermissions = new Dictionary<string, string>
                {
                    { "TJXG_GENETEC_LP_STORE_US", "Allowed" }
                }
            }
        };

            // Act & Assert
            var ex = Assert.ThrowsAsync<Exception>(async () => await service.ProcessCsvRecordsAsync(records, "US", "TJMaxx"));
            Assert.That(ex.Message, Is.EqualTo("ContainerId not found"));
        }

        /// <summary>
        /// Tests the GetOrCreateUserGroupId method when the user group exists.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        [Test]
        public async Task GetOrCreateUserGroupId_ShouldReturnExistingUserGroupId_WhenUserGroupExists()
        {
            // Arrange
            var userGroupName = "New-Group";
            var geoId = 1;
            var containerId = 1;
            string geoName = "";
            string containerName = "";
            var existingUserGroup = new UserGroup { UserGroupId = 1, Name = userGroupName };
            var userGroupList = new List<UserGroup>();
            userGroupRepo.Setup(r => r.GetByConditionAsyncUserGroup(It.IsAny<Expression<Func<UserGroup, bool>>>()))
                 .Returns((Expression<Func<UserGroup, bool>> predicate) =>
                     userGroupList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                 );


            // Act
            var result = await service.GetOrCreateUserGroupId(userGroupName, geoId, containerId, geoName, containerName);

            // Assert
            Assert.AreEqual(1, result);
        }

        /// <summary>
        /// Tests the GetOrCreateUserGroupId method when the user group does not exist.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        [Test]
        public async Task GetOrCreateUserGroupId_ShouldCreateNewUserGroup_WhenUserGroupDoesNotExist()
        {
            // Arrange
            var userGroupName = "New-Group";
            var geoId = 1;
            var containerId = 1;
            string geoName = "";
            string containerName = "";
            var userGroupList = new List<UserGroup>();
            userGroupRepo.Setup(r => r.GetByConditionAsyncUserGroup(It.IsAny<Expression<Func<UserGroup, bool>>>()))
                 .Returns((Expression<Func<UserGroup, bool>> predicate) =>
                     userGroupList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                 );
            

            // Act
            var result = await service.GetOrCreateUserGroupId(userGroupName, geoId, containerId, geoName, containerName);

            // Assert
            Assert.AreEqual(1, result); // Assuming the first ID is 1
        }

        /// <summary>
        /// Tests the GetAllGeosAsync method to ensure it returns Geo DTOs.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        [Test]
        public async Task GetAllGeosAsync_ReturnsGeoDtos()
        {
            // Arrange
            var geos = new List<Geo>
        {
            new Geo { GeoId = 1, Name = "US", Description = "United States" },
            new Geo { GeoId = 2, Name = "CA", Description = "Canada" }
        };

            var geoRepo = new Mock<IGenericRepository<Geo>>();
            geoRepo.Setup(r => r.GetAllGeos()).Returns(geos.AsQueryable());

            var userGroupList = new List<UserGroup>();
            userGroupRepo.Setup(r => r.GetByConditionAsyncUserGroup(It.IsAny<Expression<Func<UserGroup, bool>>>()))
                 .Returns((Expression<Func<UserGroup, bool>> predicate) =>
                     userGroupList.AsQueryable().Where(predicate).BuildMockDbSet().Object
                 );

            // Act
            var result = await service.GetAllGeosAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count());
            Assert.AreEqual("US", result.First().Name);
            Assert.AreEqual("United States", result.First().Description);
            Assert.AreEqual("CA", result.Last().Name);
            Assert.AreEqual("Canada", result.Last().Description);
        }
    }
}