using FunctionApp.LPPermissions.Services;
using FunctionApp.LPPermissions.Services.interfaces;
using LPPermission.BLL;
using LPPermission.DAL.Models;
using LPPermission.DAL;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.Azure.Functions.Worker.Builder;
using Microsoft.DurableTask.Client;
using Microsoft.DurableTask.Worker;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SetLPPermissions.Services;
using SetLPPermissions.Utilities;
using Microsoft.Extensions.Logging;

var builder = FunctionsApplication.CreateBuilder(args);

// Load configuration from appsettings.json
var configuration = new ConfigurationBuilder()
    .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables()
    .Build();

string keyVaultUrl = configuration["KeyVaultUrl"];
string instrumentationKey = configuration["Telemetry:InstrumentationKey"];

// Relative paths to the scripts and files
string scriptDirectory = AppDomain.CurrentDomain.BaseDirectory;
//string? baseDirectory1 = Directory.GetParent(scriptDirectory)?.Parent?.Parent?.Parent?.FullName;
string? baseDirectory = Directory.GetParent(scriptDirectory)?.FullName;

if (baseDirectory == null)
{
    throw new InvalidOperationException("Base directory could not be determined.");
}
string fedConnectScriptPath = Path.Combine(baseDirectory, configuration["Paths:FedConnectScriptPath"]);
string storeConnectScriptPath = Path.Combine(baseDirectory, configuration["Paths:StoreConnectScriptPath"]);
string getPermissionDataScriptPath = Path.Combine(baseDirectory, configuration["Paths:GetPermissionScriptPath"]);
string updatePermissionDataScriptPath = Path.Combine(baseDirectory, configuration["Paths:UpdatePermissionScriptPath"]);


string logFilePath = Path.Combine(baseDirectory, configuration["Paths:LogFilePath"]);
string ipJsonFilePath = Path.Combine(baseDirectory, configuration["Paths:IPJsonFilePath"]);
string outputFilePath = Path.Combine(baseDirectory, configuration["Paths:OutputFilePath"]);

builder.Services.AddLogging();
builder.Services.AddSingleton<PowerShellHandlerService>();
builder.Services.AddDurableTaskClient();
builder.Services.AddDurableTaskWorker(options =>
{
    options.UseGrpc();
});
builder.Services.AddSingleton<IFedConnectionService, FedConnectionService>(sp =>
    new FedConnectionService(
        fedConnectScriptPath,
        logFilePath,
        ipJsonFilePath,
        sp.GetRequiredService<TelemetryClient>(),
         sp.GetRequiredService<ILogger<FedConnectionService>>(), // Inject ILogger
        1
    )
);
builder.Services.AddSingleton<IStoreConnectionService, StoreConnectionService>(sp =>
    new StoreConnectionService(
        storeConnectScriptPath,
        getPermissionDataScriptPath,
        updatePermissionDataScriptPath,
        logFilePath,
        ipJsonFilePath,
        outputFilePath,
        sp.GetRequiredService<TelemetryClient>(),
         sp.GetRequiredService<IGetPermission>(), 1
    )
); builder.Services.AddSingleton<TelemetryClient>(sp =>
{
    var telemetryConfiguration = new TelemetryConfiguration();
    telemetryConfiguration.InstrumentationKey = instrumentationKey;
    return new TelemetryClient(telemetryConfiguration);
});
builder.Services.AddSingleton<ICircuitBreaker, CircuitBreaker>(sp =>
    new CircuitBreaker(5, TimeSpan.FromMinutes(5))
);
builder.Services.AddSingleton<IDurableTaskClientProvider, DurableTaskClientProvider>();
// Register ConfigurationPath as a singleton
var configurationPath = new SetLPPermissions.Utilities.ConfigurationPath
{
    FedConnectionScriptPath = fedConnectScriptPath,
    GetPermissionDataScriptPath = getPermissionDataScriptPath,
    StoreConnectionScriptPath = storeConnectScriptPath,
    LogFilePath = logFilePath,
    IPJsonFilePath = ipJsonFilePath
};

builder.Services.AddScoped<IGenericRepository<PermissionDto>>(sp =>
{
    var config = sp.GetRequiredService<IConfiguration>();
    var connectionString = config["ConnectionStrings:DefaultConnection"];
    return new GenericRepository<PermissionDto>(connectionString);
});

builder.Services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepository<>));
builder.Services.AddSingleton(configurationPath);
///builder.Services.AddScoped<IGenericRepository<PermissionDto>, GenericRepository<PermissionDto>>();
builder.Services.AddScoped<IGetPermission, GetPermission>();
builder.ConfigureFunctionsWebApplication();
builder.Build().Run();
