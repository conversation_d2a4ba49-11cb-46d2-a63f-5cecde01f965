﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias gsmac Get-SCMacro
Function Get-SCMacro {
    <#
    .Synopsis
        This method will return all the properties of the macro represented by the ID

    .DESCRIPTION
        This method will return all the basic properties of the macro represented by the ID.
        For object properties that represent a relation with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The MacroId parameter represents the Id of the macro to retreive (The guid representing the entity in the Security Center System)
        You can also pass any macro object that contains an ID as a parameter

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        # Will create a new entity of type macro with the name MyNewMacro
        $mac = New-SCMacro -n "MyNewMacro"

        Get-SCMacro -MacroId $mac.Id

        #Exit the session
        Exit-SCSession

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        New-SCEntity -n "MyNewMacro" -t macros | Get-SCMacro

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, Position=0, ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MacroId
    )
    begin{
    }
    process{
        SCCmdletImplementation $MyInvocation.InvocationName {
            $mid = GetIdFromObject $MacroId
            Get-SCEntity -EntityId $mid
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ismac Invoke-SCMacro
Function Invoke-SCMacro {
    <#
    .Synopsis
        Method used to execute a macro

    .DESCRIPTION
        This Method will allow the user to execute a specific macro from security center 
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $macro = New-SCMacro -Name "Live Macro" | Get-SCMacro
        $macro.Description = "When executed, will display video in SD tiles"
        $macro.Code = (Get-Content c:\macros\Playback.cs | Out-String)
        Set-SCMacro $macro
        Invoke-SCMacro $macro
        
        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MacroId
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $macid = GetIdFromObject $MacroId
            $uri = "Entities/$macid/Execute"
         
            $jsonBody = @{} | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "POST" -Body $jsonBody
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias nsmac New-SCMacro
Function New-SCMacro {
    <#
    .Synopsis
        Will create a new macro with the provided name

    .DESCRIPTION
        This method will create an new macro in Security Center with the given name.  The return value will contain the Id of the new macro
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new macro upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Will create a new entity of type macro with the name MyNewMacro
        New-SCMacro -n "MyNewMacro"
        
        Exit-SCSession
    .EXAMPLE
        #
        # Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Will create a new entity of type macro with the name MyNewMacro
        nsmac "MyNewMacro"
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Macros
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias rsmac Remove-SCMacro
Function Remove-SCMacro {
    <#
    .Synopsis
        This method will remove the macro represented by the provided MacroId from Security Center

    .DESCRIPTION
        This method will permanently remove the specified macro from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The MacroId parameter represents the entity to remove from Security Center
    .EXAMPLE
        #
        # Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $newMacro = New-SCMacro -Name "MyMacro"

        # Remove the new macro by providing the macro object
        Remove-SCMacro -id $newMacro
        
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $newMacro = nsmac "MyMacro"

        # remove the new macro by providing the macro object
        rsmac $newMacro
        
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $MacroId
        )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $MacroId
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssmac Set-SCMacro
Function Set-SCMacro {
    <#
    .Synopsis
        Used to update the properties of a macro in Security Center
    .DESCRIPTION
        This method is used to update the properties of a macro to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods

        The parameter Macro represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        #Will create a new entity of type macro with the name MyNewMacro
        $mac = New-SCMacro -n "MyNewMacro"
                
        $mac = Get-SCMacro $mac

        # Update its description
        $mac.Description = "My new Macro"
        $mac.Code = (Get-Content c:\Macros\Live.cs | Out-String)

        # Update config in SecurityCenter
        Set-SCMacro -Macro $mac

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        # Will create a new entity of type macro with the name MyNewMacro
        $mac = nsmac -n "MyNewMacro" | gsmac
                
        # update its description
        $mac.Description = "My new Macro"
        $mac.Code = (Get-Content c:\Macros\Live.cs | Out-String)

        # Uupdate config in SecurityCenter
        ssmac $mac

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("mac")] $Macro
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Macro
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCMacroProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a Macro
    .DESCRIPTION
        This method will list the supported properties and relation of a macro (the data model, not the actual data).  This method is used
        when you want to know what is available for a given macro
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCMacroProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiMacro" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'

