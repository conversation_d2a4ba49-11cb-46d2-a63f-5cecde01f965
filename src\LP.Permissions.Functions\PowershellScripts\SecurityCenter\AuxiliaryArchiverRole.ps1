﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias asaac Add-SCAuxiliaryArchiverCamera
Function Add-SCAuxiliaryArchiverCamera {
    <#
    .Synopsis
        This method will add a new camera to the SecurityCenter Auxiliary Archiver.

    .DESCRIPTION
        This method is used to add a camera to the auxiliary archiver.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the Auxiliary archiver on which the camera will be added.
        You can also pass any Auxiliary Archiver role object that contains an ID as a parameter.

        The CameraId parameter represents the Id of the camera to add to the auxiliary archvier.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myAA = Get-SCRoles -Type AuxiliaryArchiver | where { $_.Name -eq "MyAuxiliaryArchiver"}
        Add-SCAuxiliaryArchiverCamera $myAA.Id -CameraId "{00000001-0000-babe-0000-1a9373050575}" 

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        Get-SCArchiverCameras
        Remove-AuxiliaryArchiverCamera
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] $CameraId
    )

    Begin {
    }

    Process {

        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/Cameras"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Id", $CameraId)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Post" -Body $jsonBody 
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias dbsaa Debug-SCAuxiliaryArchiverRole
Function Debug-SCAuxiliaryArchiverRole {
    <#
    .Synopsis
        This will launch the diagnose command for the selected auxiliary archiver role.
    .DESCRIPTION
        This method will launch the diagnose command for the given auxiliary archiver role  This method is used
        when you want to troubleshoot a given auxiliary archiver role.

        The AuxiliaryArchiverId parameter represents the Id of the auxiliary archiver role to diagnose (The guid representing the role in the Security Center System).
        You can also pass any auxiliary archiver role object that contains an ID as a parameter.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will retreive the auxiliary archiver role
        $entity = Get-SCRoles -t AuxiliaryArchiver
        
        Debug-SCAuxiliaryArchiverRole -AuxiliaryArchiverId $entity

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("Id")] $AuxiliaryArchiverId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AuxiliaryArchiverId -RelationName "diagnostics" 
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias gsaac Get-SCAuxiliaryArchiverCameras
Function Get-SCAuxiliaryArchiverCameras {
    <#
    .Synopsis
        This method will return all the cameras under the given Auxiliary archiver.

    .DESCRIPTION
        This method is used to return all the cameras under the given Auxiliary archiver.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build.
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the Auxiliary archiver to retrieve the cameras from (The guid representing the auxiliary archiver in the Security Center System).
        You can also pass any Auxiliary archiver role object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myAux = Get-SCRoles -Type AuxiliaryArchiver 
        Get-SCAuxiliaryArchiverCameras $myAux.Id

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.

    .LINK
        Add-SCAuxiliaryArchiverCamera
        Remove-AuxiliaryArchiverCamera
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "Cameras"  
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsaarm Get-SCAuxiliaryArchiverRecordingModes
Function Get-SCAuxiliaryArchiverRecordingModes {
    <#
    .Synopsis
        This method will return all the Auxiliary archiver recording modes that the auxiliary archiver has configured.
    .DESCRIPTION
        This method is used to return all the recording modes that the auxiliary archiver has configured.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The RoleId parameter represents the Id of the Auxiliary archiver to retrieve the recording modes from(The guid representing the archiver in the Security Center System).
        You can also pass any Auxiliary archiver role object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type AuxiliaryArchiver 
        Get-SCAuxiliaryArchiverRecordingModes $myArc.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RoleId -RelationName "RecordingModes"  
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias gsaas Get-SCAuxiliaryArchiverStats
Function Get-SCAuxiliaryArchiverStats {
    <#
    .Synopsis
        This method will return the Auxiliary archiver statistics.
    .DESCRIPTION
        This method is used to return the statistics of Auxiliary archiving.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        This method may take some time to complete.

        The AuxiliaryArchiverId parameter represents the Id of the Auxiliary archiver to retrieve the statistics from(The guid representing the role in the Security Center System).
        You can also pass any Auxiliary archiver object that contains an ID as a parameter.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type AuxiliaryArchiver

        Get-SCAuxiliaryArchiverStats $myArc.Id

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $AuxiliaryArchiverId
    )
 
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $AuxiliaryArchiverId -RelationName "ArchiverStats"  
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias rsaac Remove-SCAuxiliaryArchiverCamera
Function Remove-SCAuxiliaryArchiverCamera {
    <#
    .Synopsis
        This method will remove the camera from the Auxiliary Archiver.

    .DESCRIPTION
        This method is used to remove the camera from the Auxiliary Archiver.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the Auxiliery Archiver from which the camera is removed.
        You can also pass any Auxiliary Archiver role object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myAux = Get-SCRoles -Type AuxiliaryArchiver | where { $_.Name -eq "MyAuxiliaryArchiver"}
        Remove-SCAuxiliaryArchiverCamera -RoleId $myAux.Id -CameraId "{00000001-0000-babe-0000-1a9373050575}"

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.

    .LINK
         Add-AuxiliaryArchiverCamera
        Get-SCArchiverCameras
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] $CameraId
    )

    Begin {
    }

    Process {

        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/Cameras/$CameraId"

            InvokeSCRestMethod -UriSuffix $uri -Method "Delete" -Body (@{} | ConvertTo-Json)
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias ssaarm Set-SCAuxiliaryArchiverRecordingModes
Function Set-SCAuxiliaryArchiverRecordingModes {
    <#
    .Synopsis
        This method will set the recording mode that the auxiliary archiver has configured.
    .DESCRIPTION
        This method is used to set the Auxiliary archiver recording modes
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete.

        The RoleId parameter represents the Id of the Auxiliary archiver to set the recording modes to(The guid representing the Auxiliary archiver in the Security Center System).
        You can also pass any Auxiliary archiver role object that contains an ID as a parameter.

        The RecModeId represents the id in the recording mode list to set the configuration.
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myArc = Get-SCRoles -Type AuxiliaryArchiver 
        Set-SCAuxiliaryArchiverRecordingModes $myArc.Id -RecModeId "0" -RecordingMode OnMotionOrManual

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("rmId")] $RecModeId
    )

    DynamicParam {
        # Set the dynamic parameters' name
        $ParameterName = 'RecordingMode'
        $ParameterAlias = 'rm'
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary

        # Create the collection of attributes
        $AttributeCollection = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute.Mandatory = $false
        $ParameterAttribute.Position = 2

        # Add the attributes to the attributes collection
        $AttributeCollection.Add($ParameterAttribute)

        # Generate and set the ValidateSet 
            
        $arrSet = 'Continuous', 'Manual', 'Off', 'OnMotionOrManual'
        $ValidateSetAttribute = New-Object System.Management.Automation.ValidateSetAttribute($arrSet)

        # Add the ValidateSet to the attributes collection
        $AttributeCollection.Add($ValidateSetAttribute)

         #add the alias to the attributes collection
        $ParamAlias = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias
        $AttributeCollection.Add($ParamAlias)

        # Create and return the dynamic parameter
        $RuntimeParameter = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName, [string], $AttributeCollection)
        $RuntimeParameterDictionary.Add($ParameterName, $RuntimeParameter)
        return $RuntimeParameterDictionary
    }
 
    Begin {
    }

    Process {
        $RecordingMode = $PsBoundParameters[$ParameterName]

        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/RecordingModes/$RecModeId"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("Mode", $RecordingMode)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Put" -Body $jsonBody 
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias ssaap Show-SCAuxiliaryArchiverProperties
Function Show-SCAuxiliaryArchiverProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an auxiliary archiver role.
    .DESCRIPTION
        This method will list the supported properties and relation of an auxiliary archiver role (the data model, not the actual data).  This method is used
        when you want to know what is available for a given auxiliary archiver role.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCAuxiliaryArchiverProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiAuxiliaryArchiverRole" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}

Export-ModuleMember -Function '*-*' -Alias '*'
