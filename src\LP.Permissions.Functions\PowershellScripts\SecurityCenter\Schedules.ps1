﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Function Get-SCSchedule {
    <#
    .Synopsis
        This method will return all the properties of the schedule represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the schedule represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ScheduleId parameter represents the Id of the schedule to retrieve (The guid representing the schedule in the Security Center System)
        You can also pass any schedule object that contains an ID as a parameter
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $myNewSchedule = New-SCSchedule -Name "MyNewRestSchedule"
        Get-SCSchedule $myNewSchedule

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ScheduleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $ScheduleId     
        }
    }    
}

# -----------------------------------------------------------------------------
Function New-SCSchedule {
    <#
    .Synopsis
        Method used to create a new schedule with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new security center schedule with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new schedule upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        New-SCSchedule -Name "MyNewSchedule" 
        
        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Schedules
        }
    }
}

# -----------------------------------------------------------------------------
Function Remove-SCSchedule {
    <#
    .Synopsis
        Will remove the schedule represented by the provided ScheduleId from Security Center
    .DESCRIPTION
        This method will permantly remove the specified schedule from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The ScheduleId parameter represents the schedule to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $mySchedule = New-SCSchedule -Name "MyNewSchedule"
        Remove-SCSchedule -ScheduleId $mySchedule
        
        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $ScheduleId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $ScheduleId
        }
    }
}

# -----------------------------------------------------------------------------
Function Set-SCSchedule() {
    <#
    .Synopsis
        Used to update the properties of a schedule in Security Center
    .DESCRIPTION
        This method is used to update the properties of a schedule to Security Center.  All properties that are not read-only will be updated.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Schedule represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new schedule
        $news = New-SCSchedule -Name "MySchedule" | Get-SCSchedule
        $news.Description = "test"
        
        Set-SCSchedule -Schedule $news

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("s")] $Schedule
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Schedule
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCScheduleProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a schedule
    .DESCRIPTION
        This method will list the supported properties and relation of a schedule (the data model, not the actual data).  This method is used
        when you want to know what is available for a given schedule
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCScheduleProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    
    SCCmdletImplementation $MyInvocation.InvocationName {
        $uri = "Help/Entities/ApiSchedule" 
        InvokeSCRestMethod -UriSuffix $uri -Method 'Get'
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'