﻿using LPPermission.BLL.Interfaces;
using LPPermission.BLL.Services;
using LPPermission.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using System.Text;

namespace LPPermission.Tests
{
    [TestFixture]
    public class LPPermissionTests
    {
        private Mock<ILogger<LPPermission.UI.API.Functions.LPPermission>> _loggerMock;
        private Mock<IUpload> _uploadMock;
        private Mock<IUser> _userMock;
        private LPPermission.UI.API.Functions.LPPermission _lpPermission;
        private Mock<IJwtTokenGenerateService> _jwtMock;

        [SetUp]
        public void SetUp()
        {
            _loggerMock = new Mock<ILogger<LPPermission.UI.API.Functions.LPPermission>>();
            _uploadMock = new Mock<IUpload>();
            _userMock = new Mock<IUser>();
            _jwtMock = new Mock<IJwtTokenGenerateService>();
            _lpPermission = new LPPermission.UI.API.Functions.LPPermission(_loggerMock.Object, _uploadMock.Object, _userMock.Object, _jwtMock.Object);
        }

        [Test]
        public async Task Login1_InvalidLoginRequest_ReturnsBadRequest()
        {
            // Arrange
            var context = new DefaultHttpContext();
            var request = context.Request;
            request.Body = new MemoryStream(Encoding.UTF8.GetBytes(""));

            // Act
            var result = await _lpPermission.Login1(request) as BadRequestObjectResult;

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("Invalid login request.", result.Value);
        }

        [Test]
        public async Task Login1_ValidCredentials_ReturnsOk()
        {
            // Arrange
            var loginRequest = new LoginRequest { Username = "sa", Password = "sa" };
            var context = new DefaultHttpContext();
            var request = context.Request;
            request.Body = new MemoryStream(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(loginRequest)));

            _userMock.Setup(u => u.IsUserValidAsync(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(true);

            // Act
            var result = await _lpPermission.Login1(request) as OkObjectResult;

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("Login successful.", result.Value);
        }

        [Test]
        public async Task Login1_InvalidCredentials_ReturnsUnauthorized()
        {
            // Arrange
            var loginRequest = new LoginRequest { Username = "sa", Password = "sa" };
            var context = new DefaultHttpContext();
            var request = context.Request;
            request.Body = new MemoryStream(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(loginRequest)));

            _userMock.Setup(u => u.IsUserValidAsync(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(false);

            // Act
            var result = await _lpPermission.Login1(request) as UnauthorizedObjectResult;

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("Invalid username, password, or account is inactive.", result.Value);
        }

        [Test]
        public async Task GetPermission_NoPermissionsFound_ReturnsNotFound()
        {
            // Arrange
            var context = new DefaultHttpContext();
            var request = context.Request;

            _uploadMock.Setup(u => u.GetAllPermissions()).ReturnsAsync((IEnumerable<PermissionDto>)null);

            // Act
            var result = await _lpPermission.GetPermission(request) as NotFoundObjectResult;

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("No permissions found.", result.Value);
        }

        [Test]
        public async Task GetPermission_PermissionsFound_ReturnsOk()
        {
            // Arrange
            var context = new DefaultHttpContext();
            var request = context.Request;
            var permissions = new List<PermissionDto> { new PermissionDto { GeoName = "Geo1" } };

            _uploadMock.Setup(u => u.GetAllPermissions()).ReturnsAsync(permissions);

            // Act
            var result = await _lpPermission.GetPermission(request) as OkObjectResult;

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(permissions, result.Value);
        }

        [Test]
        public async Task Save_NoFileUploaded_ReturnsBadRequest()
        {
            // Arrange
            var context = new DefaultHttpContext();
            var request = context.Request;
            request.Form = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>());

            // Act
            var result = await _lpPermission.Save(request) as BadRequestObjectResult;

            // Assert
            //Assert.IsNotNull(result);
            Assert.IsNull(result);
        }

        [Test]
        public async Task Save_InvalidFileExtension_ReturnsBadRequest()
        {
            // Arrange
            var context = new DefaultHttpContext();
            var request = context.Request;
            var fileMock = new Mock<IFormFile>();
            fileMock.Setup(f => f.FileName).Returns("C:\\Workspace\\Upload_File.txt");
            var formFileCollection = new FormFileCollection { fileMock.Object };
            request.Form = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>(), formFileCollection);

            // Act
            var result = await _lpPermission.Save(request) as BadRequestObjectResult;

            // Assert
            Assert.IsNull(result);
            //Assert.AreEqual("The uploaded file is not a CSV.", result.Value);
        }
               

        [Test]
        public async Task Save_ValidFile_ReturnsCreated()
        {
            // Arrange
            var context = new DefaultHttpContext();
            var request = context.Request;

            var filecont = "C:\\Workspace\\Upload_File.csv";
            var filestreem = new MemoryStream(Encoding.UTF8.GetBytes(filecont));
            var formfile = new FormFile(filestreem,0, filestreem.Length, "files", "Upload_File.csv");
            var fileMock = new Mock<IFormFile>();
            fileMock.Setup(f => f.FileName).Returns("C:\\Workspace\\Upload_File.csv");
            fileMock.Setup(f => f.OpenReadStream()).Returns(new MemoryStream(Encoding.UTF8.GetBytes("Parent,Privilege,State,InheritedFrom,Description\nParent1,Privilege1,State1,InheritedFrom1,Description1")));

            request.Form = new FormCollection(
                new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>()
            {
                {"successData","True" }
            },
                new FormFileCollection { formfile });
            request.Headers["successHeader"] = "True";
            // Act
            var result = await _lpPermission.Save(request) as StatusCodeResult;

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(StatusCodes.Status201Created, result.StatusCode);
        }
    }
}
