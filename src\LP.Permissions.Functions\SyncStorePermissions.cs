using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.Functions.Worker.Extensions.DurableTask;
using Microsoft.DurableTask.Client;
using Microsoft.DurableTask.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.DurableTask;
using DurableTask.Core;
using System.Diagnostics;
using SetLPPermissions.Utilities;
using FunctionApp.LPPermissions.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.Extensions.Options;

using FunctionApp.LPPermissions.Services.interfaces;
using Castle.Core.Logging;
using System.Threading;
using Azure.Security.KeyVault.Secrets;
using Azure.Identity;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using ConfigurationPath = SetLPPermissions.Utilities.ConfigurationPath;
using LPPermissionStoreSetup.Models;
using Microsoft.Azure.WebJobs.Hosting;
using FunctionApp.LPPermissions.Models;
using Newtonsoft.Json;
using LPPermission.BLL;
using LPPermission.DAL.Models;
using System.Collections.Generic;


namespace FunctionApp.LPPermissions
{
    public class SyncStorePermissions
    {
        private SecretClient _secretClient;
        private string systemInfo;
        private bool _telemetryEnabled;
        // Retrieve usernames from configuration
        private string primaryUsername;
        private string secondaryUsername;
        private string adminUsername;
        private IGetPermission _getPermission;

        public SyncStorePermissions(IConfiguration configuration, IGetPermission getPermission)
        {
            string keyVaultUrl = configuration["KeyVaultUrl"];
            _secretClient = new SecretClient(new Uri(keyVaultUrl), new DefaultAzureCredential());
            _telemetryEnabled = configuration.GetValue<bool>("Telemetry:Enabled");
            primaryUsername = configuration["Usernames:Primary"];
            secondaryUsername = configuration["Usernames:Secondary"];
            adminUsername = configuration["Usernames:Admin"];
            _getPermission = getPermission;
        }

        [Function("SetStorePermissions_HttpStart")]
        public async Task<HttpResponseData> HttpStart(
           [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post")] HttpRequestData req,
           [DurableClient] DurableTaskClient starter, FunctionContext executionContext)
        {
            var log = executionContext.GetLogger("StoreOrchestrator_HttpStart");
            var fedConnectionService = executionContext.InstanceServices.GetService<IFedConnectionService>();
            var telemetryClient = executionContext.InstanceServices.GetService<TelemetryClient>();
            var config = executionContext.InstanceServices.GetService<ConfigurationPath>();
            try
            {
                LogToFile(config.LogFilePath, "Starting FedConnector...");
                // Call the federation service to get store IPs
                // Try connecting with the primary user first, then secondary, then admin
                var storeIPs = await TryConnect(fedConnectionService);

                if (storeIPs == null || storeIPs.Count <= 0)
                {
                    throw new Exception("Failed to connect to federation service.");
                }
                LogToFile(config.LogFilePath, "FedConnector succeeded. Starting StoreConnector...");
                // Function input comes from the request content.
                string instanceId = await starter.ScheduleNewOrchestrationInstanceAsync("StorePermissionOrchestrator", storeIPs);
                LogToFile(config.LogFilePath, "Started orchestration with ID = '" + instanceId);
                log.LogInformation("Started orchestration with ID = '{instanceId}'.", instanceId);

                var response = req.CreateResponse(System.Net.HttpStatusCode.OK);
                response.Headers.Add("Content-Type", "application/json");
                await response.WriteStringAsync($"{{\"id\":\"{instanceId}\"}}");

                return response;
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Failed to start orchestration.");
                LogToFile(config.LogFilePath, "Failed to connect with Federation server.");
                if (_telemetryEnabled)
                {
                    telemetryClient.TrackEvent("Failed to connect with Federation server.", new Dictionary<string, string>
                                {
                                    { "ScriptPath", config.FedConnectionScriptPath }
                                });
                }
                var response = req.CreateResponse(System.Net.HttpStatusCode.InternalServerError);
                response.Headers.Add("Content-Type", "application/json");
                await response.WriteStringAsync("{\"error\":\"Failed to start orchestration.\"}");
                return response;
            }
        }
        private async Task<List<string>> TryConnect(IFedConnectionService fedConnectionService)
        {
            List<string> storeIPs = null;
            string systemInfo;
            // Retrieve credentials from Key Vault
           // var primaryCredential = await GetCredential(primaryUsername);            
            var federationServer = await GetCredential("FederationServer");
            // Try with primary user
            //storeIPs = fedConnectionService.Connect(federationServer, primaryUsername, primaryCredential,string.Empty, out systemInfo);
            //if (storeIPs != null && storeIPs.Count > 0)
            //{
            //    return storeIPs;
            //}
            var secondaryCredential = await GetCredential(secondaryUsername);
            // Try with secondary user
            storeIPs = fedConnectionService.Connect(federationServer, secondaryUsername, secondaryCredential, string.Empty, out systemInfo);
            if (storeIPs != null && storeIPs.Count > 0)
            {
                return storeIPs;
            }
            var adminCredential = await GetCredential(adminUsername);
            // Try with admin user
            storeIPs = fedConnectionService.Connect(federationServer, adminUsername, adminCredential, string.Empty, out systemInfo);
            return storeIPs;
        }
        private async Task<List<PermissionDto>> TryConnectForStore(IStoreConnectionService storeConnectionService, string ipAddress)
        {
            List<PermissionDto> permissions = null;

            // Retrieve credentials from Key Vault
            var secondaryCredential = await GetCredential(secondaryUsername);
            // Try with primary user
            permissions = await storeConnectionService.SetLPPermissions(ipAddress, secondaryUsername, secondaryCredential);
            if (permissions != null && permissions.Count > 0)
            {
                return permissions;
            }

            // Try with secondary user
            var primaryCredential = await GetCredential(primaryUsername);
            permissions = await storeConnectionService.SetLPPermissions(ipAddress, primaryUsername, primaryCredential);
            if (permissions != null && permissions.Count > 0)
            {
                return permissions;
            }

            // Try with admin user
            var adminCredential = await GetCredential(adminUsername);
            permissions = await storeConnectionService.SetLPPermissions(ipAddress, adminUsername, adminCredential);
            return permissions;
        }
        [Function("StorePermissionOrchestrator")]
        public async Task StoreProcessingOrchestrator([OrchestrationTrigger] TaskOrchestrationContext context, FunctionContext executionContext)
        {
            var log = executionContext.GetLogger("StoreOrchestrator_HttpStart");
            try
            {
                var ipAddresses = context.GetInput<List<string>>();

                var tasks = new List<Task>();
                foreach (var ipAddress in ipAddresses)
                {
                    tasks.Add(context.CallActivityAsync("GetProcessStore", ipAddress));
                }

                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Failed to process orchestrator.");
                throw;
            }
        }
        [Function("GetProcessStore")]
        public async Task ProcessStore([ActivityTrigger] string ipAddress, FunctionContext executionContext)
        {
            var log = executionContext.GetLogger("ProcessStore");
            var telemetryClient = executionContext.InstanceServices.GetService<TelemetryClient>();
            var config = executionContext.InstanceServices.GetService<ConfigurationPath>();
            var storeConnectionService = executionContext.InstanceServices.GetService<IStoreConnectionService>();
            List<PermissionDto> permissions = null;
            bool isConnected = false;

            try
            {
                LogToFile(config.LogFilePath, "Starting StoreConnector...");

                // Try to connect for store using primary, secondary, and admin users
                permissions = await TryConnectForStore(storeConnectionService, ipAddress);
                isConnected = permissions is not null;

                if (!isConnected)
                {
                    LogToFile(config.LogFilePath, "Not able to connect with store.");
                    if (_telemetryEnabled)
                    {
                        telemetryClient.TrackEvent("Not able to connect with store.", new Dictionary<string, string>
                        {
                            { "ScriptPath", config.StoreConnectionScriptPath },
                            { "SystemInfo", systemInfo } // Placeholder for actual system info
                        });
                    }
                    throw new Exception("Failed to connect to store with primary, secondary, and admin credentials.");
                }
                else
                {
                    LogToFile(config.LogFilePath, "Successfully connected and retrieved system info.");
                    if (_telemetryEnabled)
                    {
                        telemetryClient.TrackEvent("Success with store connection", new Dictionary<string, string>
                        {
                            { "ScriptPath", config.StoreConnectionScriptPath },
                            { "SystemInfo", systemInfo } // Placeholder for actual system info
                        });
                    }
                }

                if (_telemetryEnabled)
                {
                    telemetryClient.TrackEvent("StoreProcessed", new Dictionary<string, string> { { "IPAddress", ipAddress } });
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, $"Failed to process store with IP: {ipAddress}");
                if (_telemetryEnabled)
                {
                    telemetryClient.TrackException(ex);
                }
            throw;
            }

        }
        private async Task<string> GetCredential(string credentialName)
        {
            var log = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger("GetCredential");
            try
            {
                KeyVaultSecret secret = await _secretClient.GetSecretAsync(credentialName);
                return secret.Value;
            }
            catch (Exception ex)
            {
                log.LogError(ex, $"Failed to get credential: {credentialName}");
                throw;
            }
        }

        private void LogToFile(string filePath, string message)
        {
            var log = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger("LogToFile");
            try
            {
                using (StreamWriter writer = new StreamWriter(filePath, true))
                {
                    writer.WriteLine($"{DateTime.Now}: {message}");
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Failed to write to log file");
                if (_telemetryEnabled)
                {
                    TelemetryConfiguration configuration = TelemetryConfiguration.CreateDefault();
                    TelemetryClient telemetryClient = new TelemetryClient(configuration);
                    telemetryClient.TrackException(ex);
                }
            }
        }
    }
}
