﻿using LPPermission.UI.Models;

namespace LPPermission.UI.Services
{
    /// <summary>
    /// Service for retrieving Geo and Container data from the API.
    /// </summary>
    public class GeoContainerService : IGeoContainerService
    {
        // HttpClient for making HTTP requests
        private readonly HttpClient _httpClient;

        /// <summary>
        /// Constructor to initialize the GeoContainerService with an HttpClient.
        /// </summary>
        /// <param name="httpClient">HttpClient instance for making HTTP requests.</param>
        public GeoContainerService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        /// <summary>
        /// Retrieves a list of Geo data from the API.
        /// </summary>
        /// <returns>A list of GeoDto objects.</returns>
        public async Task<List<GeoDto>> GetGeosAsync()
        {
            // Send a GET request to the API endpoint to retrieve Geo data
            return await _httpClient.GetFromJsonAsync<List<GeoDto>>("api/geos");
        }

        /// <summary>
        /// Retrieves a list of Container data for a specific Geo ID from the API.
        /// </summary>
        /// <param name="geoId">The ID of the Geo for which to retrieve containers.</param>
        /// <returns>A list of ContainerDto objects.</returns>
        public async Task<List<ContainerDto>> GetContainersAsync(int geoId)
        {
            // Send a GET request to the API endpoint to retrieve Container data for the specified Geo ID
            return await _httpClient.GetFromJsonAsync<List<ContainerDto>>($"api/containers/{geoId}");
        }
    }
}