param (
    [string]$permissionsJsonFilePath,
    [string]$IPAddress,
    [string]$Username,
    [string]$Credential,
    [string]$ServerPassword = ""
)
$parentDirectory = Split-Path -Parent $PSScriptRoot
$logFilePath = "ps_logfile.log"

function Write-Message {
    param (
        [string]$message
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Add-Content -Path $logFilePath -Value "$timestamp - $message"
}

try {
    if (-not $permissionsJsonFilePath -or -not (Test-Path $permissionsJsonFilePath)) {
        Write-Message "Permissions JSON file does not exist: $permissionsJsonFilePath"
        throw "Permissions JSON file not found."
    }
    $directoryPassword = $Credential
    $genetecServerPassword = $ServerPassword

    # Read JSON input
    $dbPermissionData = Get-Content -Path $permissionsJsonFilePath | ConvertFrom-Json

    # Enter session
    Enter-SCSession -ComputerName $IPAddress -User $Username -DirectoryPassword $directoryPassword -GenetecServerPassword $genetecServerPassword

    # Get User Groups from Store
    $storePermissionData = Get-SCEntities -Type UserGroups

    # Group privileges by UserGroupName to process each group only once
    $groupedPermissions = $dbPermissionData | Group-Object -Property UserGroupName

    foreach ($group in $groupedPermissions) {
        $dbGroupName = $group.Name
        $dbPrivileges = $group.Group

        # Check if the group exists in the store
        $matchingStoreGroup = $storePermissionData | Where-Object { $_.Name -eq $dbGroupName }

        if ($matchingStoreGroup) {
            # Group exists, process privileges
            $StoreGroupName = $matchingStoreGroup.Name
            $StorePrivileges = Get-SCUserGroupPrivileges -UserGroupId $matchingStoreGroup.ID

            foreach ($dbPrivilege in $dbPrivileges) {
                $dbPrivilegeID = $dbPrivilege.GenetecReference
                $dbPrivilegeState = $dbPrivilege.StateName

                $matchingStorePrivilege = $StorePrivileges | Where-Object { $_.Id -eq $dbPrivilegeID }

                if ($matchingStorePrivilege) {
                    # Privilege exists, update its state
                    try {
                        Set-SCUserGroupPrivileges -UserGroupId $matchingStoreGroup.ID -PrivilegeId $dbPrivilegeID -PrivilegeState $dbPrivilegeState
                        Write-Message "Updated privilege $dbPrivilegeID for group $dbGroupName state changed to $dbPrivilegeState for $IPAddress"
                    } catch {
                        Write-Message "Failed to update privilege $dbPrivilegeID for group $dbGroupName $_ for $IPAddress"
                    }
                } else {
                    Write-Message "Privilege $dbPrivilegeID not found for group $dbGroupName for $IPAddress"
                }
            }
        } else {
            # Group does not exist, create it and set privileges
            try {
                $newGroup = New-SCUserGroup -Name $dbGroupName
                $newGroupID = $newGroup.ID

                foreach ($dbPrivilege in $dbPrivileges) {
                    $dbPrivilegeID = $dbPrivilege.GenetecReference
                    $dbPrivilegeState = $dbPrivilege.StateName

                    try {
                        Set-SCUserGroupPrivileges -UserGroupId $newGroupID -PrivilegeId $dbPrivilegeID -PrivilegeState $dbPrivilegeState
                        Write-Message "Set privilege $dbPrivilegeID for new group $dbGroupName"
                    } catch {
                        Write-Message "Failed to set privilege $dbPrivilegeID for new group $dbGroupName $_"
                    }
                }
                Write-Message "Created new group $dbGroupName and set privileges"
            } catch {
                Write-Message "Failed to create new group $dbGroupName $_"
            }
        }
    }
} catch {
    Write-Message "An error occurred: $_"
} finally {
    # Exit session
    try {
        Exit-SCSession
        Write-Message "Exited session successfully"
    } catch {
        Write-Message "Failed to exit session: $_"
    }
}