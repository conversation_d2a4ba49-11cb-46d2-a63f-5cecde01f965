﻿@page "/form"
@inject HttpClient Http
@implements IAsyncDisposable



<div class="top-row px-4">
    <NavMenu />
</div>

<div class="page-content">
    <!-- File Upload Section -->
    <div class="file-upload-container">
        <h3 class="k-form-label">File Upload</h3>
        <InputFile OnChange="HandleFileSelected" />
        <button @onclick="UploadFile" disabled="@isUploading" class="k-upload-button">Upload</button>

        <div class="k-form-hint">Accepted files: <strong>CSV</strong></div>

        <!-- Status Message -->
        <div class="status-message">
            @uploadResult
        </div>
    </div>
</div>

@code {
    private IBrowserFile selectedFile;
    private bool isUploading = false;
    private string uploadResult;

    // This method handles the file selection from the input
    private void HandleFileSelected(InputFileChangeEventArgs e)
    {
        selectedFile = e.File;
        uploadResult = null; // Reset result message
    }

    // This method uploads the selected file
    private async Task UploadFile()
    {
        if (selectedFile == null)
        {
            uploadResult = "Please select a file first.";
            return;
        }

        try
        {
            isUploading = true;
            uploadResult = null; // Reset result message

            // Prepare the MultipartFormDataContent for the file upload
            var content = new MultipartFormDataContent();
            var fileContent = new StreamContent(selectedFile.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024)); // max file size 10MB
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

            // Add the file to the content
            content.Add(fileContent, "file", selectedFile.Name);

            // Make the POST request to the local Azure Function endpoint
            var response = await Http.PostAsync("http://localhost:7223/api/Function", content);

            if (response.IsSuccessStatusCode)
            {
                uploadResult = "File uploaded successfully!";
            }
            else
            {
                uploadResult = $"Error: {response.ReasonPhrase}";
            }
        }
        catch (Exception ex)
        {
            uploadResult = $"An error occurred: {ex.Message}";
        }
        finally
        {
            isUploading = false;
        }
    }

    public async ValueTask DisposeAsync()
    {
        // Dispose of resources if needed
    }
}


@* @using LPPermission.UI.Shared
@using TelerikUI_App.Models
@using TelerikUI_App.Services
@inject NavigationManager Navigation

<style>
    /* General Body Styling */
    body {
        font-family: 'Arial', sans-serif;
        background-color: #f4f7fb;
        color: #333;
        margin: 0;
        padding: 0;
        height: 100vh;
    }

    /* Flexbox layout for the page content */
    .page-content {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 40px;
        height: 100%;
    }

    /* Styling the top menu section */
    .top-menu {
        display: flex;
        justify-content: space-between;
        width: 100%;
        padding: 20px;
        background-color: #85151e;
        color: white;
        font-size: 16px;
        font-weight: bold;
        border-bottom: 2px solid #6a0c12;
    }

    /* Styling the top menu items */
    .top-menu-item {
        padding: 10px 20px;
        cursor: pointer;
    }

    .top-menu-item:hover {
        background-color: #6a0c12;
    }

    /* Logo Styling */
    .logo {
        text-align: center;
        margin-bottom: 30px;
    }

    .logo img {
        width: 150px;
        height: auto;
    }

    /* Main Content */
    .main-content {
        text-align: center;
        font-size: 24px;
        color: #85151e;
        margin-top: 50px;
    }

    /* Footer Styling */
    .footer {
        margin-top: 40px;
        text-align: center;
        font-size: 14px;
        color: #555;
    }

    .footer a {
        color: #85151e;
        text-decoration: none;
    }

    .footer a:hover {
        text-decoration: underline;
    }
</style>

<div class="top-row px-4">
    <NavMenu />
</div>

<!-- Top Menu Section -->
@* <div class="top-menu">
    <div class="top-menu-item">Menu 1</div>
    <div class="top-menu-item">Menu 2</div>
</div> *@
@* 
<div class="page-content">
    <!-- Logo Section -->
    <div class="logo">
        <img src="TJX_Logo.svg.png" alt="Logo" />
    </div>

    <!-- Main Content Section -->
    <div class="main-content">
        <h1>Welcome to the LP Permission</h1>
    </div>

    
</div> *@

@code {
    // You can add any dynamic logic here for menu interaction or content updates
}
 