# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================


# Internal function
# Generic method to set the module settings of the LPR role
#
Function SetModuleSetting($roleId, $setting, $value) {

    SCCmdletImplementation $MyInvocation.InvocationName {
	$id = GetIdFromObject $RoleId

	$jsonObject = [ordered]@{} 
	$jsonObject.Add("IsActive", $value)
	$jsonBody = $jsonObject | ConvertTo-Json
	
	InvokeSCRestMethod -Method 'PUT' -UriSuffix "Entities/$id/Modules/$setting" -Body $jsonBody
    }  
}

# Internal function
# Generic method to retrieve the module setting from the LPR role
#
Function GetModuleSetting($roleId, $setting) {

    SCCmdletImplementation $MyInvocation.InvocationName {
	$id = GetIdFromObject $RoleId
	
	$result = InvokeSCRestMethod -Method 'GET' -UriSuffix "Entities/$id/Modules/$setting"
	$result.IsActive
    }
}

# -----------------------------------------------------------------------------
Function Set-SCLprHitMatcher {
    <#
    .Synopsis
        Method used to set the module setting Hit Matcher of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to toggle the Hit matcher module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to set this value

        The parameter Value represents the value to be set.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting Hit Matcher of this specific LPR manager.
        Set-SCLprHitMatcher -RoleId $LprManager.Id -Value $true

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] [alias("val")] [bool] $value
    )
    Begin {
    }

    Process {
	    SetModuleSetting $RoleId "HitMatcherSettings" $value
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCLprHitMatcher {
    <#
    .Synopsis
        Method used to get the module setting Hit Matcher of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to get the Hit matcher module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to get this value from

     .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting Hit Matcher of this specific LPR manager.
        Get-SCLprHitMatcher -RoleId $LprManager.Id

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
    Begin {
    }

    Process {
	    GetModuleSetting $roleId "HitMatcherSettings"
    }    
}

# -----------------------------------------------------------------------------
Function Set-SCLprGeoCoding {
    <#
    .Synopsis
        Method used to set the module setting Geocoding of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to toggle the Geocoding module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to set this value

        The parameter Value represents the value to be set.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting Geo Coding of this specific LPR manager.
        Set-SCLprGeoCodeing -RoleId $LprManager.Id -Value $true

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] [alias("val")] [bool] $value
    )
    Begin {
    }

    Process {
	    SetModuleSetting $RoleId "GeocoderSettings" $value
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCLprGeoCoding {
    <#
    .Synopsis
        Method used to get the module setting Geocoding of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to get the Geocoding module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to get this value from

     .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting GeoCoding of this specific LPR manager.
        Get-SCLprGeoCoding -RoleId $LprManager.Id

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
    Begin {
    }

    Process {
	    GetModuleSetting $roleId "GeocoderSettings"
    }    
}

# -----------------------------------------------------------------------------
Function Set-SCLprPlateFiltering {
    <#
    .Synopsis
        Method used to set the module setting Plate Filtering of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to toggle the Plate Filtering module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to set this value

        The parameter Value represents the value to be set.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting Plate Filtering of this specific LPR manager.
        Set-SCLprPlateFiltering -RoleId $LprManager.Id -Value $true

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] [alias("val")] [bool] $value
    )
    Begin {
    }

    Process {
        SetModuleSetting $RoleId "HotlistFilteringSettings" $value
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCLprPlateFiltering {
    <#
    .Synopsis
        Method used to get the module setting Plate Filtering of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to get the Plate Filtering module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to get this value from

     .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting plate Filtering of this specific LPR manager.
        Get-SCLprPlateFiltering -RoleId $LprManager.Id

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
    Begin {
    }

    Process {
	GetModuleSetting $roleId "HotlistFilteringSettings"
    }    
}

# -----------------------------------------------------------------------------
Function Set-SCLprEmailNotification {
    <#
    .Synopsis
        Method used to set the module setting Email Notification of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to toggle the Email Notification module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to set this value

        The parameter Value represents the value to be set.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting Email Notification of this specific LPR manager.
        Set-SCLprEmailNotification -RoleId $LprManager.Id -Value $true

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] [alias("val")] [bool] $value
    )
    Begin {
    }

    Process {
        SetModuleSetting $RoleId "EmailNotificationSettings" $value
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCLprEmailNotification {
    <#
    .Synopsis
        Method used to get the module setting Email notification of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to get the Email Notification module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to get this value from

     .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting Email notification of this specific LPR manager.
        Get-SCLprEMailNotification -RoleId $LprManager.Id

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
    Begin {
    }

    Process {
	GetModuleSetting $roleId "EmailNotificationSettings"
    }    
}

# -----------------------------------------------------------------------------
Function Set-SCLprXmlImport {
    <#
    .Synopsis
        Method used to set the module setting XML import of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to toggle the Xml Import module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to set this value

        The parameter Value represents the value to be set.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting XML import of this specific LPR manager.
        Set-SCLprXmlImport -RoleId $LprManager.Id -Value $true

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] [alias("val")] [bool] $value
    )
    Begin {
    }

    Process {
        SetModuleSetting $RoleId "XmlImporterSettings" $value
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCLprXmlImport {
    <#
    .Synopsis
        Method used to get the module setting Xml Import of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to get the Xml Import module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to get this value from

     .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting Xml Import of this specific LPR manager.
        Get-SCLprXmlImport -RoleId $LprManager.Id

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
    Begin {
    }

    Process {
	GetModuleSetting $roleId "XmlImporterSettings"
    }    
}

# -----------------------------------------------------------------------------
Function Set-SCLprXmlExport {
    <#
    .Synopsis
        Method used to set the module setting XML export of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to toggle the Xml export module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to set this value

        The parameter Value represents the value to be set.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting XML export of this specific LPR manager.
        Set-SCLprXmlExport -RoleId $LprManager.Id -Value $true

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] [alias("val")] [bool] $value
    )
    Begin {
    }

    Process {
        SetModuleSetting $RoleId "XmlExporterSettings" $value
    }    
}

# -----------------------------------------------------------------------------
Function Get-SCLprXmlExport {
    <#
    .Synopsis
        Method used to get the module setting Xml Export of the LPR Manager role.
    .DESCRIPTION
        This Method will allow the user to get the Xml Export module setting of a specific LPR Manager role.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to get this value from

     .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Set the module setting Xml Export of this specific LPR manager.
        Get-SCLprXmlExport -RoleId $LprManager.Id

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
    Begin {
    }

    Process {
	GetModuleSetting $roleId "XmlExporterSettings"
    }    
}

# -----------------------------------------------------------------------------
Function Add-SCLprAssociation {
    <#
    .Synopsis
        Method used to add a hotlist or permit association to an LPR Manager.
    .DESCRIPTION
        This Method will allow the user to add a hotlist or permit association to a specified LPR Manager.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to add to the permit or hotlist association to

        The parameter EntityId represents the hotlist or permit entity id we want to associate with the LPR Manager.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Associate the hotlist with the lpr manager.
        Add-SCLprAssociation -RoleId $LprManager.Id -EntityId $myHotlist.Id

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RoleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("eid")] $EntityId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
        $id = GetIdFromObject $RoleId
        $eid = GetIdFromObject $EntityId

        $jsonObject = [ordered]@{} 
        $jsonObject.Add("Id", $eid)
        $jsonBody = $jsonObject | ConvertTo-Json
    
        InvokeSCRestMethod -Method 'POST' -UriSuffix "Entities/$id/Associations" -Body $jsonBody
    }
    }    
}
# -----------------------------------------------------------------------------
Function Remove-SCLprAssociation {
    <#
    .Synopsis
        Method used to remove a hotlist or permit association from an LPR Manager.
    .DESCRIPTION
        This Method will allow the user to remove a hotlist or permit association to a specified LPR Manager.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to remove the permit or hotlist association from

        The parameter EntityId represents the hotlist or permit entity id we want to dissociate from the LPR Manager.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
       # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Associate the hotlist with the lpr manager.
        Remove-SCLprAssociation -RoleId $LprManager.Id -EntityId $myHotlist.Id

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RoleId,
        [parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("eid")] $EntityId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
        $id = GetIdFromObject $RoleId
        $eid = GetIdFromObject $EntityId
    
        InvokeSCRestMethod -Method 'DELETE' -UriSuffix "Entities/$id/Associations/$eid"
        }
    }    
}
# -----------------------------------------------------------------------------
Function Get-SCLprAssociations {
    <#
    .Synopsis
        Method used to list hotlist and permit association to a specified LPR Manager.
    .DESCRIPTION
         This Method will allow the user to list hotlist or permit association to a specified LPR Manager.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter RoleId will be used to specify the LPR Manager we want to list the permit or hotlist association from.
        
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Get the LPR Manager
        $LprManager = Get-SCRoles -Type LicensePlateManager 

        # Associate the hotlist with the lpr manager.
        Get-SCLprAssociation -RoleId $LprManager.Id 

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
        $id = GetIdFromObject $RoleId
    
        InvokeSCRestMethod -Method 'GET' -UriSuffix "Entities/$id/Associations"
        }
    }    
}
# -----------------------------------------------------------------------------
Function ConvertTo-SCPolygon() {
    <#
    .Synopsis
        Used to create a Polygon object in powershell session.
    .DESCRIPTION
        Used to create a Polygon object in powershell session.

    .EXAMPLE
        #
        $polygon = ConvertTo-SCPolygon -Name "My Polygon" -Capacity 50 -Points @( 
        (ConvertTo-SCPoint -lat 54.41344 -lon -98.01514), 
        (ConvertTo-SCPoint -lat 51.4959 -lon -71.62063),
        (ConvertTo-SCPoint -lat 46.23197 -lon -74.72963)
        ) 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [Guid]$Id = [Guid]::Empty,
        [string] $Name,
        [int]$Capacity,
        [PSCustomObject[]]$Points
    )
    Process {
        return [PSCustomObject]@{
            Id = $Id
            Name = $Name
            Capacity = $Capacity
            Points = $Points
            }
    }
}
# -----------------------------------------------------------------------------
Function ConvertTo-SCPoint() {
    <#
    .Synopsis
        Used to create a point object in powershell session.
    .DESCRIPTION
        Used to create a point object in powershell session.

    .EXAMPLE
        #
        $polygon = ConvertTo-SCPolygon -Name "My Polygon" -Capacity 50 -Points @( 
        (ConvertTo-SCPoint -lat 54.41344 -lon -98.01514), 
        (ConvertTo-SCPoint -lat 51.4959 -lon -71.62063),
        (ConvertTo-SCPoint -lat 46.23197 -lon -74.72963)
        ) 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [double][alias("lat")]$Latitude,
        [double][alias("long")]$Longitude
    )
    Process {
        return [PSCustomObject]@{            
            Latitude = $Latitude
            Longitude = $Longitude
            }
    }
}

# -----------------------------------------------------------------------------
Function New-SCLprPolygon {
    <#
    .Synopsis
        Method used to create a new polygon to a permit, permit rule or overtime rule.
    .DESCRIPTION
        This Method will allow the user to create a new polygon to a specified entity.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter EntityId will be used to specify the permit, permit rule or overtime rule id.

        The parameter Polygon represents the object of the polygon. See example for details.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new polygon
        $polygon = [PSCustomObject]@{
            Capacity = 5
            Name = "New Zone From PS"
            Points = @([PSCustomObject]@{
                Longitude = -98.01514
                Latitude = 54.41344
            }, [PSCustomObject]@{
                Longitude = -71.62063
                Latitude = 51.4959
            }, [PSCustomObject]@{
                Longitude = -74.72963
                Latitude = 46.23197
            })
        }
        $polygonId = New-SCLprPolygon -EntityId $myPermitRule.Id -Polygon $polygon
        $polygon = Get-SCLprPolygon -EntityId $myPermitRule.Id -PolygonId $polygonId              

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $EntityId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("poly")] $Polygon
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
        $id = GetIdFromObject $EntityId
        $jsonBody = $Polygon | ConvertTo-Json 
    
        InvokeSCRestMethod -Method 'POST' -UriSuffix "Entities/$id/Polygons" -Body $jsonBody
    }
    }    
}
# -----------------------------------------------------------------------------
Function Get-SCLprPolygons {
    <#
    .Synopsis
        Method used to get the list of all polygons of a permit, permit rule or overtime rule.
    .DESCRIPTION
        This Method will allow the user to get the list of polygon or a specified permit, permit rule or overtime rule id.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter EntityId will be used to specify the permit, permit rule or overtime rule id.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $polygon = Get-SCLprPolygons -EntityId $myPermitRule.Id 

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $EntityId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
        $id = GetIdFromObject $EntityId
    
        InvokeSCRestMethod -Method 'GET' -UriSuffix "Entities/$id/Polygons" 
    }
    }    
}
# -----------------------------------------------------------------------------
Function Get-SCLprPolygon {
    <#
    .Synopsis
        Method used to get a specified polygons from a permit, permit rule or overtime rule.
    .DESCRIPTION
        This Method will allow the user to get a specified polygon from a specified permit, permit rule or overtime rule id.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter EntityId will be used to specify the permit, permit rule or overtime rule id.

        The parameter PolygonId will be used to specify the polygon id.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $polygon = Get-SCLprPolygon -EntityId $myPermitRule.Id -PolygonId $polygonId

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $EntityId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PolygonId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
        $id = GetIdFromObject $EntityId
        $pid = GetIdFromObject $PolygonId
    
        InvokeSCRestMethod -Method 'GET' -UriSuffix "Entities/$id/Polygons/$pid" 
    }
    }    
}
# -----------------------------------------------------------------------------
Function Remove-SCLprPolygon {
    <#
    .Synopsis
        Method used to remove a specified polygons from a permit, permit rule or overtime rule.
    .DESCRIPTION
        This Method will allow the user to remove a specified polygon from a specified permit, permit rule or overtime rule id.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter EntityId will be used to specify the permit, permit rule or overtime rule id.

        The parameter PolygonId will be used to specify the polygon id.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Remove-SCLprPolygon -EntityId $myPermitRule.Id -PolygonId $polygonId

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $EntityId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("pid")] $PolygonId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
        $id = GetIdFromObject $EntityId
        $pid = GetIdFromObject $PolygonId
    
        InvokeSCRestMethod -Method 'DELETE' -UriSuffix "Entities/$id/Polygons/$pid" 
    }
    }    
}
# -----------------------------------------------------------------------------
Function Set-SCLprPolygon {
    <#
    .Synopsis
        Method used to update a specified polygon from a permit, permit rule or overtime rule.
    .DESCRIPTION
        This Method will allow the user to update a specified polygon from a specified permit, permit rule or overtime rule id. The object to 
        update can be partial but the list properties must be full. See example for details.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter EntityId will be used to specify the permit, permit rule or overtime rule id.

        The parameter Polygon represents the object of the polygon. See example for details.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Update a polygon
        $polygon = [PSCustomObject]@{
            Id = "00000000-0000-0000-1111-000000000000"
            Capacity = 50
            Name = "Modified Zone"
            Points = @([PSCustomObject]@{
                Longitude = -98.01514
                Latitude = 54.41344
            }, [PSCustomObject]@{
                Longitude = -71.62063
                Latitude = 51.4959
            }, [PSCustomObject]@{
                Longitude = -74.72963
                Latitude = 46.23197
            })
        }
        $polygonId = Set-SCLprPolygon -EntityId $myPermitRule.Id -Polygon $polygon
        $polygon = Get-SCLprPolygon -EntityId $myPermitRule.Id -PolygonId $polygonId              

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $EntityId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("poly")] $Polygon
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
        $id = GetIdFromObject $EntityId
        $p = GetIdFromObject $Polygon
        $jsonBody = $Polygon | ConvertTo-Json 
    
        InvokeSCRestMethod -Method 'PUT' -UriSuffix "Entities/$id/Polygons/$pid" -Body $jsonBody
    }
    }    
}
# -----------------------------------------------------------------------------
Function Search-SCLprReads {
    <#
    .Synopsis
        Method used to query for LPR Reads.
    .DESCRIPTION
        This Method will allow the user to query all LPR Manager in the security center directory for LPR Reads.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Start will be used to specify the start date from which we want the reads

        The parameter End will be used to specify the end date to which we want the reads

        The parameter Units represents a list of Lpr Units Ids that are comma separated.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $allReads = Search-SCLprReads

        Write-Host $allReads.Results

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [DateTime][parameter(Mandatory=$false,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("s")] $Start,
        [DateTime][parameter(Mandatory=$false,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("e")] $End,
        [parameter(Mandatory=$false,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("u")] $Units
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
        
        $queryUri = "Reports/Reads"
        $queryParams = new-object System.Collections.Specialized.NameValueCollection

        if($Start -And $End ){
            $queryParams['timestart'] = $Start.ToString('o')
            $queryParams['timeend'] = $End.ToString('o')            
        }
        if($Units){ $queryParams['units'] = $Units }
       
        For ($i=0; $i -lt $queryParams.Count; $i++)  {
            if($i -eq 0){ $queryUri += '?' }
            else{ $queryUri += '&'}
            $queryUri += $queryParams.Keys[$i] + '=' + $queryParams[$i]
        }

        InvokeSCRestMethod -Method 'GET' -UriSuffix $queryUri
        }
    }    
}
# -----------------------------------------------------------------------------
# Internal function  
Function Get-SCRandomLicensePlate {
[CmdletBinding()]
    param (
        [System.Random] $rand,
        [char[]] $sourcedata
    )
    Begin {
    }
    Process {         
        SCCmdletImplementation $MyInvocation.InvocationName {   
            $length = $rand.Next(5,8)            
            $sb = New-Object -TypeName "System.Text.StringBuilder"; 
            For ($loop=1; $loop -le $length; $loop++) {
                [void]$sb.Append($sourcedata[$rand.Next(0, $sourcedata.Length)])
            }      
            $sb.ToString()                 
        } 
    }
}

Function New-SCPlatelistFile {
    <#
    .Synopsis
        Method used to generate platelist file.
    .DESCRIPTION
        This Method will allow the user to construct a file on disk representing a platelist containing random data.
        
        The parameter Path represent the file path where the platelist will be written.
        The parameter Count represent the number of item that should be generated.
        The parameter Category represent the category of each item that are generated. (Optional)
        The parameter StateList represent an array of string containing plate state. (Optional)
        The parameter EffectiveDate represent the start date for each item that are generated. (Optional)
        The parameter ExpiryDate represent the end date for each item that are generated. (Optional)
        The parameter PermitID represent the permit ID for each item that are generated. (Leave this blank if creating hotlists) (Optional)

    .EXAMPLE
        #
        New-SCPlatelistFile -Path "D:\hotlist\test123.txt" -Count 10000
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [string][parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("p")] $Path,
        [int][parameter(Mandatory=$true,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("c")] $Count,
        [string][parameter(Mandatory=$false,Position=3,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("cat")]$Category="MyCategory",
        [string[]][parameter(Mandatory=$false,Position=4,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("state")]$StateList=('QC','TX', 'NY'),
        [string][parameter(Mandatory=$false,Position=5,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("efdate")]$EffectiveDate="2016-01-01",
        [string][parameter(Mandatory=$false,Position=6,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("exdate")]$ExpiryDate="2030-12-31",
        [string][parameter(Mandatory=$false,Position=7,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("PID")]$PermitID=""
    )
    Begin {
    }
    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Write-Host "Generating a platelist file of $Count entries at $Path..."
            $rand = New-Object System.Random
            $sourcedata = [char[]] ([char]'0'..[char]'9' + [char]'A'..[char]'Z' )
            $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
            $progress = 0
            $stream = [System.IO.StreamWriter] $Path

            For ($i=0;$i -le $count;$i++){                
                $plate = Get-SCRandomLicensePlate -rand $rand -sourcedata $sourcedata
                $state = $StateList[$rand.Next(0,$StateList.Count)]

                # Only include permit Id with its delimeter if it is specified (in case of creating permits). Else leave blank for hotlist creation
                if($PermitID -ne ""){
                    $stream.WriteLine("$Category;$state;$plate;$EffectiveDate;$ExpiryDate;$PermitID")
                }else{
                    $stream.WriteLine("$Category;$state;$plate;$EffectiveDate;$ExpiryDate")
                }

                if($i % 100){
                    Write-Progress -activity "Writing file..." -status "Generating entries... " -PercentComplete (($i / $count) * 100.0)
                }
            }
            $stream.close()
            $stopwatch.Stop()
            $elapsed = $stopwatch.Elapsed
            Write-Host "Completed in $elapsed sec."
        }
    }
}
