﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias asadg Add-SCActiveDirectoryGroup
Function Add-SCActiveDirectoryGroup {
    <#
    .Synopsis
        This method will add a new Active Directory group to the SecurityCenter system.

    .DESCRIPTION
        This method is used to add an Active Directory group to the system.

        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the Active Directory on which the group should be added.
        You can also pass any Active Directory role object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myAD = Get-SCRoles -Type ActiveDirectory | where { $_.Name -eq "MyActiveDirectory"}
        Add-SCActiveDirectoryGroup $myAD.Id -GroupAccountGuid "{2d97cd19-d565-4f38-9166-accbfff5aea4}" -GroupAccountName "Access Control Dev" -SyncUserGroup -SyncUsers

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        Remove-SCActiveDirectoryGroups
        Get-SCActiveDirectoryGroups
        Invoke-SCActiveDirectorySync
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] $GroupAccountGuid,
        [parameter(Mandatory=$true,Position=2)] $GroupAccountName,
        [switch]$SyncCardholders,
        [switch]$SyncCredentials,
        [switch]$SyncUserGroup,
        [switch]$SyncUsers
    )

    Begin {
    }

    Process {

        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/ImportedGroups"

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("GroupAccountGuid", $GroupAccountGuid)
            $jsonObject.Add("GroupAccountName", $GroupAccountName)
            $jsonObject.Add("SynchronizeCardholders", $SyncCardholders.IsPresent)
            $jsonObject.Add("SynchronizeCredentials", $SyncCredentials.IsPresent)
            $jsonObject.Add("SynchronizeUserGroup", $SyncUserGroup.IsPresent)
            $jsonObject.Add("SynchronizeUsers", $SyncUsers.IsPresent)
            $jsonBody = $jsonObject | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "Post" -Body $jsonBody 
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias dbsad Debug-SCActiveDirectory
Function Debug-SCActiveDirectory {
    <#
    .Synopsis
        This will launch the diagnose command for the selected active directory role.
    .DESCRIPTION
        This method will launch the diagnose command for the given active directory role.  This method is used
        when you want to troubleshoot a given active directory role.

        The ActiveDirectoryId parameter represents the Id of the active directory role to diagnose (The guid representing the role in the Security Center System)
        You can also pass any active directory role object that contains an ID as a parameter.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Will retreive the active directory role
        $adRole = Get-SCRoles -t ActiveDirectory
        
        Debug-SCActiveDirectory -ActiveDirectoryId $adRole

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("Id")] $ActiveDirectoryId
    )
    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $ActiveDirectoryId -RelationName "diagnostics" 
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias gsadg Get-SCActiveDirectoryGroups
Function Get-SCActiveDirectoryGroups {
    <#
    .Synopsis
        This method will get the list of Active Directory groups already imported in SecurityCenter.

    .DESCRIPTION
        This method is used to get the list of Active Directory groups already imported in SecurityCenter.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the Active Directory on which the imported groups is read.
        You can also pass any Active Directory role object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myAD = Get-SCRoles -Type ActiveDirectory | where { $_.Name -eq "MyActiveDirectory"}
        Get-SCActiveDirectoryGroups $myAD.Id 

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        Add-SCActiveDirectoryGroup
        Remove-SCActiveDirectoryGroup
        Invoke-SCActiveDirectorySync        
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )

    Begin {
    }

    Process {

        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/ImportedGroups"

            InvokeSCRestMethod -UriSuffix $uri -Method "Get" 
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias isads Invoke-SCActiveDirectorySync
Function Invoke-SCActiveDirectorySync {
    <#
    .Synopsis
        This method will trigger an Active Directory synchronisation based on the list of Active Directory imported groups.

    .DESCRIPTION
        This method is used to synchronize the groups from the Active Directory imported groups. 
        Method EnterSC-Session must be called prior to calling this method to specify
        server connection parameters and build the parameter caches for auto-complete. 

        The RoleId parameter represents the Id of the Active Directory on which the synchronization is requested.
        You can also pass any Active Directory role object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myAD = Get-SCRoles -Type ActiveDirectory | where { $_.Name -eq "MyActiveDirectory"} | Invoke-SCActiveDirectorySync

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        Get-SCActiveDirectoryGroups
        Add-SCActiveDirectoryGroup
        Remove-SCActiveDirectoryGroup
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId
    )

    Begin {
    }

    Process {

        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/TriggerSynchronization"
            InvokeSCRestMethod -UriSuffix $uri -Method "Post" -Body (@{} | ConvertTo-Json)
        }
    }   
}

# -----------------------------------------------------------------------------
Set-Alias rsadg Remove-SCActiveDirectoryGroup
Function Remove-SCActiveDirectoryGroup {
    <#
    .Synopsis
        This method will remove the Active Directory group from the Security Center imported groups

    .DESCRIPTION
        This method is used to remove the Active Directory group from the Security Center imported groups.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete 

        The RoleId parameter represents the Id of the Active Direcory from which the Active Directory group is removed
        You can also pass any Active Directory role object that contains an ID as a parameter.

    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $myAD = Get-SCRoles -Type ActiveDirectory | where { $_.Name -eq "MyActiveDirectory"}
        Remove-SCActiveDirectoryGroup -RoleId $myAD.Id -GroupAccountGuid "{2d97cd19-d565-4f38-9166-accbfff5aea4}"

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RoleId,
        [parameter(Mandatory=$true,Position=1)] $GroupAccountGuid
    )

    Begin {
    }

    Process {

        SCCmdletImplementation $MyInvocation.InvocationName {
            $rid = GetIdFromObject $RoleId
            $uri = "Entities/$rid/ImportedGroups/$GroupAccountGuid"

            InvokeSCRestMethod -UriSuffix $uri -Method "Delete" -Body (@{} | ConvertTo-Json)
        }
    }  
}

# -----------------------------------------------------------------------------
Set-Alias ssadp Show-SCActiveDirectoryProperties
Function Show-SCActiveDirectoryProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of an active directory role.
    .DESCRIPTION
        This method will list the supported properties and relation of an active directory role (the data model, not the actual data).  This method is used
        when you want to know what is available for a given active directory role.
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCActiveDirectoryProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiActiveDirectoryRole" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }    
}

Export-ModuleMember -Function '*-*' -Alias '*'
