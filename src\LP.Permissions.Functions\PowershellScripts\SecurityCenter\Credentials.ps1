﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias gscr Get-SCCredentials
Function Get-SCCredentials {
    <#
    .Synopsis
        This method will return all the properties of the credential represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the credential represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CredentialId parameter represents the Id of the credential to retrieve (The guid representing the credential in the Security Center System)
        You can also pass any credential object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cred = New-SCCredentials -n "MyNewCredentials"

        Get-SCCredentials -CredentialId $cred.Id

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ch = New-SCCredentials -n "MyNewCredentials" | Get-SCCredentials

        #Exit the session
        Exit-SCSession
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $ch = nscr -n "MyNewCredentials" | gscr

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CredentialId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $CredentialId           
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias nsc New-SCCredentialsEx
Function New-SCCredentialsEx {
    <#
    .Synopsis
        Method used to create a new credential with a valid card format
    .DESCRIPTION
        This Method will allow the user to create a new credential with a valid card format
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to new access rule upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $cfEnum = Get-SCEnum "CardFormats"
        $cardFormat = $cfEnum | Where-Object { $_.Description -eq "Standard 26 bits" }
        $credential = New-SCCredentialsEx -Name "Credential" -CardFormat $cardFormat.Id -FacilityCode 11 -CardNumber 101 -ErrorAction Stop  | Get-SCCredentials -ErrorAction Stop
        Exit-SCSession 

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name,
        [parameter(Mandatory=$true)] [alias("cf")]$CardFormat,
        [parameter(Mandatory=$true)] [alias("fc")]$FacilityCode,
        [parameter(Mandatory=$true)] [alias("cn")]$CardNumber
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uriSuffix = "Entities/" 

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("EntityType", "Credentials")
            $jsonObject.Add("Name", $Name)
            $jsonObject.Add("Format", $CardFormat)
            $jsonObject.Add("Facility", $FacilityCode)
            $jsonObject.Add("CardNumber", $CardNumber)
            
            $jsonBody = $jsonObject | ConvertTo-Json
        
            InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'POST' -Body $jsonBody
        }
    } 
}

# -----------------------------------------------------------------------------
Set-Alias nscr New-SCCredentials
Function New-SCCredentials {
    <#
    .Synopsis
        Method used to create a new credential with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new credential with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new credential upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $cred = New-SCCredentials -n "MyNewCredential"

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t Credentials
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias nscpr New-SCPinCredentials
Function New-SCPinCredentials {
	<#
    .Synopsis
        Method used to create a new PIN credential with the provided name and PIN Code
    .DESCRIPTION
        This Method will allow the user to create a new PIN credential with the provided name and PIN Code
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new credential upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $cred = New-SCPinCredentials -n "MyNewPinCredential" -PinCode 1234

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
	
[CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name,
        [parameter(Mandatory=$true)] [alias("pc")]$PinCode
    )

    begin {
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uriSuffix = "Entities/" 

            $jsonObject = [ordered]@{} 
            $jsonObject.Add("EntityType", "Credentials")
            $jsonObject.Add("Name", $Name)
            $jsonObject.Add("CredentialType", "Keypad")
            $jsonObject.Add("KeyPad", $PinCode)
            
            $jsonBody = $jsonObject | ConvertTo-Json
        
            InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'POST' -Body $jsonBody
        }
    }	
}

# -----------------------------------------------------------------------------
Set-Alias rscr Remove-SCCredentials
Function Remove-SCCredentials {
    <#
    .Synopsis
        Will remove the credential represented by the provided CredentialId parameter from Security Center
    .DESCRIPTION
        This method will permanently remove the specified credential from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The CredentialId parameter represents the credential to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $cred = New-SCCredentials -n "MyNewCredential"

        Remove-SCCredentials -CredentialId $cred.Id

        Exit-SCSession 
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $CredentialId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $CredentialId
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias sscr Set-SCCredentials
Function Set-SCCredentials() {
    <#
    .Synopsis
        Used to update the properties of a credential in Security Center
    .DESCRIPTION
        This method is used to update the properties of a credential to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter Credential represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newcred = New-SCCredentials -Name "Mych"  | gscr
        $newch = New-SCCardholder -Name "Mych"  | gsch
        $newcred.Cardholder = $newch.Id
        
        Set-SCCredentials -Credential $newcred

        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        #Create a new user
        $newcred = New-SCCredentials -Name "Mych"  | gscr
        $newch = New-SCCardholder -Name "Mych"  | gsch
        $newcred.Cardholder = $newch.Id
        
        sscr $newcred

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("cr")] $Credential
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Credential
        }
    }
}

# -----------------------------------------------------------------------------
Function Search-SCCredentialsAccessStatus {
    <#
    .Synopsis
        Searches security center Credentials and returns all credentials who's status matches the provided one
    .DESCRIPTION
        This method is used to return all cardholder who's status matches the provided one

        The parameter AccessStatus is the status of the credentials we want to search for

        The parameter Filter (optional) is used to specify the data returned by the method.  Base value will only return the minimum 
        data and the All Value will return all available data.  Base is the default value
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCCredentialsAccessStatus -AccessStatus Active -f All
                
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCCredentialsAccessStatus -as Active -f All
                
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
    )
 
    DynamicParam {
        $sess = GetSession -Quiet $true

        # Set the dynamic parameters' name
        $ParameterName1 = 'AccessStatus'
        $ParameterAlias1 = "as"
        $ParameterName2 = 'Filter'
        $ParameterAlias2 = "f"
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary
        
        # Create the collection of attributes
        $AttributeCollection1 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
        $AttributeCollection2 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]
            
        # Create and set the parameters' attributes
        $ParameterAttribute1 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute1.Mandatory = $true
        $ParameterAttribute1.Position = 1

        $ParameterAttribute2 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute2.Mandatory = $false
        $ParameterAttribute2.Position = 2

        # Add the attributes to the attributes collection
        $AttributeCollection1.Add($ParameterAttribute1)
        $AttributeCollection2.Add($ParameterAttribute2)

        # Generate and set the ValidateSet 
        if ($sess -ne $null) { 
            $arrSet1 = $sess.SCAccessStatus
            $ValidateSetAttribute1 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet1)

            $arrSet2 = $sess.SCFilterCache
            $ValidateSetAttribute2 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet2)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection1.Add($ValidateSetAttribute1)
            $AttributeCollection2.Add($ValidateSetAttribute2)
        }
        # don't validate if we can't retrieve
        
        #add the alias to the attributes collection
        $ParamAlias1 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias1
        $AttributeCollection1.Add($ParamAlias1)
        $ParamAlias2 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias2
        $AttributeCollection2.Add($ParamAlias2)

        # Create and return the dynamic parameter
        $RuntimeParameter1 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName1, [string], $AttributeCollection1)
        $RuntimeParameterDictionary.Add($ParameterName1, $RuntimeParameter1)

        $RuntimeParameter2 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName2, [string], $AttributeCollection2)
        $RuntimeParameterDictionary.Add($ParameterName2, $RuntimeParameter2)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $AccessStatus = $PsBoundParameters[$ParameterName1]
        $Filter = $PsBoundParameters[$ParameterName2]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uriSuffix = "Entities/Credentials?AccessStatus=" + $AccessStatus            

            if($Filter) {
                $uriSuffix = $uriSuffix + "&ValidFlags=" + $Filter
            }

            $result = InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'Get'

            if($IdOnly) {
                $result | foreach {Write-Output $_.Id}
            }
            else {
                return $result
            }
        }
    }
}

# -----------------------------------------------------------------------------
Function Search-SCCredentialsExpirationDate {
    <#
    .Synopsis
        Searches security center Credentials and returns all credentials who's expiration date falls in the provided range
    .DESCRIPTION
        This method is used to return all credentials who's expiration date falls in the provided range

        The parameter ExpirationStartTime is the start of the range to search for

        The parameter ExpirationEndTime is the end of the range to search for

        The parameter Filter (optional) is used to specify the data returned by the method.  Base value will only return the minimum 
        data and the All Value will return all available data.  Base is the default value
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCCredentialsExpirationDate -ExpirationStartTime (Get-Date).AddDays(-1) -ExpirationEndTime (Get-Date).AddDays(1) -f all
                
        Exit-SCSession
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        Search-SCCredentialsExpirationDate -est (Get-Date).AddDays(-1) -eet (Get-Date).AddDays(1) -f all
                
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    Param(
        [parameter(Mandatory=$false,Position=1,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("est")] [DateTime]$ExpirationStartTime,
        [parameter(Mandatory=$false,Position=2,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("eet")] [DateTime]$ExpirationEndTime
    )
 
    DynamicParam {
        $sess = GetSession -Quiet $true

        # Set the dynamic parameters' name
        $ParameterName2 = 'Filter'
        $ParameterAlias2 = "f"
            
        # Create the dictionary 
        $RuntimeParameterDictionary = New-Object System.Management.Automation.RuntimeDefinedParameterDictionary
        
        # Create the collection of attributes
        $AttributeCollection2 = New-Object System.Collections.ObjectModel.Collection[System.Attribute]

        $ParameterAttribute2 = New-Object System.Management.Automation.ParameterAttribute
        $ParameterAttribute2.Mandatory = $false
        $ParameterAttribute2.Position = 2

        # Add the attributes to the attributes collection
        $AttributeCollection2.Add($ParameterAttribute2)

        # Generate and set the ValidateSet 
        if ($sess -ne $null) { 
            $arrSet2 = $sess.SCFilterCache
            $ValidateSetAttribute2 = New-Object System.Management.Automation.ValidateSetAttribute($arrSet2)

            # Add the ValidateSet to the attributes collection
            $AttributeCollection2.Add($ValidateSetAttribute2)
        }
        # don't validate if we can't retrieve
        
        #add the alias to the attributes collection
        $ParamAlias2 = New-Object System.Management.Automation.AliasAttribute -ArgumentList $ParameterAlias2
        $AttributeCollection2.Add($ParamAlias2)

        # Create and return the dynamic parameter
        $RuntimeParameter2 = New-Object System.Management.Automation.RuntimeDefinedParameter($ParameterName2, [string], $AttributeCollection2)
        $RuntimeParameterDictionary.Add($ParameterName2, $RuntimeParameter2)

        return $RuntimeParameterDictionary
    }

    begin {
        # Bind the parameter to a friendly variable
        $Filter = $PsBoundParameters[$ParameterName2]
    }

    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $uriSuffix = "Entities/Credentials?ExpirationTimeStart=" + $ExpirationStartTime.ToString("o") + "&ExpirationTimeEnd=" + $ExpirationEndTime.ToString("o")        

            if($Filter) {
                $uriSuffix = $uriSuffix + "&ValidFlags=" + $Filter
            }

            $result = InvokeSCRestMethod -UriSuffix $uriSuffix -Method 'Get'

            if($IdOnly) {
                $result | foreach {Write-Output $_.Id}
            }
            else {
                return $result
            }
        }
    }
}

# -----------------------------------------------------------------------------
Function Show-SCCredentialActivityReport {
    <#
    .Synopsis
        Used to retrieve a credential activity report
    .DESCRIPTION
        This method will return a credential activity report for all the activities of the specified credential.  The report will contain a Results property
        that contains 2 arrays.  The first array will be the column definition.  This will contain the information this will be in each rows.
        The second array is the actual data of the report matching the column definition
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The credential parameter represents the Id of the credential to retrieve (The guid representing the credential in the Security Center System)
        You can also pass any credential object that contains an ID as a parameter
    .EXAMPLE
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 

        $ch = Search-SCCredentialsAccessStatus -AccessStatus Active

        Show-SCCredentialActivityReport $ch[0]

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $Credential
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $cid = GetIdFromObject $Credential   
            $uri = "Reports/CredentialActivities?Credentials="  + $cid
            InvokeSCRestMethod -UriSuffix $uri -Method 'GET'
        }
    }  
}

# -----------------------------------------------------------------------------
Function Show-SCCredentialProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a credential
    .DESCRIPTION
        This method will list the supported properties and relation of an credential (the data model, not the actual data).  This method is used
        when you want to know what is available for a given credential
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCCredentialProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiCredential" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'