
SET IDENTITY_INSERT [dbo].[UserLogin] ON 

INSERT [dbo].[UserLogin] ([Id], [Username], [Password], [IsActive]) VALUES (1, N'Admin', N'Admin', 0)
INSERT [dbo].[UserLogin] ([Id], [Username], [Password], [IsActive]) VALUES (2, N'sa', N'sa', 1)
SET IDENTITY_INSERT [dbo].[UserLogin] OFF
GO
INSERT [dbo].[Containers] ([container_id], [name], [geo_id], [description], [identifier]) VALUES (1, N'TJMaxx', 1, N'TJMaxx Banner', N'')
INSERT [dbo].[Containers] ([container_id], [name], [geo_id], [description], [identifier]) VALUES (2, N'HomeSense', 2, N'HomeSense Banner', N'')
GO
INSERT [dbo].[Geos] ([geo_id], [name], [description]) VALUES (1, N'US', N'United States')
INSERT [dbo].[Geos] ([geo_id], [name], [description]) VALUES (2, N'Canada', N'Canada')
GO
SET IDENTITY_INSERT [dbo].[States] ON 

INSERT [dbo].[States] ([state_id], [name], [description]) VALUES (1, N'Allowed', N'Allowed
')
INSERT [dbo].[States] ([state_id], [name], [description]) VALUES (2, N'Deny', N'Deny
')
INSERT [dbo].[States] ([state_id], [name], [description]) VALUES (3, N'Undefined', N'Undefined
')
SET IDENTITY_INSERT [dbo].[States] OFF
GO
INSERT [dbo].[Stores] ([store_id], [container_id], [server_name], [name]) VALUES (1, 1, N'TJM9617LPCS', N'TJM9617')
INSERT [dbo].[Stores] ([store_id], [container_id], [server_name], [name]) VALUES (2, 1, N'TJM9633LPCS', N'TJM9633')
GO
INSERT [dbo].[StoreUserGroups] ([store_user_group_id], [store_id], [user_group_id]) VALUES (1, 1, 2)
INSERT [dbo].[StoreUserGroups] ([store_user_group_id], [store_id], [user_group_id]) VALUES (2, 1, 3)
INSERT [dbo].[StoreUserGroups] ([store_user_group_id], [store_id], [user_group_id]) VALUES (3, 1, 5)
INSERT [dbo].[StoreUserGroups] ([store_user_group_id], [store_id], [user_group_id]) VALUES (4, 1, 6)
INSERT [dbo].[StoreUserGroups] ([store_user_group_id], [store_id], [user_group_id]) VALUES (5, 1, 7)
INSERT [dbo].[StoreUserGroups] ([store_user_group_id], [store_id], [user_group_id]) VALUES (6, 2, 2)
INSERT [dbo].[StoreUserGroups] ([store_user_group_id], [store_id], [user_group_id]) VALUES (7, 2, 3)
INSERT [dbo].[StoreUserGroups] ([store_user_group_id], [store_id], [user_group_id]) VALUES (8, 2, 5)
INSERT [dbo].[StoreUserGroups] ([store_user_group_id], [store_id], [user_group_id]) VALUES (9, 2, 6)
INSERT [dbo].[StoreUserGroups] ([store_user_group_id], [store_id], [user_group_id]) VALUES (10, 2, 7)
GO
INSERT [dbo].[UserGroups] ([user_group_id], [description], [parent_user_group_id], [name]) VALUES (1, N'MyNewUsergroup', NULL, N'MyNewUsergroup')
INSERT [dbo].[UserGroups] ([user_group_id], [description], [parent_user_group_id], [name]) VALUES (2, N'TJXG-GENETEC-LP-STORE-US', NULL, N'TJXG-GENETEC-LP-STORE-US')
INSERT [dbo].[UserGroups] ([user_group_id], [description], [parent_user_group_id], [name]) VALUES (3, N'TJXG-GENETEC-LP-STORE- Admin-US', NULL, N'TJXG-GENETEC-LP-STORE- Admin-US')
INSERT [dbo].[UserGroups] ([user_group_id], [description], [parent_user_group_id], [name]) VALUES (4, N'TJXG-GENETEC-LP-STORE- Admin-CA', NULL, N'TJXG-GENETEC-LP-STORE- Admin-CA')
INSERT [dbo].[UserGroups] ([user_group_id], [description], [parent_user_group_id], [name]) VALUES (5, N'TJXG-GENETEC-LP-LV1-Support', NULL, N'TJXG-GENETEC-LP-LV1-Support')
INSERT [dbo].[UserGroups] ([user_group_id], [description], [parent_user_group_id], [name]) VALUES (6, N'TJXG-GENETEC-LP-SDM-Support', NULL, N'TJXG-GENETEC-LP-SDM-Support')
INSERT [dbo].[UserGroups] ([user_group_id], [description], [parent_user_group_id], [name]) VALUES (7, N'TJXG-GENETEC-LP-LPSD-Support', NULL, N'TJXG-GENETEC-LP-LPSD-Support')
INSERT [dbo].[UserGroups] ([user_group_id], [description], [parent_user_group_id], [name]) VALUES (8, N'TJXG-GENETEC-LP-STORE-CA', NULL, N'TJXG-GENETEC-LP-STORE-CA')
GO