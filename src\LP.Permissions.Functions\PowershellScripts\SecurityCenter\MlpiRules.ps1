# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================
# -----------------------------------------------------------------------------
Function ConvertTo-SCMlpiRuleLocation() {
    <#
    .Synopsis
        Used to create a Location object in powershell session.
    .DESCRIPTION
        
        The parameter Id represent a read-only identifier of the location. It is generated by the system.
        
        The parameter Name represent the name of location path separated by '/'. Example: parkingName/Row #02

        The parameter SpaceCount represent the number of parking space available in that location.

    .EXAMPLE
        #
        
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [Guid]$Id,
        [string] $Name,
        [int]$SpaceCount
    )
    Process {
        return [PSCustomObject]@{
            Id = $Id
            Name = $Name
            SpaceCount = $SpaceCount
            }
    }
}
# -----------------------------------------------------------------------------
Function Get-SCMlpiRule {
    <#
    .Synopsis
        Method used to view a mlpi rule with the provided id
    .DESCRIPTION
        This Method will allow the user to view an mlpi rule with the provided id
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represent the specified mlpi rule identifier. 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new mlpi rule and get it.
        $myMlpiRule = New-SCMlpiRule -Name "MyMlpiRule" | Get-SCMlpiRule

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $RuleId           
        }
    }
}
# -----------------------------------------------------------------------------
Function New-SCMlpiRule {
    <#
    .Synopsis
        Method used to create a new mlpi rule with the provided name
    .DESCRIPTION
        This Method will allow the user to create a new mlpi rule with the provided name
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The name parameter will be given to the new mlpi rule upon creation 
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new mlpi rule and get it.
        $myMlpiRule = New-SCMlpiRule -Name "MyMlpiRule" | Get-SCMlpiRule

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("n")][string]$Name
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            New-SCEntity -n $Name -t MlpiRules
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCMlpiRule {
    <#
    .Synopsis
        Will remove the mlpi rule represented by the provided RuleId parameter from Security Center
    .DESCRIPTION
        This method will permanently remove the specified mlpi rule from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the mlpi rule to remove from Security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
      
        Remove-SCMlpiRule -RuleId $mlpiruleid

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RuleId
        )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Remove-SCEntity -EntityId $RuleId
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCMlpiRule() {
    <#
    .Synopsis
        Used to update the properties of a mlpi rule in Security Center
    .DESCRIPTION
        This method is used to update the properties of a mlpi rule to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter MlpiRule represents and contains the properties that will be updated to security Center
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new mlpi rule.
        $myMlpiRule = New-SCMlpiRule -Name "MyMlpiRule" | Get-SCMlpiRule

        # Update the entity.
        $myMlpiRule.Color = "ffffffff"
        Set-SCMlpiRule -MlpiRule $myMlpiRule 

        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("p")] $MlpiRule
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $MlpiRule
        }
    }
}
# -----------------------------------------------------------------------------
Function Show-SCMlpiRuleProperties {
    <#
    .Synopsis
        This will show all properties and possible relation of a mlpi rule
    .DESCRIPTION
        This method will list the supported properties and relation of an mlpi rule (the data model, not the actual data).  This method is used
        when you want to know what is available for a mlpi rule
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        Show-SCMlpiRuleProperties

        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    $uri = "Help/Entities/ApiMlpiRule" 

    SCCmdletImplementation $MyInvocation.InvocationName {
        $result = InvokeSCRestMethod -UriSuffix $uri -Method 'Get'

        if($result.Fields -and $result.Relations)
        {
            $result.Fields
            $result.Relations
        }
        else
        {
            $result
        }
    }
}
# -----------------------------------------------------------------------------
Function Get-SCMlpiRuleLocations {
    <#
    .Synopsis
        This method will return all the properties of all the mlpi rule locations represented by the RuleId
    .DESCRIPTION
        This method will return all the basic properties of the mlpi rule location contained in a MlpiRule represented by the RuleId.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the mlpi rule that contain the location to retrieve (The guid representing the mlpi rule in the Security Center System)
        You can also pass any mlpi rule object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $locations = Get-SCMlpiRuleLocations -RuleId $mymlpiRule.id       

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $RuleId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $RuleId
    
            InvokeSCRestMethod -Method 'GET' -UriSuffix "Entities/$id/Locations"      
        }
    }
}
# -----------------------------------------------------------------------------
Function Get-SCMlpiRuleLocation {
    <#
    .Synopsis
        This method will return all the properties of the mlpi rule location represented by the ID in a specified mlpi rule.
    .DESCRIPTION
        This method will return all the basic properties of the mlpi rule location represented by the ID contained inside a specified RuleId.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the mlpi rule that contain the location. (The guid representing the mlpi rule in the Security Center System)
        You can also pass any mlpi rule object that contains an ID as a parameter

        The LocationId parameter represents the Id of the mlpi rule location to retrieve (The int representing the mlpi rule location in the Security Center System)
        You can also pass any mlpi rule location object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $locations = Get-SCMlpiRuleLocation -RuleId $mymlpiRule.id -LocationId $locationId      

        #Exit the session
        Exit-SCSession
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RuleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("lid")] $LocationId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $RuleId
            $lid = GetIdFromObject $LocationId
    
            InvokeSCRestMethod -Method 'GET' -UriSuffix "Entities/$id/Locations/$lid"      
        }
    }
}
# -----------------------------------------------------------------------------
Function Set-SCMlpiRuleLocation() {
    <#
    .Synopsis
        Used to update the properties of a mlpi rule location in Security Center
    .DESCRIPTION
        This method is used to update the properties of a mlpi rule location to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the mlpi rule that contain the location. (The guid representing the mlpi rule in the Security Center System)
        You can also pass any mlpi rule object that contains an ID as a parameter

        The Location parameter represents the mlpi rule location to update.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        $location = [PSCustomObject]@{
            Id= 12334556
            Name = "new name"
            SpaceCount = 50
            }
        $locationId = Set-SCMlpiRuleLocation -MlpiRuleId $myMlpiRule.Id -Location $location

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("id")] $RuleId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("l")] $Location
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $id = GetIdFromObject $RuleId
            $lid = GetIdFromObject $Location
            $jsonBody = $Location | ConvertTo-Json 
    
            InvokeSCRestMethod -Method 'PUT' -UriSuffix "Entities/$id/Locations/$vid" -Body $jsonBody
        }
    }
}
# -----------------------------------------------------------------------------
Function New-SCMlpiRuleLocation() {
    <#
    .Synopsis
         Method used to create a new mlpi rule location with the provided object
    .DESCRIPTION
        This method is used to create the object in security center and to fill the properties.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated separately by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the mlpi rule that contain the location. (The guid representing the mlpi rule in the Security Center System)
        You can also pass any mlpi rule object that contains an ID as a parameter

        The Location parameter represents the mlpi rule location to create.
        
    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
        
        # Create a new mlpi rule location
        $location = [PSCustomObject]@{
            Id= 12334556
            Name = "initial name"
            SpaceCount = 50
            }
        $locationId = New-SCMlpiRuleLocation -MlpiRuleId $myMlpiRule.Id -Location $location

        Exit-SCSession   
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("id")] $RuleId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("l")] $Location
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            $id = GetIdFromObject $RuleId
            $jsonBody = $Location | ConvertTo-Json 
    
            InvokeSCRestMethod -Method 'POST' -UriSuffix "Entities/$id/Locations" -Body $jsonBody
        }
    }
}
# -----------------------------------------------------------------------------
Function Remove-SCMlpiRuleLocation {
    <#
    .Synopsis
        Will remove the mlpi rule location represented by the LocationId in a specific Mlpi Rule.
    .DESCRIPTION
        This method will permanently remove the specified mlpi rule location from Security Center.
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The RuleId parameter represents the Id of the mlpi rule that contain the location. (The guid representing the mlpi rule in the Security Center System)
        You can also pass any mlpi rule object that contains an ID as a parameter

        The LocationId parameter represents the mlpi rule location to remove.

    .EXAMPLE
        #
        #Enter into a Security Center Session
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "password" -GenetecServerPassword "password" 
      
        Remove-SCMlpiRuleLocation -RuleId $mlpiruleid -LocationId $myRestriction

        Exit-SCSession    
    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("id")] $RuleId,
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("lid")] $LocationId
    )

    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $id = GetIdFromObject $RuleId
            $lid = GetIdFromObject $LocationId
    
            InvokeSCRestMethod -Method 'DELETE' -UriSuffix "Entities/$id/Locations/$lid"      
        }
    }
}
# -----------------------------------------------------------------------------
Export-ModuleMember -Function '*-*' -Alias '*'