parameters:
  - name: name
  - name: environmentName
  - name: geo
  - name: resourcegroupName
  - name: condition
  - name: outputDir
  - name: buildConfiguration
  - name: serviceConnection

jobs:
  - deployment: ${{ parameters.geo }}_${{ parameters.environmentName }}_deployment
    displayName: "Deploy: ${{ parameters.geo }}/${{ parameters.environmentName }}"
    pool:
      vmImage: $(vmImageName)
    condition: ${{ parameters.condition }}
    environment: ${{ parameters.environmentName }}
    strategy:
      runOnce:
        deploy:
          steps:
            # - task: DownloadBuildArtifacts@1
            #   inputs:
            #     buildType: "current"
            #     downloadType: "single"
            #     artifactName: "drop"
            #     downloadPath: "${{ parameters.outputDir }}"
            # - script: |
            #     echo  OutputDir: ${{ parameters.outputDir }}
            #     dir ../drop/Release
            #   displayName: List Output Directory

            - task: AzureCLI@2
              displayName: Run deploy.sh script
              inputs:
                failOnStandardError: false
                azureSubscription: ${{ parameters.serviceConnection }}
                scriptType: "bash"
                scriptLocation: "scriptPath"      
                scriptPath: "../source/src/Backend/deployment/cicd/deploy.sh"
                arguments: "--name ${{ parameters.name }}  --zip ../app/LPPermission.UI.API.zip   --env ${{ parameters.environmentName }} --geo ${{ parameters.geo }}  --rg ${{ parameters.resourcegroupName }} --conn ${{ parameters.serviceConnection }}"
