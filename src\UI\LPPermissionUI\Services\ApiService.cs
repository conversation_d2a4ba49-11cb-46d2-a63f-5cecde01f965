using LPPermission.UI.Services.Interface;
using System.Net.Http.Headers;

namespace LPPermission.UI.Services
{
    /// <summary>
    /// Service for making API calls with support for JWT-based authorization.
    /// </summary>
    public class ApiService : IApiService
    {
        // HttpClient for making HTTP requests
        private readonly HttpClient _httpClient;

        // Service for retrieving JWT tokens from secure storage
        private readonly JwtTokenStorageService _jwtTokenStorageService;

        // Base URI for the API, configured in appsettings.json
        private readonly string _baseUri;

        /// <summary>
        /// Constructor to initialize the ApiService with dependencies.
        /// </summary>
        /// <param name="httpClient">HttpClient instance for making HTTP requests.</param>
        /// <param name="jwtTokenStorageService">Service for managing JWT tokens.</param>
        /// <param name="configuration">Configuration for retrieving API base URI.</param>
        public ApiService(HttpClient httpClient, JwtTokenStorageService jwtTokenStorageService, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _jwtTokenStorageService = jwtTokenStorageService;
            _baseUri = configuration["ApiBaseUri"];
        }

        /// <summary>
        /// Adds the Authorization header with a Bearer token to the HTTP client.
        /// </summary>
        private async Task AddAuthorizationHeaderAsync()
        {
            var token = await _jwtTokenStorageService.GetTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }
        }

        /// <summary>
        /// Sends a POST request to the specified API endpoint with the provided content.
        /// </summary>
        /// <typeparam name="T">The type of the content to send in the request body.</typeparam>
        /// <param name="requestUri">The relative URI of the API endpoint.</param>
        /// <param name="content">The content to send in the request body.</param>
        /// <returns>The HTTP response message from the API.</returns>
        public async Task<HttpResponseMessage> PostAsync<T>(string requestUri, T content)
        {
            await AddAuthorizationHeaderAsync();
            return await _httpClient.PostAsJsonAsync($"{_baseUri}/{requestUri}", content);
        }

        /// <summary>
        /// Sends a GET request to the specified API endpoint and retrieves a list of employees.
        /// </summary>
        /// <param name="requestUri">The relative URI of the API endpoint.</param>
        /// <returns>A list of employees retrieved from the API.</returns>
        public async Task<List<Employee>> GetAsync(string requestUri)
        {
            await AddAuthorizationHeaderAsync();
            return await _httpClient.GetFromJsonAsync<List<Employee>>($"{_baseUri}/{requestUri}");
        }
    }
}