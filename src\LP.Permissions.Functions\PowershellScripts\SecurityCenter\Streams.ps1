﻿# ==========================================================================
# Copyright (C) 1989-2017 by Genetec, Inc.
# All rights reserved.
# ==========================================================================

# -----------------------------------------------------------------------------
Set-Alias gsstr Get-SCStream
Function Get-SCStream {
    <#
    .Synopsis
        This method will return all the properties of the stream represented by the ID
    .DESCRIPTION
        This method will return all the basic properties of the stream represented by the ID.  For object properties that represent a relation
        with an other entity, use the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The StreamId parameter represents the Id of the stream to retreive (The guid representing the stream in the Security Center System)
        You can also pass any stream object that contains an ID as a parameter
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        $stream = Get-SCStream -StreamId $cams[0].Id

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true,Position=0,ValueFromPipelineByPropertyName=$true, ValueFromPipeline=$true)] [alias("Id")] $StreamId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $streamId   
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias ssstr Set-SCStream
Function Set-SCStream() {
    <#
    .Synopsis
        Used to update the properties of an stream in Security Center
    .DESCRIPTION
        This method is used to update the properties of a stream to Security Center.  All properties that are not read-only will be update.
        Entity Relations will NOT be updated by this method call.  Those properties must be updated seperatly by using the specific methods
        Method EnterSC-Session must be called prior to calling this method to specify server connection parameters and build
        the parameter caches for auto-complete

        The parameter stream represents and contains the properties that will be updated to security Center
    .EXAMPLE
        # Must enter a valid Security Center session before calling any method
        Enter-SCSession -ComputerName 127.0.0.1 -User "admin" -DirectoryPassword "" -GenetecServerPassword ""

        $cams = Search-SCEntities -Name "My camera" -Type Cameras -Filter 'All'

        $stream = Get-SCStream -StreamId $cams[0].Id
        $stream.MulticastAddress = *********

        #update config in SecurityCenter
        Set-SCStream -Stream $stream

        #Exit the session
        Exit-SCSession

    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>
    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("s")] $Stream
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName { 
            Set-SCEntity -EntityToSet $Stream
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias gssc Get-SCStreamCompression
Function Get-SCStreamCompressions {
    <#
    .Synopsis

    .DESCRIPTION

    .EXAMPLE

    .EXAMPLE


    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("id")] $StreamId
    )
    Begin {
    }

    Process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            Get-SCEntity -EntityId $StreamId -RelationName "Compressions"
        }
    }
}

# -----------------------------------------------------------------------------
Set-Alias sssc Set-SCStreamCompression
Function Set-SCStreamCompression {
    <#
    .Synopsis

    .DESCRIPTION

    .EXAMPLE

    .EXAMPLE


    .NOTES
        Written by Genetec Inc.
    .LINK
        https://gtapforum.genetec.com/
    #>

    [CmdletBinding()]
    param (
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("id")] $StreamId,
        [parameter(Mandatory=$true, ValueFromPipeline=$true)] [alias("comp")]  $Compression
    )
    begin {
    }
    process {
        SCCmdletImplementation $MyInvocation.InvocationName {
            $comId = $Compression.Id
            $sid  = GetIdFromObject $StreamId
            
            $uri = "Entities/$sid/Compressions/$comId"

            $jsonBody = $Compression | ConvertTo-Json
            InvokeSCRestMethod -UriSuffix $uri -Method "PUT" -Body $jsonBody
        }
    }
}

Export-ModuleMember -Function '*-*' -Alias '*'