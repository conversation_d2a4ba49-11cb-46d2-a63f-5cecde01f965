﻿using System.Net.Http;
using System.Threading.Tasks;

namespace LPPermission.UI.Services
{
    /// <summary>
    /// Service for interacting with the orchestrator API to start workflows.
    /// </summary>
    public class OrchestratorService
    {
        // HttpClient for making HTTP requests
        private readonly HttpClient _httpClient;

        /// <summary>
        /// Constructor to initialize the OrchestratorService with an HttpClient.
        /// </summary>
        /// <param name="httpClient">HttpClient instance for making HTTP requests.</param>
        public OrchestratorService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        /// <summary>
        /// Starts the orchestrator by sending a POST request to the API.
        /// </summary>
        /// <returns>The response content as a string.</returns>
        public async Task<string> StartOrchestratorAsync()
        {
            // Send a POST request to the orchestrator's HTTP start endpoint
            var response = await _httpClient.PostAsync("http://localhost:7010/api/StoreOrchestrator_HttpStart", null);

            // Ensure the response indicates success; throw an exception if not
            response.EnsureSuccessStatusCode();

            // Return the response content as a string
            return await response.Content.ReadAsStringAsync();
        }
    }
}